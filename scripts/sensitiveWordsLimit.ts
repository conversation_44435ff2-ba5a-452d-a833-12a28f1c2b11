interface RuntimeEnv {
  features?: {
    upload_sensitive_words?: string;
  };
  [key: string]: any;
}

function sensitiveWordsLimit(envsArray: RuntimeEnv[]): string | undefined {
  if (!Array.isArray(envsArray)) {
    console.warn('envs 不是数组类型');
    return undefined;
  }
  // 遍历所有环境变量对象
  for (let i = 0; i < envsArray.length; i++) {
    const env = envsArray[i];
    // 检查是否存在 features 对象
    if (env && env.features && typeof env.features === 'object') {
      // 检查 features 对象中是否有 upload_sensitive_words 属性
      if (env.features.upload_sensitive_words !== undefined) {
        console.log(`在 envs[${i}].features 中找到 upload_sensitive_words:`, env.features.upload_sensitive_words);
        return env.features.upload_sensitive_words;
      }
    }
  }
  console.warn('在 envs 中未找到 upload_sensitive_words 属性');
  return undefined;
}

// 将类型定义导出供其他文件使用
export { RuntimeEnv, sensitiveWordsLimit };

// 类型声明
declare global {
  interface Window {
    upload_sensitive_words: string | undefined;
  }
}
