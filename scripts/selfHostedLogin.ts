interface RuntimeEnv {
  features?: {
    SELF_HOSTED_CUSTOM_LOGIN?: string;
  };
  [key: string]: any;
}

function findSelfHostedCustomLogin(envsArray: RuntimeEnv[]): string | undefined {
  if (!Array.isArray(envsArray)) {
    console.warn('envs 不是数组类型');
    return undefined;
  }
  // 遍历所有环境变量对象
  for (let i = 0; i < envsArray.length; i++) {
    const env = envsArray[i];
    // 检查是否存在 features 对象
    if (env && env.features && typeof env.features === 'object') {
      // 检查 features 对象中是否有 SELF_HOSTED_CUSTOM_LOGIN 属性
      if (env.features.SELF_HOSTED_CUSTOM_LOGIN !== undefined) {
        console.log(`在 envs[${i}].features 中找到 SELF_HOSTED_CUSTOM_LOGIN:`, env.features.SELF_HOSTED_CUSTOM_LOGIN);
        return env.features.SELF_HOSTED_CUSTOM_LOGIN;
      }
    }
  }
  console.warn('在 envs 中未找到 SELF_HOSTED_CUSTOM_LOGIN 属性');
  return undefined;
}

// 将类型定义导出供其他文件使用
export { findSelfHostedCustomLogin, RuntimeEnv };

// 类型声明
declare global {
  interface Window {
    SELF_HOSTED_CUSTOM_LOGIN: string | undefined;
  }
}
