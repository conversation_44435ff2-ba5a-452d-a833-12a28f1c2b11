# DropdownItems 筛选功能示例

## 修改说明

我们已经成功修改了 `useRoleItem` hook，使其能够根据传入的 `data` 数据筛选 `DropdownItems`。

## 工作原理

### 原始行为
之前，`DropdownItems` 总是显示所有4个选项：
- 文件管理者 (value: 99)
- 有编辑权限的协作者 (value: 60)
- 有评论权限的协作者 (value: 40)
- 有阅读权限的协作者 (value: 20)

### 新行为
现在，`DropdownItems` 会根据 `data` 数组中的 `targetId` 进行筛选：

```typescript
// 示例数据
const data: RolePermission[] = [
  { targetId: 60, allow: true, inherited: false },
  { targetId: 40, allow: false, inherited: false }
];

// 结果：只显示 value 为 60 和 40 的选项
// - 有编辑权限的协作者 (value: 60)
// - 有评论权限的协作者 (value: 40)
```

## 使用场景

### 场景1：限制特定角色
```typescript
// 只允许编辑和评论权限
const data = [
  { targetId: 60, allow: true, inherited: false },  // 编辑权限
  { targetId: 40, allow: false, inherited: false }  // 评论权限
];
// 下拉菜单只显示这两个选项
```

### 场景2：只允许管理者权限
```typescript
// 只允许文件管理者
const data = [
  { targetId: 99, allow: true, inherited: false }
];
// 下拉菜单只显示文件管理者选项
```

### 场景3：无数据或空数据
```typescript
// 无数据或空数组
const data = [] || undefined;
// 下拉菜单显示所有4个选项（保持向后兼容）
```

## 技术实现

### 核心逻辑
```typescript
const filteredDropdownItems = useMemo(() => {
  if (!data || data.length === 0) {
    return DropdownItems; // 显示所有选项
  }
  
  // 获取data中所有的targetId
  const availableTargetIds = data.map(item => item.targetId);
  
  // 筛选出在data中存在的DropdownItems
  return DropdownItems.filter(item => availableTargetIds.includes(item.value));
}, [data]);
```

### 默认选择逻辑
- 如果有 `allow: true` 的项，选择第一个
- 如果没有 `allow: true` 的项，选择筛选后的第一个选项
- 如果没有数据，选择默认值99或第一个可用选项

## 向后兼容性

这个修改完全向后兼容：
- 当 `data` 为 `undefined` 或空数组时，显示所有选项
- 现有的API和接口保持不变
- 不影响其他组件的使用

## 测试建议

建议测试以下场景：
1. 传入包含部分 targetId 的数据
2. 传入空数组
3. 传入 undefined
4. 传入单个 targetId 的数据
5. 验证选中状态的正确性
