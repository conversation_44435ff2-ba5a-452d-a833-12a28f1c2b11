.myLayout {
  background: var(--theme-layout-color-bg-white);
  color: var(--theme-brand-color);
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-size: 14px;

  * {
    box-sizing: border-box;
  }

  .bd {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .content {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: var(--theme-layout-color-bg-new-page);
  }
}
