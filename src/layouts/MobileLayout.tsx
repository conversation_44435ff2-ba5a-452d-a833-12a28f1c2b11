import { type PropsWithChildren, useMemo } from 'react';
import { useLocation } from 'umi';

import CreateFileBtn from '@/components-mobile/CreateFileBtn';
import { UploadList } from '@/components-mobile/FileUpload/UploadList';
import { FileSettingProvider } from '@/contexts/FileSetting';
import { useLoginGuard } from '@/hooks/Authorization';
import { useShowErrorPageStore } from '@/store/errorPage';
import { useMeStore } from '@/store/Me';

import { Footer } from '../components-mobile/Footer';
import style from './MobileLayout.less';

const noFooterPath = ['/templates', '/login', '/move', '/createCopy'];
export default function MobileLayout(props: PropsWithChildren) {
  const meId = useMeStore((state) => state.me.id);
  const showErrorPage = useShowErrorPageStore((state) => state.showErrorPage);

  const location = useLocation();
  const notInitial = meId === null;

  useLoginGuard();
  const isOutside = useMemo(() => {
    return noFooterPath.some((item) => location.pathname.includes(item));
  }, [location.pathname]);

  return notInitial ? null : (
    <FileSettingProvider>
      <div className={style.layout} style={{ height: isOutside ? '100%' : 'calc(100% - 48px)' }}>
        {props.children}
      </div>
      {!isOutside && <Footer />}
      {showErrorPage ? null : (
        <>
          <UploadList />
          <CreateFileBtn />
        </>
      )}
    </FileSettingProvider>
  );
}
