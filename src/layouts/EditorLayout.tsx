import type { PropsWithChildren } from 'react';

import EditLayoutContent from '@/components/EditLayoutContent';
import { FileSettingProvider } from '@/contexts/FileSetting';
import { useMeStore } from '@/store/Me';

export default function EditorLayout(props: PropsWithChildren<{ style?: React.CSSProperties }>) {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;

  return notInitial ? null : (
    <FileSettingProvider>
      <EditLayoutContent {...props} />
    </FileSettingProvider>
  );
}
