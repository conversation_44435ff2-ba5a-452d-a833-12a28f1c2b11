import { DndContext, pointerWithin, useDroppable } from '@dnd-kit/core';
import Layout from 'antd/es/layout';
import { Content } from 'antd/es/layout/layout';
import { type PropsWithChildren, useEffect } from 'react';

import { CustomDragOverlay } from '@/components/Desktop/QuickAccess/CustomDragOverlay';
import SiderMenu from '@/components/Desktop/SiderMenu/index';
import DesktopHeader from '@/components/DesktopHeader';
import { useLoginGuard } from '@/hooks/Authorization';
import { DragPositionProvider, useDragContext } from '@/hooks/useDragContext';
import { useDragHandlers } from '@/hooks/useDragHandlers';
import { CustomEventName } from '@/model/CustomEvent';
import { useMeStore } from '@/store/Me';
import { emitCustomEvent } from '@/utils/customEvent';

import styles from './DesktopLayout.less';

// 桌面内容区域组件，包含拖拽接收功能
function DesktopContent({ children }: PropsWithChildren) {
  const { setNodeRef: setDesktopDropRef } = useDroppable({
    id: 'desktop-drop-zone',
  });

  return (
    <Content ref={setDesktopDropRef} className={styles.styledContent}>
      {children}
    </Content>
  );
}

export default function DesktopLayout(props: PropsWithChildren) {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;
  useLoginGuard();

  // 获取拖拽处理器
  const { handleGlobalDragEnd } = useDragHandlers();

  // 使用统一的拖拽上下文管理
  const { sensors, activeId, dragData, dragPosition, handleDragStart, handleDragMove, handleDragEnd } = useDragContext({
    activationDistance: 3,
    onDragEnd: handleGlobalDragEnd, // 使用抽离的拖拽处理器
  });

  useEffect(() => {
    setTimeout(() => {
      emitCustomEvent(CustomEventName.aiTemplateInitAI, {});
    }, 100);
  }, []);

  return notInitial ? null : (
    <DragPositionProvider value={dragPosition}>
      <DndContext
        collisionDetection={pointerWithin}
        sensors={sensors}
        onDragEnd={handleDragEnd}
        onDragMove={handleDragMove}
        onDragStart={handleDragStart}
      >
        <div className={styles.layoutContainer}>
          <DesktopHeader />
          <Layout className={styles.styledLayout}>
            <SiderMenu />
            <Layout>
              <DesktopContent>{props.children}</DesktopContent>
            </Layout>
          </Layout>
        </div>
        {/* 自定义拖拽浮层 */}
        {activeId && (dragData?.type === 'desktop-file' || dragData?.type === 'quick-access-file') && (
          <CustomDragOverlay
            fileCount={dragData.fileCount || 1}
            fileName={dragData.file?.name}
            fileType={dragData.file?.type}
            isShortcut={dragData.file?.isShortcut}
            posX={dragPosition.x}
            posY={dragPosition.y}
          />
        )}
      </DndContext>
    </DragPositionProvider>
  );
}
