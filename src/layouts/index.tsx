import { ConfigProvider, message, theme } from 'antd';
import type { ReactNode } from 'react';
import React, { Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'umi';

import { AiButton, SYSTEM_AI_LIST } from '@/components/AiButton';
import { LoadBoard } from '@/components/LoadBoard';
import { TemplateModal } from '@/components/TemplateModal';
import { useAnonymousEffects, useHasLogin } from '@/hooks/Authorization';
import { useApplyThemeToHtml, useThemeIsDark } from '@/hooks/Theme';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm } from '@/modules/Locale';
import { themeConfig } from '@/themes/config';
import { handleLogin } from '@/utils/login/login';

import BlankLayout from './BlankLayout';
import { ContainerLayout } from './ContainerLayout';
import DesktopLayout from './DesktopLayout';
import EditorLayout from './EditorLayout';
import styles from './index.less';
import LoginLayout from './LoginLayout';
import MobileLayout from './MobileLayout';
import SettingLayout from './SettingLayout';

const LoginPath = ['/login', '/__DRIVE__/config', '/ldap/login'];
const EditorPath = [
  '/docs',
  '/docx',
  '/sheets',
  '/presentation',
  '/ppt',
  '/tables',
  '/forms',
  '/files',
  '/boards',
  '/mindmaps',
];
const ProfilePath = ['/profile'];
const DesktopPath = ['/desktop', '/space', '/recent', '/share', '/trash', '/favorites', '/folder', '/workActivity'];
const ManagementPath = [
  '/enterprise/trash',
  '/enterprise/efficiency',
  '/enterprise/members',
  '/enterprise/audit',
  '/enterprise/packages',
  '/enterprise/whitelist',
  '/enterprise/settings',
  '/enterprise/template',
];
// 空白页面，不带任何layout,只有用户数据
const BlankPath = ['/file-invite'];

const Layout: React.FC = () => {
  const isMobilePlatform = process.env.PLATFORM === 'mobile';
  let isEditorPage = false;

  useApplyThemeToHtml();
  const isDark = useThemeIsDark();

  // 初始化当前用户的数据
  useAnonymousEffects();

  // 获取当前地址
  const location = useLocation();
  const networkError = fm('UseFileUpload.networkError');
  //判断网络情况
  const isOnline = useNetworkStatus();
  useEffect(() => {
    if (!isOnline) {
      // 编辑器页面已有其他网络提示，这里不再提示
      if (!EditorPath.some((path) => location.pathname.startsWith(path))) {
        message.error(networkError);
      }
    }
  }, [isOnline, location.pathname, networkError]);

  const hasLogin = useHasLogin();

  // 检查是否需要跳转到登录页
  const needLogin =
    !hasLogin &&
    !LoginPath.some((path) => location.pathname.startsWith(path)) &&
    !BlankPath.some((path) => location.pathname.startsWith(path)) &&
    !EditorPath.some((path) => location.pathname.startsWith(path));

  // 登录守卫 - 没有登录信息时跳转到登录页
  useEffect(() => {
    if (needLogin) {
      handleLogin();
    }
  }, [needLogin]);

  // 如果需要跳转到登录页，返回空
  if (needLogin) return null;

  let content: ReactNode;

  if (isMobilePlatform) {
    if (EditorPath.some((path) => location.pathname.startsWith(path))) {
      // 编辑器页面的头部在layout里面，预览页面单独引入头部（预览不走这里）
      content = (
        <EditorLayout>
          <Outlet />
          <LoadBoard />
        </EditorLayout>
      );
    } else if (BlankPath.some((path) => location.pathname.startsWith(path))) {
      content = (
        <BlankLayout>
          <Outlet />
        </BlankLayout>
      );
    } else {
      content = (
        <MobileLayout>
          <Outlet />
        </MobileLayout>
      );
    }
  } else if (LoginPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <LoginLayout>
        <Outlet />
      </LoginLayout>
    );
  } else if (EditorPath.some((path) => location.pathname.startsWith(path))) {
    isEditorPage = true;
    document.body.classList.remove('drive-dark'); //编辑器页面的sdk暂不支持暗色模式
    content = (
      <EditorLayout>
        <Outlet />
        <LoadBoard />
      </EditorLayout>
    );
  } else if (ProfilePath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <SettingLayout>
        <Outlet />
      </SettingLayout>
    );
  } else if (DesktopPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <DesktopLayout>
        <Outlet />
        <LoadBoard />
        <AiButton arrow={true} open={!(localStorage.getItem(SYSTEM_AI_LIST) === '1')} type="list" />
      </DesktopLayout>
    );
  } else if (ManagementPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <ContainerLayout>
        <Outlet />
      </ContainerLayout>
    );
  } else if (BlankPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <BlankLayout>
        <Outlet />
      </BlankLayout>
    );
  } else {
    content = <Outlet />;
  }

  return (
    <Suspense fallback={'loading...'}>
      <ConfigProvider
        theme={{
          ...themeConfig,
          algorithm: isDark && !isEditorPage ? theme.darkAlgorithm : theme.defaultAlgorithm,
        }}
        wave={{ disabled: true }}
      >
        <div className={styles.layoutContainer}>{content}</div>
        <TemplateModal />
        <div id="ai-btn-pop" />
      </ConfigProvider>
    </Suspense>
  );
};

export default Layout;
