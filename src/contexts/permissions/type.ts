/**
 * 统一权限项清单
 *
 * 权限项的标识应与后端接口保持一致
 * 包含了 RBAC 覆盖的权限项(命名必须和后端一致) 以及其他拥有权限特征的所有内容
 *
 * 权限验证包含三个步骤：
 * 1. rbac check: 判断这个权限是否由 RBAC 接口返回。 对于 RBAC 覆盖外的权限需加入 DefaultTruthPermissionItemKeys 数组中。
 * 2. team check: 判断企业是否拥有该权限(如 alpha features, user features 等)。
 * 3. scene check: 判断权限在特殊场景下是否可用。计划用来约束不能删除创建者类似的场景， 也用于对 RBAC 覆盖外的权限判断是否是创建者/管理员。
 */
export type PermissionItemKeys =
  // 效能看板
  | 'manage_performance'
  // 支付与订单
  | 'manage_order'
  // 查看通讯录
  | 'show_team_members'
  // 管理通讯录
  | 'manage_team_members'
  // 邀请新成员
  | 'invite_team_members'
  // 组织架构可见范围
  | 'manage_organization_visible_scope'
  // 操作日志
  | 'manage_audit'
  // 公开分享链接管理
  | 'manage_public_share_links'
  // 是否允许企业成员主动退出企业
  | 'manage_exit_behavior'
  // 文件安全预警
  | 'manage_alert'
  // 企业文件展示协作者水印
  | 'manage_watermark'
  // 文件全局管控
  | 'manage_team_file'
  // 企业回收站
  | 'manage_enterprise_trash'
  // 角色管理(只有部分企业内的身份才有这个功能，比如企业创建者)
  | 'manage_role'
  // 离职交接
  | 'handover'
  // 文件高级权限
  | 'manage_file_permission'
  // 字段管理
  | 'fieldManager'
  // 新建空间
  | 'create_team_space'
  // 防打扰管理
  | 'manage_bother_rules'
  // 容量管理
  | 'manage_quota'
  // 套餐管理菜单
  | 'enable_manage_kit_seat'
  // 白名单管理菜单
  | 'enable_manage_whitelist_seat'
  // SSO 单点登录
  | 'sso_saml';

/**
 * 所有 /lizard-api/user/features 返回的key
 *
 * 下面测的key 不一定全，如果发现下面有新的 key 请及时补充
 */
export type FeatureKeys =
  | 'pivot_table'
  | 'sheet_lock'
  | 'filter_viewport'
  | 'combine_sheet'
  | 'cell_history'
  | 'date_mention'
  | 'follow_selected_area'
  | 'array_formula'
  | 'space_volume'
  | 'file_permission'
  | 'file_lock'
  | 'contacts'
  | 'organization_management'
  | 'file_organization'
  | 'performance_panel'
  | 'handover'
  | 'audit'
  | 'external_collaborators'
  | 'public_sharing'
  | 'visitor_watermark'
  | 'safe_alert'
  | 'doc_follow_mode'
  | 'department_scope'
  | 'security_setting_outside'
  | 'security_setting_sharelink'
  | 'security_setting_copyright'
  | 'security_setting_file_download'
  | 'security_setting_copy'
  | 'security_setting_contract'
  | 'enable_share_expire_time'
  | 'is_open_role_apply'
  | 'doc_wide_table'
  | 'enable_share_password'
  | 'enable_share_enterprise_only'
  | 'doc_sheet_comment'
  | 'enterprise_trash'
  | 'file_control'
  | 'batch_download'
  | 'public_share'
  | 'unzip'
  | 'insert_video_into_ppt'
  | 'calculate_table_exclusive_queue'
  | 'combined_table_auto_sync'
  | 'file_advance_permissions'
  | 'can_upload_office_file'
  | 'can_upload_archive'
  | 'can_upload_video'
  | 'can_upload_audio'
  | 'file_share_management'
  | 'can_search_content'
  | 'manage_quota'
  | 'member_manage_quota'
  | 'file_templates'
  | 'change_folder_collaborator'
  | 'output_conversion_toolbox'
  | 'output_conversion_toolbox_pdf2word'
  | 'output_conversion_toolbox_pdf2excel'
  | 'output_conversion_toolbox_pdf2ppt'
  | 'tracking'
  | 'sso_saml'
  | 'team_role_manage'
  | 'enable_file_tag'
  | 'bind_member_permission'
  | 'modifying_role_permission'
  | 'enable_manage_kit_seat'
  | 'enable_manage_whitelist_seat'
  | 'create_role_permission'
  | 'manage_bother_rules'
  | 'upgrade_to_premium';

export type TeamType = 'dingtalk' | 'wework' | 'normal' | 'ssosaml';

/**
 * 不受 RBAC 控制的权限项
 * 如果想将一个权限项迁移至 RBAC, 只需要删除这里的默认值, 然后检查 SceneConditionMap 是否有角色相关逻辑并移除
 */
export const DefaultTruthPermissionItemKeys: Array<PermissionItemKeys> = [
  'manage_performance',
  'manage_order',
  'manage_organization_visible_scope',
  'manage_audit',
  'manage_exit_behavior',
  'handover',
  'manage_file_permission',
  'manage_alert',
  'manage_watermark',
  'manage_bother_rules',
  'manage_quota',
  'fieldManager',
  'sso_saml',
  'enable_manage_kit_seat',
  'enable_manage_whitelist_seat',
];

export enum AlphaFeatureKeys {
  handover = 'handover',
  enterprise_audit = 'enterprise_audit',
  performance = 'performance',
  file_control = 'file_control',
  organization_contacts = 'organization_contacts',
}

export const alphaFeatureKeys = [
  AlphaFeatureKeys.handover,
  AlphaFeatureKeys.enterprise_audit,
  AlphaFeatureKeys.performance,
  AlphaFeatureKeys.file_control,
  AlphaFeatureKeys.organization_contacts,
];

export type TargetAccountType = 'enterprise_standard' | 'enterprise_premium' | 'enterprise_light';

export interface TeamInfoResponse {
  quitAction: string;
  scaleNum: number;
  id: number;
  city: string;
  inviteGuid: string;
  mobile: string;
  name: string;
  province: string;
  quit_action: string;
  scale: string;
  type: string;
  deletedAt: null;
  updatedAt: string;
  createdAt: string;
  scale_num: number;
  info: string;
  dissolved: boolean;
  dingtalkCorp: null | boolean;
  weworkCorp: null | boolean;
  enterpriseId: string;
  membership: {
    expiredAt: string;
    isOfficial: boolean;
    memberCount: number;
    targetId: number;
    targetType: number;
    deletedAt: null | string;
    updatedAt: string;
    createdAt: string;
    editionId: number;
    id: number;
    category: number;
  };
  accountType: TargetAccountType;
  isEnterprisePremium: boolean;
  disabledMemberCount: number;
  memberCount: number;
  seatMemberCount: number;
  invitedMemberCount: number;
}

export interface SyncThirdPartyResponse {
  syncFromThirdparty: boolean;
  syncPanelEnabled: boolean;
  profileChange: {
    avatar: boolean;
    name: boolean;
  };
}

export interface PermissionState {
  alphaFeatures: {
    [key: string]: boolean;
  };
  features: FeatureKeys[];
  currentPermissions: Array<PermissionItemKeys>;
  /**
   * 1: 在线席位（含白名单）  0: 固定席位
   */
  limitType?: 1 | 0;
  loaded: boolean;
  isDingtalk: boolean;
  isSSOSAML: boolean;
  isWework: boolean;
  teamInfo: TeamInfoResponse | null;
  syncFromThirdparty: boolean;
  syncPanelEnabled: boolean;
}

export type Action =
  | {
      type: 'setState';
      payload: PermissionState;
    }
  | {
      type: 'setIsDingtalkOrWework';
      payload: {
        isDingtalk: boolean;
        isSSOSAML: boolean;
        isWework: boolean;
      };
    }
  | {
      type: 'setTeamInfo';
      payload: {
        teamInfo: TeamInfoResponse;
      };
    }
  | {
      type: 'setSyncThirdState';
      payload: {
        syncFromThirdparty: boolean;
        syncPanelEnabled: boolean;
      };
    };
