/* eslint-disable complexity */
import { useCallback } from 'react';

import { FileIconType } from '@/components/FileIconOld/type';

import type { ImageAsset } from './image-config';
import { useGetImageSrcset } from './image-config';

export function useGetFileIcon24(): (type: `${FileIconType}`) => ImageAsset {
  const getImageSrcset = useGetImageSrcset();

  return useCallback(
    (type: `${FileIconType}`) => {
      switch (type) {
        // 石墨文件
        case FileIconType.Folder:
          return getImageSrcset('FileFolder');
        case FileIconType.Document:
          return getImageSrcset('FileDoc');
        case FileIconType.Spreadsheet:
          return getImageSrcset('FileSheet');
        case FileIconType.Doc:
          return getImageSrcset('FileDoc');
        case FileIconType.Sheet:
          return getImageSrcset('FileSheet');
        case FileIconType.Mosheet:
          return getImageSrcset('FileSheet');
        case FileIconType.Modoc:
          return getImageSrcset('FileDocx');
        case FileIconType.Mindmap:
          return getImageSrcset('FileMindmap');
        case FileIconType.Form:
          return getImageSrcset('FileForm');
        case FileIconType.Slide:
          return getImageSrcset('FilePresentation');
        case FileIconType.TableViewForm:
          return getImageSrcset('FileForm');
        case FileIconType.QuizForm:
          return getImageSrcset('FileForm');
        case FileIconType.Board:
          return getImageSrcset('FileBoard');
        case FileIconType.Presentation:
          return getImageSrcset('FilePresentation');
        case FileIconType.Table:
          return getImageSrcset('FileTable');
        case FileIconType.Shortcut:
          return getImageSrcset('FileUnknown');

        // 云文件
        case FileIconType.Xmind:
          return getImageSrcset('FileXmind');
        case FileIconType.Wps:
          return getImageSrcset('FileWps');
        case FileIconType.Video:
          return getImageSrcset('FileVideo');
        case FileIconType.Zip:
          return getImageSrcset('FileZip');
        case FileIconType.Audio:
          return getImageSrcset('FileAudio');
        case FileIconType.Ppt:
          return getImageSrcset('FilePpt');
        case FileIconType.Word:
          return getImageSrcset('FileWord');
        case FileIconType.Excel:
          return getImageSrcset('FileExcel');
        case FileIconType.Pdf:
          return getImageSrcset('FilePdf');
        case FileIconType.Image:
          return getImageSrcset('FileImage');
        case FileIconType.Ae:
          return getImageSrcset('FileAe');
        case FileIconType.Ai:
          return getImageSrcset('FileAi');
        case FileIconType.Psd:
          return getImageSrcset('FilePs');
        case FileIconType.Sketch:
          return getImageSrcset('FileSketch');

        // 其他格式
        case FileIconType.Cloud:
          return getImageSrcset('FileCloud');
        case FileIconType.Space:
          return getImageSrcset('FileSpace');

        case FileIconType.Unknown:
        default:
          return getImageSrcset('FileUnknown');
      }
    },
    [getImageSrcset],
  );
}

export function useFileIcon24(type: `${FileIconType}`): ImageAsset {
  return useGetFileIcon24()(type);
}
