import { DEFAULT_GUID } from '../index';
import type { Action, Files, HandoverState, TreeFile } from '../type';

/**
 * 从文件树查找某一个guid的children
 */
function findChildrenByGuid(treeFile: TreeFile, guid: string): TreeFile[] | undefined {
  if (guid === treeFile.guid) {
    return treeFile.children;
  } else {
    if (treeFile.children) {
      const children = treeFile.children.find((child) => child.guid === guid);
      if (children) {
        return children.children;
      } else {
        const foundChildren = treeFile.children.reduce((acc, child) => {
          if (child.children) {
            const found = findChildrenByGuid(child, guid);
            if (found) {
              acc.push(...found);
            }
          }
          return acc;
        }, [] as TreeFile[]);

        if (foundChildren.length > 0) {
          return foundChildren;
        }
        return undefined;
      }
    } else {
      return undefined;
    }
  }
}

/**
 * 数组去重
 */
function unique(list1: any[], list2: any[]) {
  return Array.from(new Set([...list1, ...list2].map((file) => file.guid))).map((guid) => {
    // 在合并后的数组中查找文件
    const fileInList = list2.find((file) => file.guid === guid);
    const fileInFlattenFiles = list1.find((file) => file.guid === guid);

    // 如果文件在两个数组中都存在，只添加一次
    if (fileInList && fileInFlattenFiles) {
      return fileInList;
    } else if (fileInList) {
      return fileInList;
    } else {
      return fileInFlattenFiles;
    }
  });
}

/**
 * 将文件列表插入到文件树
 */
function insertFilesInTree(files: Files, treeFile: TreeFile, guid: string) {
  if (treeFile.children) {
    const guidChildren = treeFile.children.map((child) => child.guid);
    const index = guidChildren.findIndex((g) => g === guid);
    if (index > -1) {
      treeFile.children[index].children = files;
    } else {
      treeFile.children.map((child) => (child.children ? insertFilesInTree(files, child, guid) : null));
    }
  }
}

export function handoverReducer(handover: HandoverState, action: Action): any {
  switch (action.type) {
    case 'setState': {
      const { flattenFiles: preFlattenFile } = action.state;
      const flattenFiles = preFlattenFile.map((file) => {
        if (file.guid !== DEFAULT_GUID) {
          file.parentGuid = DEFAULT_GUID;
        }
        return file;
      });
      return {
        ...action.state,
        flattenFiles,
      };
    }
    case 'setFiles': {
      const { files, guid } = action;
      const { treeFiles, currentFolder } = handover;
      const flattenFile = files.map((file) => {
        file.parentGuid = guid;
        return file;
      });

      const newFlattenFiles = unique(handover.flattenFiles, flattenFile);
      insertFilesInTree(files, treeFiles, guid);

      return {
        ...handover,
        files: action.files,
        flattenFiles: newFlattenFiles,
        treeFiles,
        currentFolder: newFlattenFiles.find((file) => file.guid === guid) ?? currentFolder,
      };
    }
    case 'backFolderGuid': {
      const { guid } = action;
      const { treeFiles, flattenFiles } = handover;
      const files = findChildrenByGuid(treeFiles, guid);
      const currentFolder = flattenFiles.find((file) => file.guid === guid);

      return {
        ...handover,
        files: files ?? [],
        currentFolder,
      };
    }
    case 'setLoading': {
      const { flag } = action;
      return {
        ...handover,
        loading: flag,
      };
    }
    default: {
      throw Error(`Unknown action: ${JSON.stringify(action)}`);
    }
  }
}
