import type { Files, HistoryFiles, PageType } from '../type';
import { baseURL } from './constant';

/**
 * 获取该id的桌面文件
 * @param id
 * @returns
 */

/**
 * 获取文件夹子文件
 * @param guid
 * @returns
 */
export const getChildrenFiles = async (props: { guid: string; leftUser: number; pageType: PageType }) => {
  const { guid, leftUser, pageType } = props;
  const response = await fetch(`${baseURL}/handover/files/${guid}?leftUser=${leftUser}&pageType=${pageType}`);
  const data = (await response.json()) as Files;
  return data;
};

/**
 * 生成文件快照
 */
export const createSnapshot = async (guids: string[], uid: number) => {
  const response = await fetch(`${baseURL}/handover/tokens`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },

    body: JSON.stringify({
      guids,
      left_user: uid,
      confirmed: false,
    }),
  });
  if (!response.ok) {
    throw new Error('create snapshot failed');
  }
  const data = (await response.json()) as { token: string };
  return data;
};

/**
 * 获取历史交接文件
 */
export const getHistoryFiles = async (uid: number) => {
  const response = await fetch(`${baseURL}/handover/files/history/${uid}`);
  const data = (await response.json()) as HistoryFiles;

  return data;
};
