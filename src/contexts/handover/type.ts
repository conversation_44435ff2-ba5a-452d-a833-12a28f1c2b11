export interface File {
  id: number;
  guid: string;
  type: string;
  name: string;
  isSpace: boolean;

  parentGuid?: string;
  /**
   * @warning 这个属性只有在获取了子文件列表后才会存在值
   */
  children?: Array<TreeFile>;
}

export type Files = File[];

export enum FileType {
  Table = -11,
  Presentation = -10,
  Board = -9,
  Form = -8,
  Mindmap = -7,
  Modoc = -6,
  Sheet = -4,
  Doc = -2,
  Folder = 1,
  Space = 2,
  Img = 3,
  Pdf = 4,
  Xls = 5,
  Docx = 6,
  Ppt = 7,
  Mp3 = 8,
  Zip = 9,
  Mp4 = 10,
  Wps = 11,
  Xmind = 12,

  OldSheet = -3,
  Document = 0,
}

export interface TreeFile extends File {
  children?: Array<TreeFile>;
}

export interface HandoverState {
  files: Files;
  flattenFiles: Array<TreeFile>;
  treeFiles: TreeFile;
  currentFolder: TreeFile;
  loading: boolean;
}

export type Action =
  | {
      type: 'setState';
      state: HandoverState;
    }
  | {
      type: 'setFiles';
      files: Files;
      guid: string;
    }
  | {
      type: 'backFolderGuid';
      guid: string;
    }
  | {
      type: 'setLoading';
      flag: boolean;
    };

export interface HistoryFileItem {
  id: number;
  token: string;
  name: string;
  guid: string;
  handoverStatus: string; // '已交接'  ???
  handoverUsers: string; // '6036606,6037669'
  type: 1 | 2 | 3;
  subType: number;
  userList: {
    id: number;
    name: string;
    avatar: string;
    email: string;
  }[];
}

export interface HistoryFiles {
  list: HistoryFileItem[];
}

/**
 * 页面进入模式类型
 */
export enum PageType {
  /**
   * 0:普通用户进入交接页面
   */
  HANDOVER = 0,
  /**
   * 1:管理员进入交接管理页面
   */
  MANAGEMENT_HANDOVER = 1,
  /**
   * 2:发起交接页面
   */
  INITIATE_HANDOVER = 2,
}
