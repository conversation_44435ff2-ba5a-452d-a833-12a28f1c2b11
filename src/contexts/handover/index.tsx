import type { ReactNode } from 'react';
import React, { createContext, useEffect, useReducer } from 'react';

import { getDesktopFiles } from '@/api/handover';
import type { Action, HandoverState } from '@/model/handover';
import { fm2 } from '@/modules/Locale';

import { handoverReducer } from './reducer';
export const HandoverContext = createContext<HandoverState | null>(null);
export const HandoverDispatchContext = createContext<null | React.Dispatch<Action>>(null);

/**
 * 默认id
 * 最外层
 */
export const DEFAULT_ID = -999;

/**
 * 默认guid
 * 最外层
 */
export const DEFAULT_GUID = 'root';

const s18nText = {
  allFiles: fm2('hanover.allFiles'),
};

const initialHandover: HandoverState = {
  files: [],
  flattenFiles: [],
  // 最外层结构需要手动构建
  currentFolder: {
    id: DEFAULT_ID,
    guid: DEFAULT_GUID,
    type: '',
    name: s18nText.allFiles,
    isSpace: true,
  },
  treeFiles: {
    children: [],
    id: DEFAULT_ID,
    guid: DEFAULT_GUID,
    type: '',
    name: s18nText.allFiles,
    isSpace: true,
  },
  loading: false,
};

export const HandoverProvider: React.FC<{
  children: ReactNode;
  uid: number;
}> = ({ children, uid }) => {
  const [handover, dispatch] = useReducer(handoverReducer, initialHandover);

  const init = (uid: number) => {
    dispatch({
      type: 'setLoading',
      flag: true,
    });
    getDesktopFiles(uid)
      .then((files) => {
        dispatch({
          type: 'setState',
          state: {
            ...initialHandover,
            files,
            flattenFiles: [initialHandover.currentFolder, ...files],
            currentFolder: initialHandover.currentFolder,
            treeFiles: {
              ...initialHandover.treeFiles,
              children: files,
            },
          },
        });
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => {
        dispatch({
          type: 'setLoading',
          flag: false,
        });
      });
  };

  useEffect(() => {
    if (uid) {
      init(uid);
    }
  }, [uid]);

  return (
    <HandoverContext.Provider value={handover}>
      <HandoverDispatchContext.Provider value={dispatch}>{children}</HandoverDispatchContext.Provider>
    </HandoverContext.Provider>
  );
};
