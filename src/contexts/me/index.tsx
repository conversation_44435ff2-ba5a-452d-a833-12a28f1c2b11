import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useReducer } from 'react';

import { PreferencesDispatchContext } from '../preferences';
// import { useTracker } from '../tracker';
import { meReducer } from './reducer';
import { getMe, getPreferences } from './service/api';
import type { Action, Me } from './type';

export const MeContext = createContext<Me | null>(null);
export const MeDispatchContext = createContext<null | React.Dispatch<Action>>(null);

const initialMe: Me = {
  id: null,
  name: '',
  name_pinyin: '',
  email: '',
  avatar: '',
  status: 0,
  team: {
    quitAction: '',
    scaleNum: 0,
    id: 0,
    city: '',
    mobile: '',
    name: '',
    province: '',
    quit_action: '',
    scale: '',
    type: '',
    deletedAt: '',
    updatedAt: '',
    createdAt: '',
    scale_num: 0,
    info: '',
    dissolved: false,
    isMerged: false,
  },
  teamRole: '',
  teamTime: '',
  team_time: '',
  team_role: '',
  isSeat: 1,
  is_seat: 1,
  createdAt: '',
  merged_into: null,
  mergedInto: null,
  teamId: 0,
  team_id: 0,
  mobile: '',
  mobileAccount: '',
  hasPassword: false,
  membership: {
    accountTypeExpiredAt: '',
    accountType: '',
    accountTypeCreatedAt: '',
    isEnterprisePremium: false,
    isExpired: false,
    isNewDing: false,
    isOfficial: false,
    editionId: 1,
  },
  accountMetadata: {
    isExpired: false,
    isDingtalk: false,
    isWework: false,
    isEnterprise: false,
    isFreeEnterprise: false,
    expiredAt: {
      seconds: 0,
      nanos: 0,
    },
    isTrial: false,
    isPersonalPremium: false,
    isEnterprisePremium: false,
    isEnterpriseLight: false,
    editionId: 0,
  },
  editionName: '',
  requiresIdentityVerification: false,
};

export const MeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [me, dispatch] = useReducer(meReducer, initialMe);
  const preferencesDispatch = useContext(PreferencesDispatchContext);
  // const tracker = useTracker();

  useEffect(() => {
    const backToLogin = () => {
      const redirectUrl = encodeURIComponent(location.href);
      location.href = `${location.origin}/login?redirect_url=${redirectUrl}`;
    };
    Promise.all([getMe(), getPreferences()])
      .then(([me, preferences]) => {
        if (!me || !me.id) {
          backToLogin();
          return;
        }
        dispatch?.({
          type: 'setMe',
          payload: me,
        });
        preferencesDispatch?.({
          type: 'setPreferences',
          payload: preferences.data,
        });

        const homePage = preferences?.data?.homePage || 'recent';
        if (!me.team) {
          location.href = `${location.origin}/${homePage}`;
        }
      })
      .catch(() => {
        // safari 多次跳转
        backToLogin();
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <MeContext.Provider value={me}>
      <MeDispatchContext.Provider value={dispatch}>{children}</MeDispatchContext.Provider>
    </MeContext.Provider>
  );
};
