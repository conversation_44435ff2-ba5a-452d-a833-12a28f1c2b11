import type { Me } from '../type';

export const getMe = async () => {
  const response = await fetch(`/users/me`, {
    headers: {
      'Cache-Control': 'no-cache',
    },
  });
  const data = (await response.json()) as Me;
  return data;
};

import type { PreferencesResponse } from '../type';

export const getPreferences = async () => {
  const response = await fetch(`/user/preferences_settings`);
  const data = (await response.json()) as PreferencesResponse;
  return data;
};
