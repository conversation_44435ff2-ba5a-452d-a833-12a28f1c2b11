export interface Me {
  id: null | number;
  name: string;
  name_pinyin: string;
  email: string;
  avatar: string;
  status: 1 | 0;
  team: {
    quitAction: string;
    scaleNum: number;
    id: number;
    city: string;
    mobile: string;
    name: string;
    province: string;
    quit_action: string;
    scale: string;
    type: string;
    deletedAt: string;
    updatedAt: string;
    createdAt: string;
    scale_num: number;
    info: string;
    dissolved: boolean;
    isMerged: boolean;
    dingtalkCorp?: boolean;
    weworkCorp?: boolean;
  };
  teamRole: string;
  teamTime: string;
  team_time: string;
  team_role: 'creator' | 'admin' | '';
  isSeat: 1 | 0;
  is_seat: 1 | 0;
  createdAt: string;
  merged_into: null;
  mergedInto: null;
  teamId: number;
  team_id: number;
  mobile: string;
  mobileAccount: string;
  hasPassword: boolean;
  membership: {
    accountTypeExpiredAt: string;
    accountType: string;
    accountTypeCreatedAt: string;
    isEnterprisePremium: boolean;
    isExpired: boolean;
    isNewDing: boolean;
    isOfficial: boolean;
    editionId: UserEdition;
  };
  accountMetadata: {
    isExpired: boolean;
    isDingtalk: boolean;
    isWework: boolean;
    isEnterprise: boolean;
    isFreeEnterprise: boolean;
    expiredAt: {
      seconds: number;
      nanos: number;
    };
    isTrial: boolean;
    isPersonalPremium: boolean;
    isEnterprisePremium: boolean;
    isEnterpriseLight: boolean;
    editionId: number;
  };
  /**
   * 这个字段需要考虑国际化问题
   */
  editionName: string;
  requiresIdentityVerification: boolean;
}

export interface Action {
  type: 'setMe';
  payload: Me;
}

/**
 * 这套各版本对应 edition 更新于 2024.05.13，后续以这套版本为主
 */
export enum UserEdition {
  /**
   *  企业版
   */
  StandardEnterprise = 1,
  /**
   * 企业高级版
   */
  PremiumEnterprise = 2,
  /**
   * 企业团队版（废弃）
   */
  DingDingLightEnterprise = 3,
  /**
   * 团队版
   */
  LightEnterprise = 4,
  /**
   * 个人高级版
   */
  PremiumPersonal = 5,
  /**
   * 个人免费版
   */
  FreePersonal = 6,
  /**
   * 钉钉企业免费版
   */
  DingDingFreeEnterprise = 8,
  /**
   * 个人体验版
   */
  ExperiencePersonal = 9,
  /**
   * 华为云免费版
   */
  HuaWeiCloudFreeEnterprise = 11,
  /**
   * 个人超级会员
   */
  SuperPersonal = 12,
}

export interface Preferences {
  clickFileArea: string;
  createFileByTemplate: boolean;
  homePage: 'desktop' | 'recent' | 'space';
  isShowIconOnly: boolean;
  openFileLocation: 'newTab' | 'currentTab';
  personalizedADRecommandation: boolean;
}

export interface PreferencesResponse {
  domain: string;
  requestId: string;
  data: Preferences;
}

// export interface Action {
//   type: 'setPreferences';
//   payload: Preferences;
// }
