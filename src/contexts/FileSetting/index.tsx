import { ActionSheet } from 'antd-mobile';
import type { Action, ActionSheetProps } from 'antd-mobile/es/components/action-sheet';
import { createContext, type ReactNode, useCallback, useMemo, useRef, useState } from 'react';

import { CollaborationShareMobile } from '@/components-mobile/Collaboration/CollaborationShareMobile';
import { useDisclosure } from '@/hooks/useDisclosure';
import type { FileDataModel, VisibleDataModel } from '@/model/FileList';

import { useActionsProp } from './actions';
import style from './index.less';
import { PopupMoveAndCopy } from './PopupMoveAndCopy';

export const FileSettingContext = createContext<{
  setOpenPopup: (bol: boolean) => void;
  setActionProp: (actions: ActionSheetProps) => void;
  updateActionProp: (fileData: FileDataModel, selectedType?: string) => void;
  goRefreshFileList: () => void;
  refreshFileList: number;
  handleOpenCollaboration: (guid: string) => void;
  closeCollaboration: () => void;
  toggleCollaboration: () => void;
  mainDivRef: React.RefObject<HTMLDivElement> | null;
  fileData: FileDataModel | null;
  setFileData: (fileData: FileDataModel) => void;
  visibleData: VisibleDataModel;
  setVisibleData: React.Dispatch<React.SetStateAction<VisibleDataModel>>;
}>({
  goRefreshFileList: () => {},
  refreshFileList: 0,
  setOpenPopup: () => {},
  setActionProp: () => {},
  updateActionProp: () => {},
  handleOpenCollaboration: () => {},
  closeCollaboration: () => {},
  toggleCollaboration: () => {},
  mainDivRef: null,
  fileData: null,
  setFileData: () => {},
  setVisibleData: () => {},
  visibleData: {
    bol: false,
    data: undefined,
  },
});

export function FileSettingProvider({ children }: { children: ReactNode }) {
  const {
    isOpen: collobrationOpen,
    open: openCollaboration,
    close: closeCollaboration,
    toggle: toggleCollaboration,
  } = useDisclosure(false);
  const [popupMoveAndCopyData, setPopupMoveAndCopyData] = useState<VisibleDataModel>({
    bol: false,
    data: undefined,
  });
  const [openPopup, setOpenPopup] = useState(false);
  const [refreshFileList, setRefreshFileList] = useState(0);
  const [actionProp, setActionProp] = useState<ActionSheetProps>({ actions: [] });
  const [guid, setGuid] = useState('');
  const [fileData, setFileData] = useState<FileDataModel | null>(null);
  const mainDivRef = useRef(null);

  const goRefreshFileList = useCallback(() => {
    setRefreshFileList((num) => num + 1);
  }, []);

  const handleOpenCollaboration = useCallback(
    (guid: string) => {
      setGuid(guid);
      openCollaboration();
    },
    [openCollaboration],
  );

  const { getActionsProp } = useActionsProp(setOpenPopup, goRefreshFileList, handleOpenCollaboration);

  const updateActionProp = useCallback(
    (fileData: FileDataModel, selectedType: string = 'Desktop') => {
      setActionProp(
        getActionsProp({
          fileData,
          selectedType,
          pageType: location.pathname.replace('/', ''),
        }) as ActionSheetProps,
      );
      setFileData(fileData);
    },
    [getActionsProp],
  );

  const providerData = useMemo(
    () => ({
      setOpenPopup,
      setActionProp,
      updateActionProp,
      refreshFileList,
      goRefreshFileList,
      handleOpenCollaboration,
      closeCollaboration,
      toggleCollaboration,
      mainDivRef,
      fileData,
      setFileData,
      setVisibleData: setPopupMoveAndCopyData,
      visibleData: popupMoveAndCopyData,
    }),
    [
      popupMoveAndCopyData,
      setPopupMoveAndCopyData,
      refreshFileList,
      goRefreshFileList,
      handleOpenCollaboration,
      closeCollaboration,
      toggleCollaboration,
      fileData,
      setFileData,
      updateActionProp,
    ],
  );

  const onClose = useCallback(() => {
    setOpenPopup(false);
  }, []);

  const onAction = (action: Action) => {
    if (action.key === 'move' || action.key === 'copy') {
      setPopupMoveAndCopyData({
        bol: true,
        data: undefined,
        key: action.key,
      });
    }
  };

  return (
    <FileSettingContext.Provider value={providerData}>
      <ActionSheet
        className={style.actionSheet}
        getContainer={mainDivRef.current}
        visible={openPopup}
        onAction={onAction}
        onClose={onClose}
        {...actionProp}
      />
      {children}
      <CollaborationShareMobile
        close={closeCollaboration}
        guid={guid}
        toggle={toggleCollaboration}
        visible={collobrationOpen}
      />
      <PopupMoveAndCopy />
    </FileSettingContext.Provider>
  );
}
