import { But<PERSON>, Popup, Toast } from 'antd-mobile';
import type { ToastHandler } from 'antd-mobile/es/components/toast';
import { useCallback, useContext, useEffect, useMemo, useRef } from 'react';

import { createFolder } from '@/api/Create';
import { duplicateBatch, files } from '@/api/File';
import { FileEmpty } from '@/components-mobile/FileEmpty';
import { ItemFolder, withFileListHOC } from '@/components-mobile/FileList';
import { FileHeader } from '@/components-mobile/FileList/Header';
import CreateFileModal from '@/components-mobile/Modal';
import { FileSettingContext } from '@/contexts/FileSetting';
import { usePopupMoveAndCopy } from '@/hooks/usePopupMoveAndCopy';
import type { FileDataModel, FileItemProps } from '@/model/FileList';
import { fm, fm2 } from '@/modules/Locale';
import style from '@/pages/mobile/Desktop/index.less';

import styled from './index.less';

export const PopupMoveAndCopy = () => {
  const text = {
    folder: fm('search.folder'),
    areCreating: fm('TempLatePicker.areCreating'),
    checkFolder: fm('FilePathPicker.selectFolder'),
    cancel: fm('SiderMenu.cancel'),
    delete: fm('File.delete'),
    moveHere: fm('FilePathPicker.moveHere'),
    createACopyHere: fm('File.createACopyHere'),
    createFolder: fm('FilePathPicker.createFolder'),
    infoSuccess: fm('Space.infoSuccess'),
    createFailed: fm('AddTemplatePop.createFailed'),
    loading: fm('Loading.loading'),
    moveFailed: fm('FilePathPicker.moveFailed'),
    ok: fm('Editor.ok'),
    noFolder: fm('FilePathPicker.noFolder'),
  };
  const careteHandlerLoading = useRef<ToastHandler>();

  const { fileData, visibleData, setVisibleData, goRefreshFileList: refreshFileList } = useContext(FileSettingContext);

  const {
    objCache,
    changeObjCache,
    moveFile,
    checkMoveAuth,
    filterFileName,
    guid,
    cancel,
    fileList,
    getAllSpacesList,
  } = usePopupMoveAndCopy();

  const getFileList = useCallback(() => {
    careteHandlerLoading.current = Toast.show({
      icon: 'loading',
      content: `${text.loading}...`,
    });
    return new Promise((resolve, reject) => {
      files?.({
        folder: guid === 'Desktop' ? undefined : guid,
      })
        .then((res) => {
          if (res.status === 200) {
            const { list = [] } = res.data;
            changeObjCache(
              visibleData.data!.guid!,
              visibleData.data,
              list?.filter((item: FileDataModel) => item.isFolder) || [],
            );
            resolve(true);
          }
        })
        .finally(() => {
          careteHandlerLoading.current?.close();
        })
        .catch(() => {
          reject();
        });
    });
  }, [text.loading, visibleData.data, guid]);

  const clearFileNode = (
    <div>
      <Button fill="none" style={{ padding: 0 }} onClick={() => cancel(true)}>
        {text.cancel}
      </Button>
    </div>
  );

  const clickItem = useCallback(
    (data: FileDataModel) => {
      changeObjCache(data.guid!, data, objCache.get(data.guid!)?.list || []);
      setVisibleData((preData) => {
        return {
          ...preData,
          data,
        };
      });
    },
    [setVisibleData, visibleData.key, objCache],
  );

  useEffect(() => {
    if (visibleData.key) {
      if (visibleData.data?.guid) {
        if (objCache.get(visibleData.data!.guid!)?.list.length) return;
        getFileList();
      } else {
        getAllSpacesList();
      }
    }
  }, [visibleData.data, visibleData.key, getAllSpacesList, getFileList, refreshFileList]);

  const apiFetch = useCallback(
    async (api: () => Promise<void>, callBack?: () => void) => {
      careteHandlerLoading.current = Toast.show({
        icon: 'loading',
        content: `${text.areCreating}...`,
      });
      try {
        await api();
        careteHandlerLoading.current?.close();
        Toast.show({
          icon: 'success',
          content: text.infoSuccess,
        });
        callBack?.();
      } catch (error) {
        careteHandlerLoading.current?.close();
        Toast.show({
          icon: 'fail',
          content: text.createFailed,
        });
      }
    },
    [text.areCreating, text.createFailed, text.infoSuccess],
  );

  const handleCreateFolder = useCallback(() => {
    CreateFileModal.show({
      type: 'input',
      title: text.createFolder,
      onConfirm: (filename) => {
        apiFetch(
          async () => {
            await createFolder(filename, guid!);
          },
          () => getFileList(),
        );
      },
    });
  }, [guid, apiFetch, text.createFolder, getFileList]);
  const goDuplicateBatch = useCallback(async () => {
    apiFetch(
      async () => {
        await duplicateBatch({
          files: [
            {
              guid: fileData?.guid || '',
              name: filterFileName(fileData?.name || '') || '',
            },
          ],
          folder: guid || '',
        });
      },
      () => cancel(true),
    );
  }, [guid, cancel, apiFetch, fileData?.guid, filterFileName, fileData?.name]);
  const EnhancedFileList = withFileListHOC<FileItemProps>((data) => <ItemFolder {...data} isHideTextTime />);

  const handleMove = useCallback(async () => {
    const { tip, auth } = checkMoveAuth({
      isAdmin: fileData?.isAdmin || fileData?.isFileAdmin || false,
      curFolder: visibleData?.data,
      role: fileData?.role || '',
      sourceFileParentGuid: fileData?.parent_guid || '',
    });
    if (tip) {
      CreateFileModal.show({
        title: text.moveFailed,
        renderContent: () => <div style={{ textAlign: 'center', marginTop: 10, fontSize: 14 }}>{tip}</div>,
        showCancelBtn: false,
        renderConfirmContent: () => text.ok,
      });
      return;
    }
    if (!auth) return;
    await moveFile(guid!, fileData!.guid!, () => {
      cancel(true);
      refreshFileList();
      Toast.show({
        icon: 'success',
        content: fm2('FilePathPicker.moveSuccess', { folderName: visibleData.data!.name! }),
      });
    });
  }, [cancel, refreshFileList, visibleData.data, fileData, guid, text.moveFailed, checkMoveAuth, text.ok, moveFile]);

  const disabled = useMemo(() => {
    if (visibleData.data?.guid === 'Desktop' && fileData?.permissionsAndReasons?.canRead?.value) {
      return false;
    }
    return !(
      fileData?.permissionsAndReasons?.canRead?.value && visibleData.data?.permissionsAndReasons?.canEdit?.value
    );
  }, [
    visibleData.data?.guid,
    fileData?.permissionsAndReasons?.canRead?.value,
    visibleData.data?.permissionsAndReasons?.canEdit?.value,
  ]);
  return (
    <Popup bodyStyle={{ height: '100vh' }} visible={visibleData.bol}>
      <div className={style.listBox}>
        <FileHeader
          back={() => cancel()}
          data={visibleData.data || { name: text.checkFolder }}
          defaultRight={clearFileNode}
          guid="1"
        />
        <div className={style.listBoxVirtualized}>
          <EnhancedFileList
            clickItem={clickItem}
            isBottomPadding={false}
            list={fileList}
            noRowsRenderer={() => <FileEmpty text={text.noFolder} />}
            selectedType="delete"
          />
        </div>
        {visibleData.data?.guid && careteHandlerLoading.current && (
          <div className={styled.footer}>
            <Button
              className="sm-btn sm-btn-large sm-btn-normal-primary"
              disabled={disabled}
              onClick={handleCreateFolder}
            >
              {text.createFolder}
            </Button>
            {visibleData.key === 'copy' && (
              <Button
                className="sm-btn sm-btn-large sm-btn-normal-primary"
                disabled={disabled}
                onClick={goDuplicateBatch}
              >
                {text.createACopyHere}
              </Button>
            )}

            {visibleData.key === 'move' && (
              <Button className="sm-btn sm-btn-large sm-btn-normal-primary" disabled={disabled} onClick={handleMove}>
                {text.moveHere}
              </Button>
            )}
          </div>
        )}
      </div>
    </Popup>
  );
};
