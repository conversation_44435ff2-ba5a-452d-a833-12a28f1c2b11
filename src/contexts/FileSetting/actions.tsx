import { Ellipsis } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { useCallback } from 'react';

import { setSpacePin, setSpaceUnPin } from '@/api/Space';
import { getPositioning } from '@/components/fileList';
import { CLOUD_FILE_TYPES_SUPPORT_IMPORT_EDIT } from '@/constants/fileTypes';
import { getFileTypeName, useFileMobileActions } from '@/hooks/useFileMobileActions';
import { useMenuItem } from '@/hooks/useMenuItem';
import type { FileDataModel } from '@/model/FileList';
import { fm2, useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

type MyAction = Action & { show: boolean };
export function useActionsProp(
  setOpenPopup: (bol: boolean) => void,
  goRefreshFileList: () => void,
  handleOpenCollaboration: (guid: string) => void,
) {
  const {
    saveTemplate,
    renameFile,
    collectionStar,
    deleteFileFunc,
    permanentlyDeleteFileFunc,
    downloadFileFunc,
    clearLastRecord,
    exitShare,
    recoveryFileFunc,
    downloadEditFileFunc,
    addMobileQuickAccess,
    removeMobileQuickAccess,
    convertToOnlineFile,
  } = useFileMobileActions();
  const { getContextMenuItems } = useMenuItem([
    'QuickAccess',
    'favorite',
    'ShareCollaboration',
    'copy',
    'clearLastRecord',
  ]);
  const meId = useMeStore((state) => state.me.id);
  const teamRole = useMeStore((state) => state.me.teamRole);
  const moDisDownTypes = ['form', 'table', 'board', 'shortcut', 'folder'];
  const moDownTypes = ['newdoc', 'presentation', 'mosheet', 'modoc', 'mindmap'];
  const getDownloadHidden = useCallback((record: FileDataModel) => {
    let type = '';
    const selectedType = record.type;
    if (moDownTypes.includes(selectedType)) {
      //单选 且 石墨文档可下载类型
      type = 'moType';
    } else if (!moDisDownTypes.includes(selectedType)) {
      //单选 且 非文件夹类型 且 排除石墨文档不可下载文件夹类型 （云文件类型=>自主上传的文件的类型）
      type = 'cloudType';
    }
    return type;
  }, []);

  const i18nText = {
    reName: useFormatMessage('File.reName'),
    locateToFolder: useFormatMessage('RightclickMouse.locateToFolder'),
    locateToSpace: useFormatMessage('RightclickMouse.locateToSpace'),
  };

  const getActionsProp = useCallback(
    ({
      fileData, //文件数据
      selectedType, //文件类型
      pageType, //页面类型
    }: {
      fileData: FileDataModel;
      selectedType: string;
      pageType: 'share' | 'space' | 'recent' | 'favorites' | 'created' | 'desktop' | string;
    }) => {
      const typeName = fileData.isSpace
        ? fm2('SiderMenu.siderMenuSpaceText')
        : fileData.type === 'folder'
          ? fm2('SiderMenu.siderMenuCreateFolderText')
          : getFileTypeName(fileData.type);
      let actions: MyAction[] = [];
      switch (selectedType) {
        // trash 回收站
        case 'trash':
          actions = [
            {
              text: fm2('Trash.recover', { name: typeName }),
              key: 'recover',
              show: true,
              onClick: () => {
                setOpenPopup(false);
                recoveryFileFunc(fileData.guid).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              text: fm2('File.deleteCompletely'),
              key: 'delete',
              danger: true,
              bold: true,
              show: true,
              onClick: () => {
                setOpenPopup(false);
                permanentlyDeleteFileFunc({
                  name: typeName,
                  guid: fileData.guid,
                }).then(() => {
                  goRefreshFileList();
                });
              },
            },
          ];
          break;

        default:
          actions = [
            {
              //重命名
              text: i18nText.reName,
              key: 'reName',
              show: true,
              onClick: () => {
                setOpenPopup(false);
                renameFile(fileData).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              // 定位到所在文件夹 | 打开空间
              text: fileData.isSpace ? i18nText.locateToSpace : i18nText.locateToFolder,
              key: 'locateToFolder',
              show: ['recent', 'share', 'favorites'].includes(pageType),
              onClick: () => {
                setOpenPopup(false);
                getPositioning(fileData);
              },
            },
            {
              //分享协作
              text: fm2('ShareCollaboration.title'),
              key: 'ShareCollaboration',
              show: !fileData.isShortcut,
              onClick: () => {
                setOpenPopup(false);
                handleOpenCollaboration(fileData.guid);
              },
            },
            {
              //收藏
              text: fileData.starred ? fm2('Header.unfavorite') : fm2('Header.favorite'),
              key: 'favorite',
              show: !fileData.isSpace,
              onClick: () => {
                setOpenPopup(false);
                collectionStar({ guid: fileData.guid, isStar: fileData.starred }).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              text: fm2('File.move'),
              key: 'move',
              show: !fileData.isSpace && (teamRole === 'teamRole' || Boolean(fileData.isFileAdmin)),
              onClick: () => {
                setOpenPopup(false);
              },
            },
            {
              //创建副本  文件夹 或者团队空间 不显示
              text: fm2('File.copy'),
              show: !(fileData.isFolder || fileData.isSpace) && !!fileData?.permissionsAndReasons?.canRead?.value,
              key: 'copy',
              onClick: () => {
                setOpenPopup(false);
              },
            },
            {
              //保存为模版  仅编辑器显示
              text: fm2('Editor.saveTemplate'),
              show: ['tables/', 'docx', 'docs/', 'presentation/', 'boards/', 'sheets/', 'forms/'].some((item) =>
                pageType.startsWith(item),
              ),
              key: 'template',
              onClick() {
                setOpenPopup(false);
                saveTemplate(fileData).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              // 转为在线文件
              text: fm2('Editor.toOnlineFile'),
              show: CLOUD_FILE_TYPES_SUPPORT_IMPORT_EDIT.some((item) => fileData.name.endsWith(item)),
              key: 'template',
              onClick: () => {
                setOpenPopup(false);
                convertToOnlineFile(fileData, () => {
                  goRefreshFileList();
                });
              },
            },
            {
              //编辑器下载
              text: `${fm2('File.download')}`,
              key: 'download',
              show: selectedType === 'editor' && !['form', 'board'].includes(fileData.type),
              onClick: () => {
                setOpenPopup(false);
                downloadEditFileFunc(fileData);
              },
            },
            {
              //列表下载
              text: fm2('File.download'),
              key: 'download',
              show: selectedType !== 'editor' && !['space'].includes(pageType) && getDownloadHidden(fileData) !== '',
              onClick: () => {
                setOpenPopup(false);
                downloadFileFunc(getDownloadHidden(fileData), fileData);
              },
            },
            {
              //清除最近使用记录  仅最近使用显示
              text: fm2('File.clearLastRecord'),
              key: 'clearLastRecord',
              show: ['recent'].includes(pageType),
              onClick: () => {
                setOpenPopup(false);
                clearLastRecord(fileData).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              //快速访问 排除团队空间类型
              text: fileData.hasDesktopShortcut ? fm2('QuickAccess.remove') : fm2('QuickAccess.add'),
              key: 'QuickAccess',
              show: !fileData.isSpace,
              onClick: () => {
                const api = fileData.hasDesktopShortcut ? removeMobileQuickAccess : addMobileQuickAccess;
                api(fileData).then(() => {
                  setOpenPopup(false);
                  goRefreshFileList();
                });
              },
            },
            {
              //置顶
              text: fm2('Space.top'),
              key: 'top',
              show: ['space'].includes(pageType),
              onClick: () => {
                setOpenPopup(false);
                const api = fileData.isDesktop ? setSpaceUnPin : setSpacePin;
                api(fileData.guid).then(() => {
                  setOpenPopup(false);
                  goRefreshFileList();
                });
              },
            },
            {
              //退出共享 仅共享的时候显示
              text: fm2('File.exitShare'),
              key: 'exitShare',
              danger: true,
              bold: true,
              show: ['share'].includes(pageType) && !!fileData.permissionsAndReasons?.canExit.value,
              onClick: () => {
                setOpenPopup(false);
                exitShare({ guid: fileData.guid, name: fileData.name, type: fileData.type }).then(() => {
                  goRefreshFileList();
                });
              },
            },
            {
              //删除
              text: fm2('File.delete'),
              key: 'delete',
              danger: true,
              bold: true,
              show: fileData.permissionsAndReasons?.canManageAdmin?.value || meId === fileData.userId,
              onClick: () => {
                setOpenPopup(false);
                deleteFileFunc({
                  isSpace: fileData.isSpace,
                  guid: fileData.guid,
                  name: fileData.name,
                  type: fileData.type,
                }).then(() => {
                  goRefreshFileList();
                });
              },
            },
          ];
          break;
      }

      return {
        actions: getContextMenuItems(actions, fileData).filter((it: MyAction) => it.show),
        cancelText: fm2('Space.cancel'),
        extra: <Ellipsis content={fileData.name} direction="end" />,
      };
    },
    [
      clearLastRecord,
      collectionStar,
      convertToOnlineFile,
      deleteFileFunc,
      downloadEditFileFunc,
      downloadFileFunc,
      exitShare,
      getDownloadHidden,
      goRefreshFileList,
      handleOpenCollaboration,
      meId,
      permanentlyDeleteFileFunc,
      recoveryFileFunc,
      renameFile,
      setOpenPopup,
    ],
  );

  return {
    getActionsProp,
  };
}
