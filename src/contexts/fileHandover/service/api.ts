import type { HandoverStatusEnum, PageType } from '../type';
import { baseURL } from './constant';

/**
 * 获取文件交接状态
 * @param token
 * @returns
 */
export const getFileTransferStatus = async (token: string, adminPage: boolean) => {
  const response = await fetch(`${baseURL}/handover/files/list?adminPage=${adminPage}`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  if (response.ok) {
    const data = await response.json();
    return data;
  } else {
    return Promise.reject(response);
  }
};

/**
 * 根据token获取被交接人的信息
 * @param token
 * @returns
 */
export const getTransfereeInfo = async (token: string) => {
  const response = await fetch(`${baseURL}/users/me`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  const data = await response.json();
  return data;
};

/**
 * 结束交接
 */
export const deleteHandover = async (token: string) => {
  const response = await fetch(`${baseURL}/handover/tokens/${token}`, {
    method: 'DELETE',
    headers: {
      'X-Shimo-Handover-Token': token,
    },
  });

  if (response.ok) {
    return Promise.resolve(response);
  } else {
    return Promise.reject(response);
  }
};

/**
 * 获取文件夹子文件
 * @param guid
 * @returns
 */
export const getChildrenFiles = async (props: {
  guid: string;
  token: string;
  leftUser: number;
  pageType: PageType;
  currentFolderStatus: HandoverStatusEnum;
}) => {
  const { guid, token, leftUser, pageType, currentFolderStatus } = props;
  const response = await fetch(
    `${baseURL}/handover/files/${guid}?check=true&leftUser=${leftUser}&pageType=${pageType}&currentFolderStatus=${currentFolderStatus}`,
    {
      headers: {
        'X-Shimo-Handover-Token': token,
        'Cache-Control': 'no-cache',
      },
    },
  );
  const data = await response.json();
  return data;
};

/**
 * 获取单个文件权限
 */
export const getFileManagement = async (token: string, guid: string) => {
  const response = await fetch(`${baseURL}/handover/files/${guid}/action/handover_admin`, {
    method: 'POST',
    headers: {
      'X-Shimo-Handover-Token': token,
      'Content-Type': 'application/json',
    },
  });

  if (response.ok) {
    return Promise.resolve(response);
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取多个文件权限
 */
export const getFilesManagement = async (token: string, guids: string[]) => {
  const response = await fetch(`${baseURL}/lizard-api/handover/files/action/handover_admin`, {
    method: 'POST',
    headers: {
      'X-Shimo-Handover-Token': token,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      guids,
    }),
  });

  if (response.ok) {
    return Promise.resolve(response);
  } else {
    return Promise.reject(response);
  }
};

/**
 * 查询批量交接进度
 */
export const getBatchHandoverProgress = async (token: string) => {
  const response = await fetch(`${baseURL}/handover/files/progress`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  const data = await response.json();
  return data;
};

/**
 * 预览摘要
 */
export const getPreviewSummary = async (token: string, guid: string) => {
  const response = await fetch(`${baseURL}/handover/files/${guid}/preview`, {
    headers: {
      'X-Shimo-Handover-Token': token,
    },
  });

  const data = await response.json();
  return data;
};
