export interface FileHandoverState {
  files: HandoverFile[];
  flattenFiles: HandoverFile[];
  treeFiles: TreeFile;
  currentFolder: TreeFile;
  transferee: User;
  invalidToken: boolean;
  tableLoading: boolean;
  loading: boolean;
  batchHandover: boolean;
  sameCompany: boolean;
  tokenExpiry: string;
}

export interface TreeFile extends HandoverFile {
  children?: Array<TreeFile>;
}

export type Action =
  | {
      type: 'setState';
      state: FileHandoverState;
    }
  | {
      type: 'setFiles';
      files: HandoverFile[];
      guid: string;
    }
  | {
      type: 'backFolderGuid';
      guid: string;
    }
  | {
      type: 'setInvalidToken';
      invalid: boolean;
    }
  | {
      type: 'setLoading';
      loading: boolean;
    }
  | {
      type: 'setTableLoading';
      tableLoading: boolean;
    }
  | {
      type: 'updateFilesInfo';
      files: HandoverFile[];
    }
  | {
      type: 'setBatchHandover';
      status: boolean;
    }
  | {
      type: 'setSameCompany';
      status: boolean;
    };

export enum HandoverStatusEnum {
  /**
   * 已删除
   */
  DELETED = 3,
  /**
   * 已交接
   */
  TRANSFERRED = 2,
  /**
   * 待交接
   */
  TO_BE_TRANSFERRED = 1,
  /**
   * 未交接
   */
  NOT_TRANSFERRED = 0,
}

export interface HandoverFile {
  id: number;
  token: string;
  name: string;
  guid: string;
  handoverStatus: HandoverStatusEnum;
  handoverUsers?: string;
  type: string;
  userList: {
    id: number;
    name: string;
    avatar: string;
    email: string;
  }[];
  parentGuid?: string;
  isSpace?: boolean;
  subType?: number;
}

export interface User {
  id: number;
  name: string;
  avatar: string;
}

export enum TabBarEnum {
  /**
   * 全部文件
   */
  ALL_FILES = 'ALL_FILES',
  /**
   * 已交接文件
   */
  TRANSFERRED_FILES = 'TRANSFERRED_FILES',
  /**
   * 待交接文件
   */
  TO_BE_TRANSFERRED_FILES = 'TO_BE_TRANSFERRED_FILES',
  /**
   * 未交接文件
   */
  NOT_TRANSFERRED_FILES = 'NOT_TRANSFERRED_FILES',
}

/**
 * 页面进入模式类型
 */
export enum PageType {
  /**
   * 0:普通用户进入交接页面
   */
  HANDOVER = 0,
  /**
   * 1:管理员进入交接管理页面
   */
  MANAGEMENT_HANDOVER = 1,
  /**
   * 2:发起交接页面
   */
  INITIATE_HANDOVER = 2,
}
