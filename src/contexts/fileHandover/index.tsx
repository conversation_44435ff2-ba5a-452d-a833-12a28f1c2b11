import dayjs from 'dayjs';
import type { ReactNode } from 'react';
import React, { createContext, useCallback, useEffect, useReducer } from 'react';

import { getBatchHandoverProgress, getFileTransferStatus, getTransfereeInfo } from '@/api/handover';
import {
  type Action,
  type FileHandoverState,
  type HandoverFile,
  HandoverStatusEnum,
  type User,
} from '@/model/fileHandover';
import { fm2 } from '@/modules/Locale';
import useManage from '@/pages/pc/Members/components/HandoverModal/hooks/useManage';

import { handoverReducer } from './reducer';

export const FileHandoverContext = createContext<FileHandoverState | null>(null);
export const FileHandoverDispatchContext = createContext<null | React.Dispatch<Action>>(null);

/**
 * 默认id
 * 最外层
 */
export const DEFAULT_ID = -999;

/**
 * 默认guid
 * 最外层
 */
export const DEFAULT_GUID = 'root';

const s18nText = {
  allFiles: fm2('hanover.allFiles'),
};

const initialFileHandover: FileHandoverState = {
  files: [],
  flattenFiles: [],
  // 最外层结构需要手动构建
  currentFolder: {
    id: DEFAULT_ID,
    guid: DEFAULT_GUID,
    type: '',
    name: s18nText.allFiles,
    token: '',
    handoverStatus: HandoverStatusEnum.NOT_TRANSFERRED,
    parentGuid: DEFAULT_GUID,
    userList: [],
  },
  treeFiles: {
    children: [],
    id: DEFAULT_ID,
    guid: DEFAULT_GUID,
    type: '',
    name: s18nText.allFiles,
    token: '',
    handoverStatus: HandoverStatusEnum.NOT_TRANSFERRED,
    userList: [],
  },
  transferee: {
    id: DEFAULT_ID,
    name: '',
    avatar: '',
  },
  invalidToken: false,
  tableLoading: false,
  loading: false,
  batchHandover: false,
  sameCompany: true,
  tokenExpiry: '',
};

export const FileHandoverProvider: React.FC<{
  children: ReactNode;
  token: string;
}> = ({ children, token }) => {
  const manage = useManage();
  const [fileHandover, dispatch] = useReducer(handoverReducer, initialFileHandover);

  const init = useCallback((token: string, manage: boolean) => {
    Promise.all([getFileTransferStatus(token, manage), getTransfereeInfo(token)])
      .then((value: [{ list: HandoverFile[]; tokenExpiry: string }, User]) => {
        const [files, user] = value;
        const { list, tokenExpiry } = files;

        const date = dayjs(tokenExpiry);
        const formattedDateTime = date.format('YYYY-MM-DD HH:mm');

        dispatch({
          type: 'setState',
          state: {
            ...initialFileHandover,
            files: list,
            flattenFiles: [initialFileHandover.currentFolder, ...list],
            currentFolder: initialFileHandover.currentFolder,
            treeFiles: {
              ...initialFileHandover.treeFiles,
              children: list,
            },
            transferee: user,
            tokenExpiry: formattedDateTime,
          },
        });
      })
      .catch(() => {
        dispatch({
          type: 'setInvalidToken',
          invalid: true,
        });
      })
      .finally(() => {
        dispatch({
          type: 'setLoading',
          loading: false,
        });
      });
  }, []);

  useEffect(() => {
    dispatch({
      type: 'setLoading',
      loading: true,
    });
    let timer: NodeJS.Timeout;
    if (token) {
      timer = setInterval(() => {
        getBatchHandoverProgress(token).then((res) => {
          if (res?.progress === 100 || manage) {
            clearInterval(timer);
            dispatch?.({
              type: 'setBatchHandover',
              status: false,
            });
            init(token, manage);
          } else if (res.errorCode === 0) {
            dispatch({
              type: 'setLoading',
              loading: false,
            });
            clearInterval(timer);
            dispatch?.({
              type: 'setInvalidToken',
              invalid: true,
            });
          } else if (res.errorCode === 17003) {
            dispatch({
              type: 'setLoading',
              loading: false,
            });
            clearInterval(timer);
            dispatch?.({
              type: 'setSameCompany',
              status: false,
            });
          } else if (res?.errorCode === 40021) {
            clearInterval(timer);
            location.href = '/forbidden';
          } else if (res?.progress === 0) {
            dispatch({
              type: 'setLoading',
              loading: false,
            });
            dispatch?.({
              type: 'setBatchHandover',
              status: true,
            });
          }
        });
      }, 2000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [token, manage, init]);

  return (
    <FileHandoverContext.Provider value={fileHandover}>
      <FileHandoverDispatchContext.Provider value={dispatch as any}>{children}</FileHandoverDispatchContext.Provider>
    </FileHandoverContext.Provider>
  );
};
