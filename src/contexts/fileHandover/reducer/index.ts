import type { Action, FileHandoverState, HandoverFile, TreeFile } from '@/model/fileHandover';

import { DEFAULT_GUID } from '../index';

/**
 * 将交接列表插入到交接树
 */
function insertFilesInTree(files: HandoverFile[], treeFile: TreeFile, guid: string) {
  if (treeFile.children) {
    const guidChildren = treeFile.children.map((child) => child.guid);
    const index = guidChildren.findIndex((g) => g === guid);
    if (index > -1) {
      treeFile.children[index].children = files;
    } else {
      treeFile.children.map((child) => (child.children ? insertFilesInTree(files, child, guid) : null));
    }
  }
}

/**
 * 从交接树查找某一个guid的父级
 */
function findParentByGuid(treeFile: TreeFile, guid: string): TreeFile | undefined {
  if (guid === treeFile.guid) {
    return treeFile;
  } else {
    if (treeFile.children) {
      const children = treeFile.children.find((child) => child.guid === guid);
      if (children) {
        return children;
      } else {
        const foundParent = treeFile.children.reduce((acc, child) => {
          if (child.children) {
            const found = findParentByGuid(child, guid);
            if (found) {
              acc.push(found);
            }
          }
          return acc;
        }, [] as TreeFile[]);

        if (foundParent.length > 0) {
          return foundParent[0];
        }
        return undefined;
      }
    } else {
      return undefined;
    }
  }
}

/**
 * 从文件树查找某一个guid的children
 */
function findChildrenByGuid(treeFile: TreeFile, guid: string): TreeFile[] | undefined {
  if (guid === treeFile.guid) {
    return treeFile.children;
  } else {
    if (treeFile.children) {
      const children = treeFile.children.find((child) => child.guid === guid);
      if (children) {
        return children.children;
      } else {
        const foundChildren = treeFile.children.reduce((acc, child) => {
          if (child.children) {
            const found = findChildrenByGuid(child, guid);
            if (found) {
              acc.push(...found);
            }
          }
          return acc;
        }, [] as TreeFile[]);

        if (foundChildren.length > 0) {
          return foundChildren;
        }
        return undefined;
      }
    } else {
      return undefined;
    }
  }
}

/**
 * 数组去重
 * list2的同guid项覆盖list1
 */
function unique(list1: HandoverFile[], list2: HandoverFile[]) {
  return Array.from(new Set([...list1, ...list2].map((file) => file.guid))).map((guid) => {
    // 在合并后的数组中查找文件
    const fileInList = list2.find((file) => file.guid === guid);
    const fileInFlattenFiles = list1.find((file) => file.guid === guid);

    // 如果文件在两个数组中都存在，只添加一次
    if (fileInList && fileInFlattenFiles) {
      return fileInList;
    } else if (fileInList) {
      return fileInList;
    } else {
      return fileInFlattenFiles;
    }
  });
}

export function handoverReducer(handover: FileHandoverState, action: Action): any {
  switch (action.type) {
    case 'setState': {
      const { flattenFiles: preFlattenFile } = action.state;
      const flattenFiles = preFlattenFile.map((file) => {
        if (file.guid !== DEFAULT_GUID) {
          file.parentGuid = DEFAULT_GUID;
        }
        return file;
      });
      return {
        ...action.state,
        flattenFiles,
      };
    }
    case 'setFiles': {
      const { files, guid } = action;
      const { treeFiles } = handover;
      const flattenFile = files.map((file) => {
        file.parentGuid = guid;
        return file;
      });
      const newFlattenFiles = unique(handover.flattenFiles, flattenFile);
      insertFilesInTree(files, treeFiles, guid);

      return {
        ...handover,
        files: action.files,
        flattenFiles: newFlattenFiles,
        treeFiles,
        currentFolder: newFlattenFiles.find((file) => file?.guid === guid),
      };
    }
    case 'backFolderGuid': {
      const { guid } = action;
      const { treeFiles, flattenFiles } = handover;
      const files = findChildrenByGuid(treeFiles, guid);
      const currentFolder = flattenFiles.find((file) => file.guid === guid);

      return {
        ...handover,
        files: files ?? [],
        currentFolder,
      };
    }
    case 'setInvalidToken': {
      const { invalid } = action;
      return {
        ...handover,
        invalidToken: invalid,
      };
    }
    case 'setLoading': {
      const { loading } = action;
      return {
        ...handover,
        loading,
      };
    }
    case 'setTableLoading': {
      const { tableLoading } = action;
      return {
        ...handover,
        tableLoading,
      };
    }
    case 'setSameCompany': {
      const { status } = action;
      return {
        ...handover,
        sameCompany: status,
      };
    }
    case 'updateFilesInfo': {
      const { files: list } = action;
      const { flattenFiles: preFlattenFiles, treeFiles, currentFolder } = handover;
      const flattenFiles = list.map((file: HandoverFile) => {
        file.parentGuid = currentFolder.parentGuid;
        return file;
      });

      const newFlattenFiles = unique(preFlattenFiles, flattenFiles);

      const newFileItem = findParentByGuid(treeFiles, currentFolder.guid);
      if (newFileItem) {
        newFileItem.children = list;
      }
      currentFolder.children = list;
      return {
        ...handover,
        files: [...list],
        currentFolder,
        flattenFiles: newFlattenFiles,
        treeFiles,
      };
    }

    case 'setBatchHandover': {
      const { status } = action;

      return {
        ...handover,
        batchHandover: status,
      };
    }

    default: {
      throw Error(`Unknown action: ${JSON.stringify(action)}`);
    }
  }
}
