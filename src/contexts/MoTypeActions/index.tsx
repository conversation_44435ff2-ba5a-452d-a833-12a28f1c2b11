import { ActionSheet } from 'antd-mobile';
import { createRoot } from 'react-dom/client';

import { exportTable } from '@/api/File';
import { to } from '@/api/Request';
import type { FileDataModel } from '@/model/FileList';
import { fm2 } from '@/modules/Locale';
import { downloadFile } from '@/utils/file';
import { getSMEditor } from '@/utils/ShimoSDK';
import { sanitizeFilename } from '@/utils/tools';

interface Action {
  text?: string;
  label: string;
  key: string;
  hidden?: boolean;
  onClick?: () => void;
}

interface MoTypeActionsProp {
  type: string;
  open: boolean;
  guid: string;
  name: string;
  fileData: FileDataModel;
  from?: string;
  close: () => void;
  downloadDiffFile: (params: { type: string; guid: string; from: string; fileName: string }) => void;
  getSecondActions?: (type: string) => Action[];
  getEditSecondActions?: (type: string) => Action[];
}

const MoTypeActions = ({
  type,
  open,
  guid,
  name,
  fileData,
  from = 'list',
  close,
  downloadDiffFile,
  getSecondActions,
  getEditSecondActions,
}: MoTypeActionsProp) => {
  const actionsProp = (
    getSecondActions ? getSecondActions(type) : getEditSecondActions ? getEditSecondActions(type) : []
  )
    .filter((it: Action) => it.hidden !== true)
    .map((item: Action) => {
      return {
        ...item,
        text: item.label,
        onClick: async () => {
          close();
          const exportType = item.key;
          if (from === 'editor') {
            if (type === 'modoc' && ['imagePdf', 'jpg'].includes(exportType)) {
              getSMEditor()
                ?.docsApi.pluginManager.getInstance('Paint')
                .exporter.export(exportType === 'jpg' ? 'image' : exportType);
              return;
            }

            if (type === 'mosheet' && ['pdf', 'jpg'].includes(exportType)) {
              if (exportType === 'pdf') {
                getSMEditor()?.exportPdf?.();
              } else {
                getSMEditor()?.exportImage?.();
              }
              return;
            }
            if (type === 'presentation' && ['imagePdf', 'jpg'].includes(exportType)) {
              getSMEditor()?.export?.(exportType === 'jpg' ? 'image' : exportType);
              return;
            }
            if (type === 'table') {
              const [, res] = await to(exportTable(guid));
              if (res?.status === 200) {
                downloadFile(res.data.downloadUrl, `${sanitizeFilename(fileData.name)}`);
              }
              return;
            }
          }
          downloadDiffFile({ type: exportType, from: 'mobile', guid, fileName: name });
        },
      };
    });

  return (
    <ActionSheet
      actions={actionsProp}
      cancelText={fm2('Space.cancel')}
      extra={fm2('File.downloadFor')}
      visible={open}
      onClose={close}
    />
  );
};

MoTypeActions.show = (props: MoTypeActionsProp) => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);

  const destroy = () => {
    root.unmount();
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  root.render(
    <MoTypeActions
      {...props}
      close={() => {
        destroy();
      }}
      open={true}
    />,
  );
};

export default MoTypeActions;
