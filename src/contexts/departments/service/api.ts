import { CommonApi } from '@/api/Request';

import type { BaseRules, Rule, RuleBaseApiResponse, RulesApiResponse, TeamInfoType } from '../type';
const formatPostRules = (rule: Omit<Rule, 'enable' | 'id'>) => {
  return {
    ...rule,
    owners: rule.owners.map(({ id, type }) => ({
      ownerId: id,
      ownerType: type,
    })),
    targets: rule.targets.map(({ id, type }) => ({
      targetId: id,
      targetType: type,
    })),
  };
};

/**
 * 获取团队信息
 */
export const getTeamInfo = async () => {
  const response = await CommonApi.get(`/teams/mine`);
  if (response.status !== 200) {
    throw new Error('get team info failed');
  }
  const data = response as unknown as TeamInfoType;
  return data;
};

/**
 * 获取基础规则 ----------
 */
export const getBaseRules = async () => {
  const response = await CommonApi.get(`/org/rules/base`);
  const { data } = (await response) as RuleBaseApiResponse;
  return data;
};

/**
 * 修改基础规则
 */
export const putBaseRules = async (params: BaseRules) => {
  const response = await CommonApi.put(`/org/rules/base`, { ...params });
  if (response.status === 204) {
    return Promise.resolve();
  } else {
    return Promise.reject(response);
  }
};

/**
 * 获取扩展规则
 */
export const getExtensionRule = async () => {
  const response = await CommonApi.get(`/org/rules`);
  const {
    data: { rules },
  } = (await response) as RulesApiResponse;
  return rules;
};

/**
 * 修改扩展规则可见性or生效
 */
export const putExtensionRule = async (
  id: number,
  params: {
    enable?: boolean;
    visible?: boolean;
  },
) => {
  const response = await CommonApi.put(`/org/rules/control/${id}`, { ...params });

  if (response.status === 204) {
    return Promise.resolve();
  } else {
    return Promise.reject(response);
  }
};

/**
 * 删除扩展规则
 */
export const deleteExtensionRule = async (id: number) => {
  const response = await CommonApi.delete(`/org/rules/${id}`);

  if (response.status === 204) {
    return Promise.resolve();
  } else {
    return Promise.reject(response);
  }
};

/**
 * 新增规则
 */
export const postExtensionRule = async (rule: Omit<Rule, 'enable' | 'id'>) => {
  const formattedRule = formatPostRules(rule);
  const response = await CommonApi.post(`/org/rules`, { ...formattedRule });
  if (response.status === 200 || response.status === 204) {
    return Promise.resolve();
  } else {
    return Promise.reject(await response);
  }
};

/**
 * 编辑规则
 */
export const putExtensionRuleById = async (id: number, rule: Omit<Rule, 'enable' | 'id'>) => {
  const formattedRule = formatPostRules(rule);
  const response = await CommonApi.put(`/org/rules/${id}`, { ...formattedRule });
  if (response.status === 200 || response.status === 204) {
    return Promise.resolve();
  } else {
    return Promise.reject(response);
  }
};
