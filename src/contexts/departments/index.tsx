import type { ReactNode } from 'react';
import React, { createContext, useEffect, useReducer } from 'react';

import { DepartmentsReducer } from './reducer';
import { getBaseRules, getExtensionRule, getTeamInfo } from './service/api';
import { type Action, type Departments, RulePermission } from './type';

const initialDepartments: Departments = {
  teamInfo: {
    id: 0,
    name: '',
  },
  baseRules: {
    permission: RulePermission.all,
    applyToSearch: false,
    showMemberCount: false,
    showSubDepartments: false,
  },
  extendedRules: [],
  tableLoading: true,
};

export const DepartmentsContext = createContext<Departments>(initialDepartments);
export const DepartmentsDispatchContext = createContext<null | React.Dispatch<Action>>(null);

export const DepartmentsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [Departments, dispatch] = useReducer(DepartmentsReducer, initialDepartments);

  useEffect(() => {
    Promise.all([getTeamInfo(), getBaseRules(), getExtensionRule()]).then(([teamInfo, baseRules, extendedRules]) => {
      dispatch?.({
        type: 'setData',
        payload: {
          teamInfo,
          baseRules,
          extendedRules: extendedRules ?? [],
          tableLoading: false,
        },
      });
    });
  }, []);

  return (
    <DepartmentsContext.Provider value={Departments}>
      <DepartmentsDispatchContext.Provider value={dispatch}>{children}</DepartmentsDispatchContext.Provider>
    </DepartmentsContext.Provider>
  );
};
