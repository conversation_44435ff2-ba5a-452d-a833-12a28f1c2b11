import type { Action, Departments } from '../type';

export function DepartmentsReducer(state: Departments, action: Action) {
  switch (action.type) {
    case 'setData': {
      return action.payload;
    }
    case 'setDataItem': {
      return {
        ...state,
        ...action.data,
      };
    }
    case 'PatchRule': {
      const { data } = action;
      const nextRules = state.extendedRules?.map((rule) => (rule.id === data.id ? { ...rule, ...data.params } : rule));
      return {
        ...state,
        extendedRules: nextRules ?? [],
      };
    }
    case 'deleteRule': {
      const { id } = action;
      const nextRules = state?.extendedRules?.filter((rule) => rule.id !== id);
      return { ...state, extendedRules: nextRules ?? [] };
    }

    default: {
      throw Error('Unknown action');
    }
  }
}
