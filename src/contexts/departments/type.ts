import type { ReactNode } from 'react';

import { formatterListItem } from '@/utils/orgSelect';

export enum ObjectType {
  User = 'user',
  Department = 'department',
}
export interface OpenZoneType {
  id: number;
  type: ObjectType;
  name: string;
  avatar?: string;
  email?: string;
  icon?: ReactNode;
  onCancel?: () => void;
}

export interface Departments {
  teamInfo: TeamInfoType;
  baseRules: BaseRules;
  extendedRules: ExtendedRule[];
  tableLoading: boolean;
}

export interface TeamInfoType {
  /**
   * 这里的类型是残缺的，只定义了使用到的字段
   */
  id: number;
  name: string;
}

export interface BaseRules {
  permission: RulePermission;
  applyToSearch: boolean;
  showMemberCount: boolean;
  showSubDepartments: boolean;
}

export interface ExtendedRule {
  id: number;
  owners: {
    ownerId: number;
    ownerName: string;
    ownerAvatar: string;
    ownerType: ObjectType;
  }[];
  targets: {
    targetId: number;
    targetName: string;
    targetAvatar: string;
    targetType: ObjectType;
  }[];
  visible: boolean;
  enable: boolean;
}

export interface RuleBaseApiResponse {
  data: BaseRules;
}

export interface RulesApiResponse {
  data: {
    rules: ExtendedRule[] | null;
  };
}

export enum RulePermission {
  all = 'all',
  nobody = 'nobody',
  department = 'department',
}

export type Action =
  | {
      type: 'setData';
      payload: Departments;
    }
  | {
      type: 'setDataItem';
      data: Partial<Departments>;
    }
  | {
      type: 'PatchRule';
      data: {
        id: number;
        params: {
          enable?: boolean;
          visible?: boolean;
        };
      };
    }
  | {
      type: 'deleteRule';
      id: number;
    };

export interface Department {
  id: number;
  name: string;
  allMemberCount: number;
}

export interface User {
  id: number;
  name: string;
  email: string;
  avatar: string;
  teamRole: string;
}

export interface Rule {
  id: number;
  owners: Array<{ id: number; type: ObjectType }>;
  targets: Array<{ id: number; type: ObjectType }>;
  visible: boolean;
  enable: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface MembershipScope extends OpenZoneType {}

export const SELECT_BASIC = {
  itemHeight: 36,
  itemKey: 'id',
  itemRender: formatterListItem,
};
