import type { ReactElement, ReactNode } from 'react';
import React, { createContext, useCallback, useContext } from 'react';

import { throwOutsideProviderError } from '@/utils/contexts';

import * as images from '../assets/fileIcon';
import { concatImageUrl } from '../utils/image';

export type ImageAsset = string;

// 所有图片资源的 key
type ImageKey = keyof typeof images;

// 所有图片资源名称，不带 2x 或 3x 后缀的 key
type ImageName = Exclude<ImageKey, `${string}2x` | `${string}3x`>;

export type ImageContextModel = Record<ImageKey, ImageAsset>;

const ImageContext = createContext<ImageContextModel | null>(null);

export interface ConfigContextProviderProps {
  children: ReactNode;
  imageConfig: ImageContextModel;
}

export function ImageContextProvider(props: ConfigContextProviderProps): ReactElement {
  const { imageConfig, children } = props;

  return <ImageContext.Provider value={imageConfig}>{children}</ImageContext.Provider>;
}

/**
 * 使用图片资源的上下文，在获取不到时会抛出错误
 * @returns
 */
function useImageContext(): ImageContextModel {
  const context = useContext(ImageContext);

  if (!context) {
    throwOutsideProviderError('ImageContext', 'context');
  }

  return context;
}

/**
 * 判断传入 string 是否是图片资源的 key
 * @param key
 * @returns
 */
function isImageKey(key: string): key is ImageKey {
  return key in images;
}

/**
 * 通过图片名称和缩放值获取的图片资源
 * @param name
 * @param context
 * @returns
 */
function getAssetByName(name: ImageName, context: ImageContextModel, scale?: 2 | 3): ImageAsset {
  if (scale !== 2 && scale !== 3) {
    return context[name];
  }

  const key = `${name}${scale}x`;
  if (isImageKey(key)) {
    return context[key as ImageKey];
  } else {
    return context[name];
  }
}

/**
 * 使用图片，这里只能返回基础的图片资源，如果需要 srcset 请使用 useImageSrcset
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function useImage(name: ImageName): ImageAsset {
  const context = useImageContext();
  return getAssetByName(name, context, 2);
}

/**
 * 使用图片 srcset
 * @returns
 */
export function useImageSrcset(name: ImageName): string {
  const context = useImageContext();

  return `${getAssetByName(name, context)} 1x, ${getAssetByName(name, context, 2)} 2x, ${getAssetByName(
    name,
    context,
    3,
  )} 3x`;
}

/**
 * 使用背景图 image-set
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function useBgImageSet(name: ImageName): string {
  const context = useImageContext();

  return `-webkit-image-set(url(${getAssetByName(name, context)}) 1x, url(${getAssetByName(
    name,
    context,
    2,
  )}) 2x, url(${getAssetByName(name, context, 3)}) 3x)`;
}

/**
 * 使用默认背景图 url
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function useDefaultBgImage(name: ImageName): string {
  const context = useImageContext();

  return `url(${getAssetByName(name, context, 2)})`;
}

/**
 * 使用用于获取图片地址的回调
 * @returns
 */
export function useGetImage(): (name: ImageName) => ImageAsset {
  const context = useImageContext();

  return useCallback(
    (name: ImageName) => {
      return getAssetByName(name, context, 2);
    },
    [context],
  );
}

/**
 * 使用用于获取图片 srcset 的回调
 * @returns
 */
export function useGetImageSrcset(): (name: ImageName) => ImageAsset {
  const context = useImageContext();

  return useCallback(
    (name: ImageName) => {
      return `${concatImageUrl(getAssetByName(name, context))} 1x, ${concatImageUrl(getAssetByName(name, context, 2))} 2x, ${concatImageUrl(
        getAssetByName(name, context, 3),
      )} 3x`;
    },
    [context],
  );
}
