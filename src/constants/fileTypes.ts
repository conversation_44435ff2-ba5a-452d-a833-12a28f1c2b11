// 石墨支持的内部类型, 暂不支持: 应用表格table, 表单form
export type ShimoFileType =
  | 'newdoc' // 轻文档
  | 'modoc' // 传统文档
  | 'mosheet' // 专业表格
  | 'presentation'; // 专业幻灯片

// 硬编码映射关系：扩展名 → 石墨文件类型
export const importExtToShimoTypeMap = {
  md: 'newdoc',
  txt: 'newdoc',

  docx: 'modoc',
  doc: 'modoc',
  wps: 'modoc',

  xls: 'mosheet',
  xlsx: 'mosheet',
  csv: 'mosheet',
  xlsm: 'mosheet',

  ppt: 'presentation',
  pptx: 'presentation',

  xmind: 'mindmap',
} as const;

// 支持查看且支持导入编辑的云文件类型
export const CLOUD_FILE_TYPES_SUPPORT_IMPORT_EDIT = Object.keys(importExtToShimoTypeMap) as Array<
  keyof typeof importExtToShimoTypeMap
>;
export type CloudImportExt = keyof typeof importExtToShimoTypeMap;
// type 是 FileDetail.subtype 的类型
export function isSupportedImportExt(type: string): type is CloudImportExt {
  return (CLOUD_FILE_TYPES_SUPPORT_IMPORT_EDIT as readonly string[]).includes(type);
}

// 支持查看且不支持编辑的文件格式如下，编辑按钮隐藏不显示
export const CLOUD_FILE_TYPES_SUPPORT_VIEW = [
  'wpt',
  'pdf',
  'rtf',
  'mp3',
  'mp4',
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'heic',
  'heif',
  'ofd',
  'rtf',
];

// 不支持导入编辑且不支持查看的云文件,打开文件后，进入到文件缺省页

// 后端文件type字典
/* const FILE_TYPE_DICT = [
  {
    type: 'xls',
    display: '表格',
    subTypes: ['numbers', 'xlsx', 'xlsm', 'xlsb', 'xltx', 'xltm', 'xls', 'xlt', 'xlam', 'xla', 'xlw', 'xlr', 'csv'],
  },
  {
    type: 'docx',
    display: '文档',
    subTypes: ['pages', 'doc', 'docx', 'dotx', 'dotm', 'txt', 'md', 'xml', 'xps'],
  },
  {
    type: 'ppt',
    display: '演示文稿',
    subTypes: ['keynote', 'ppt', 'pptx', 'pot', 'potm', 'potx', 'pptm', 'pps', 'ppsm', 'ppsx', 'rtf'],
  },
  {
    type: 'mp3',
    display: '音频',
    subTypes: ['aac', 'mp3', 'flac', 'rec', 'm4a', 'wav', 'ogg', 'mogg', 'wma', 'ape', 'alac', 'wv'],
  },
  {
    type: 'mp4',
    display: '视频',
    subTypes: ['mp4', 'avi', 'mkv', 'swf', 'wmv', 'm2p', 'm4v', 'h264', 'h265', 'flv'],
  },
  {
    type: 'zip',
    display: '压缩文件',
    subTypes: ['zip', 'rar', 'pkg', '7z', 'tar', 'gz'],
  },
  {
    type: 'wps',
    display: 'WPS 文件',
    subTypes: ['wps'],
  },
  {
    type: 'xmind',
    display: 'XMIND 文件',
    subTypes: ['xmind'],
  },
  {
    type: 'pdf',
    display: 'PDF',
    subTypes: ['pdf'],
  },
  {
    type: 'img',
    display: '图片',
    subTypes: ['jpg', 'jpeg', 'png', 'svg', 'raw', 'gif', 'bmp', 'webp', 'sketch', 'psd', 'ai', 'ico'],
  },
  {
    type: 'unknown',
    display: '未知类型',
    subTypes: [],
  },
] as const; */
