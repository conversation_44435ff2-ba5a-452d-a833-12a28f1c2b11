import type { DescriptionsProps } from 'antd';
import { Button, Descriptions, Tooltip, Typography } from 'antd';
import moment from 'moment';

import { ReactComponent as StatusIcon } from '@/assets/images/svg/desktop-preview/status.svg';
import { FileTypeEnum, RoleEnum } from '@/model/Desktop';
import { fm } from '@/modules/Locale';
import { useCollaborationPanelStore } from '@/store/Desktop';
import type { FileDetail } from '@/types/api';

const { Text, Paragraph } = Typography;
import { useFilePreviewStore } from '@/store/filePreview';

import styles from './index.less';

interface DescriptionProps {
  fileDetail: FileDetail;
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  if (bytes === undefined || bytes === null) return '-';

  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  if (i === 0) return `${bytes} ${units[i]}`;
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
};

const Description = ({ fileDetail }: DescriptionProps) => {
  const { isShortcutFromRecord } = useFilePreviewStore();
  const { name, role, type, user, createdAt, fileSize, updatedUser, updatedAt, isCloudFile, guid } = fileDetail;
  const { handleOpenCollaborationPanel } = useCollaborationPanelStore();

  // 提前调用所有 fm 函数
  const collaboratorsLabel = fm('Description.collaborators');
  const myPermissionsLabel = fm('Description.myPermissions');
  const typeLabel = fm('Description.type');
  const creatorLabel = fm('Description.creator');
  const creationTimeLabel = fm('Description.creationTime');
  const lastUpdatedLabel = fm('Description.lastUpdated');
  const sizeLabel = fm('Description.size');
  const openCollaborationPanelText = fm('Description.openCollaborationPanel');
  const sizeTooltipText = fm('Description.sizeTooltip');

  const titleStyle = {
    display: 'block',
    fontSize: '13px',
    maxWidth: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
    marginBottom: '0',
  };

  const contentStyle = {
    color: 'var(--theme-text-color-medium)',
    display: 'inline-block',
    fontSize: '12px',
    maxWidth: '100%',
    textAlign: 'right' as const,
  };

  const textContentStyle = {
    color: 'var(--theme-text-color-medium)',
    fontSize: '12px',
  };

  const testStyle = {
    display: 'inline-block',
    width: 'calc(100% - 130px)',
    fontSize: '12px',
    color: 'var(--theme-text-color-medium)',
  };

  const buttonStyle = {
    color: 'var(--theme-link-button-color)',
    fontSize: '12px',
    height: 'auto',
    lineHeight: 'inherit',
    padding: 0,
    textAlign: 'right' as const,
  };

  const labelStyle = {
    color: 'var(--theme-text-color-default)',
    fontSize: '12px',
  };

  // 处理打开协作面板
  const openCollaborationPanel = () => {
    if (guid) {
      handleOpenCollaborationPanel(guid);
    }
  };

  const items: DescriptionsProps['items'] = [
    ...(!isCloudFile && !isShortcutFromRecord
      ? [
          {
            key: '1',
            label: collaboratorsLabel,
            children: (
              <Button style={buttonStyle} type="link" onClick={openCollaborationPanel}>
                {openCollaborationPanelText}
              </Button>
            ),
          },
        ]
      : []),
    {
      key: '2',
      label: myPermissionsLabel,
      children: <p className={styles.textContent}>{role ? RoleEnum[role as keyof typeof RoleEnum] : ''}</p>,
    },
    {
      key: '2-1',
      label: '',
      children: '',
      className: styles.emptyRow,
    },
    {
      key: '3',
      label: typeLabel,
      children: <p className={styles.textContent}>{FileTypeEnum[type]}</p>,
    },
    {
      key: '4',
      label: creatorLabel,
      children: (
        <Text ellipsis style={textContentStyle}>
          {user?.name}
        </Text>
      ),
    },
    {
      key: '5',
      label: creationTimeLabel,
      children: <p className={styles.textContent}>{moment(createdAt * 1000).format('YYYY-MM-DD HH:mm')}</p>,
    },
    {
      key: '6',
      label: lastUpdatedLabel,
      children: (
        <p>
          <Text ellipsis style={testStyle}>
            {updatedUser?.name}
          </Text>{' '}
          <span className={styles.textContent}>{moment(updatedAt * 1000).format('YYYY-MM-DD HH:mm')}</span>
        </p>
      ),
    },
    {
      key: '6-1',
      label: '',
      children: '',
      className: styles.emptyRow,
    },
    {
      key: '7',
      label: (
        <span className={styles.sizeLabel}>
          {sizeLabel}
          <Tooltip placement="bottom" title={sizeTooltipText}>
            <StatusIcon height={14} width={14} />
          </Tooltip>
        </span>
      ),
      children: <p className={styles.textContent}>{formatFileSize(fileSize || 0)}</p>,
    },
  ];
  return (
    <Descriptions
      className={styles.descriptionContainer}
      column={1}
      contentStyle={contentStyle}
      items={items}
      labelStyle={labelStyle}
      title={
        <Paragraph ellipsis={{ rows: 1, tooltip: name }} style={titleStyle}>
          {name}
        </Paragraph>
      }
    />
  );
};

export default Description;
