import { Modal, Skeleton } from 'antd';
import { useMemo } from 'react';

import { ReactComponent as ArrowLeft } from '@/assets/images/svg/arrow-left.svg';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';

import { Footer } from './components/Footer';
import { MainView } from './components/MainView';
import { UserAdd } from './components/UserAdd';
import { UserList } from './components/UserList';
import { useFilePermission, ViewType } from './hooks/use-file-permission';
import styles from './index.less';

function ModalTitle({ title, goBack, showGoBack }: { title: string; showGoBack: boolean; goBack: () => void }) {
  return (
    <div className={styles.title} onClick={showGoBack ? goBack : undefined}>
      {showGoBack && <ArrowLeft height={22} width={22} />}
      <span className={styles.titleText}>{title}</span>
    </div>
  );
}

interface ViewRendererProps extends UserOrgSelectorProps {
  currentView: ViewType;
  goNext: () => void;
  tabList: ReturnType<typeof useFilePermission>['tabList'];
  updateData: ReturnType<typeof useFilePermission>['updateData'];
  transformCollapseList: ReturnType<typeof useFilePermission>['transformCollapseList'];
}

function ViewRenderer({ currentView, goNext, tabList, updateData, transformCollapseList, ...rest }: ViewRendererProps) {
  const viewComponents = useMemo(
    () => ({
      [ViewType.MAIN]: (
        <MainView
          goNext={goNext}
          tabList={tabList}
          transformCollapseList={transformCollapseList}
          updateData={updateData}
        />
      ),
      [ViewType.USER_LIST]: <UserList goNext={goNext} />,
      [ViewType.USER_ADD]: <UserAdd {...rest} />,
    }),
    [goNext, tabList, updateData, transformCollapseList, rest],
  );

  return viewComponents[currentView];
}

export function FilePermissionModal({ onClose }: { onClose: () => void }) {
  const {
    title,
    currentView,
    goBack,
    showGoBack,
    goNext,
    tabList,
    updateData,
    transformCollapseList,
    isLoading,
    error,
    changed,
    ...rest
  } = useFilePermission();

  if (error) {
    return (
      <Modal open className={styles.filePermissionModal} footer={null} title="错误" onCancel={onClose}>
        <div>加载权限设置失败，请重试</div>
      </Modal>
    );
  }

  return (
    <Modal
      open
      className={styles.filePermissionModal}
      footer={null}
      keyboard={false}
      maskClosable={false}
      title={<ModalTitle goBack={goBack} showGoBack={showGoBack} title={title} />}
      onCancel={onClose}
    >
      {isLoading ? (
        <Skeleton />
      ) : (
        <>
          <ViewRenderer
            currentView={currentView}
            goNext={goNext}
            tabList={tabList}
            transformCollapseList={transformCollapseList}
            updateData={updateData}
            {...rest}
          />
          {!showGoBack && (
            <Footer
              changed={changed}
              showCheckbox={false}
              onClose={onClose}
              onSave={() => {
                // TODO: 实现实际的保存逻辑
                console.log('保存权限设置');
              }}
            />
          )}
        </>
      )}
    </Modal>
  );
}
