import { Tooltip } from 'antd';

import { ReactComponent as StatusIcon } from '@/assets/images/svg/desktop-preview/status.svg';

import styles from './TooltipIcon.less';

export function TooltipIcon({
  toolTips,
  height,
  width,
  className,
}: {
  className?: string;
  toolTips?: string;
  height: number;
  width: number;
}) {
  return (
    <Tooltip className={className} title={toolTips}>
      <span className={styles.icon}>
        <StatusIcon height={height} width={width} />
      </span>
    </Tooltip>
  );
}
