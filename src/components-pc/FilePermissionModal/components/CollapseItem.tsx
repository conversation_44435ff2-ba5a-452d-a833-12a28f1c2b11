import { Avatar } from 'antd';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import UserCardPopover from '@/components/UserCardPopover';

import { useCollapseItem } from '../hooks/use-collapse-item';
import type { TabListItem, UpdateData } from '../hooks/use-permission-setting';
import { CheckboxList } from './CheckboxList';
import { CheckboxWithDropdown } from './CheckboxWithDropdown';
import styles from './CollapseItem.less';

export type CollapseItemProps = TabListItem['list'][number] & {
  onViewAll?: () => void;
  memberCount?: number;
  onUpdate: UpdateData;
};

export function CollapseItem({ onViewAll, memberCount = 1, onUpdate, ...rest }: CollapseItemProps) {
  const { admin, creator, roleList } = useCollapseItem(rest, onUpdate);

  return (
    <div className={styles.container}>
      <div className={styles.checkboxSection}>
        <CheckboxList admin={admin || undefined} creator={creator || undefined} />
        {roleList.map(({ key, data, ...props }) => (
          <CheckboxWithDropdown key={key} data={Array.isArray(data) ? data : undefined} {...props} />
        ))}
      </div>

      <div className={styles.membersSection}>
        <div className={styles.membersLeft}>
          <span className={styles.membersLabel}>指定成员</span>
          <Avatar.Group max={{ count: 10, style: { width: '24px', height: '24px' } }}>
            <UserCardPopover userId={99197}>
              <Avatar className={styles.avatar} size={24} src="https://via.placeholder.com/24x24" />
            </UserCardPopover>
          </Avatar.Group>
        </div>
        <div className={styles.viewAllButton} onClick={onViewAll}>
          <span className={styles.viewAllText}>查看全部({memberCount})</span>
          <ArrowRight />
        </div>
      </div>
    </div>
  );
}
