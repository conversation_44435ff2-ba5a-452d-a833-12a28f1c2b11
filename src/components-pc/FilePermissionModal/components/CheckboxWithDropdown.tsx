import { Checkbox, Dropdown } from 'antd';
import classNames from 'classnames';

import { ReactComponent as SelectArrow } from '@/assets/images/svg/selectArrowOutline.svg';

import type { UseRoleItemProps } from '../hooks/use-role-item';
import { useRoleItem } from '../hooks/use-role-item';
import styles from './CheckboxWithDropdown.less';

export interface CheckboxWithSelectProps extends UseRoleItemProps {
  label: string;
}

export function CheckboxWithDropdown({ label, ...rest }: CheckboxWithSelectProps) {
  const { itemLabel, onClick, itemText, checked, handleChecked } = useRoleItem(rest);

  return (
    <div className={styles.content}>
      <Checkbox checked={checked} onChange={handleChecked}>
        {label}
      </Checkbox>
      <Dropdown disabled={!checked} menu={{ items: itemLabel, onClick }} trigger={['click']}>
        <div
          className={classNames(styles.item, {
            [styles.disabled]: !checked,
          })}
        >
          <span className={styles.text}>{itemText}</span>
          <SelectArrow className={styles.arrow} height={16} width={16} />
        </div>
      </Dropdown>
    </div>
  );
}
