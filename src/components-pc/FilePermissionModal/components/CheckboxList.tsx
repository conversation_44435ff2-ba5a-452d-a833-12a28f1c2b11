import type { CheckboxChangeEvent } from 'antd';
import { Checkbox, Tooltip } from 'antd';

import { useFormatMessage } from '@/modules/Locale';

import type { CheckboxConfig } from '../hooks/use-collapse-item';
import styles from './CheckboxList.less';

export interface CheckboxItemProps {
  tooltip?: string;
  checked?: boolean;
  onChange: (checked: boolean) => void;
}

interface CheckboxListProps {
  creator?: CheckboxConfig;
  admin?: CheckboxConfig;
}

export function CheckboxList({ creator, admin }: CheckboxListProps) {
  const enterpriseCreatorText = useFormatMessage('Role.enterpriseCreator');
  const enterpriseAdminText = useFormatMessage('Role.enterpriseAdmin');

  function handleCreatorChange(e: CheckboxChangeEvent) {
    creator?.onChange(e.target.checked);
  }

  function handleAdminChange(e: CheckboxChangeEvent) {
    admin?.onChange(e.target.checked);
  }

  return (
    <div className={styles.checkboxRow}>
      <Tooltip title={creator?.tooltip}>
        <Checkbox checked={creator?.checked} onChange={handleCreatorChange}>
          {enterpriseCreatorText}
        </Checkbox>
      </Tooltip>
      <Tooltip title={admin?.tooltip}>
        <Checkbox checked={admin?.checked} onChange={handleAdminChange}>
          {enterpriseAdminText}
        </Checkbox>
      </Tooltip>
    </div>
  );
}
