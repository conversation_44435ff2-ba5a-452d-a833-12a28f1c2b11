import { Button } from 'antd';

import { ReactComponent as PlusIcon } from '@/assets/images/svg/plus-circle.svg';

import { TooltipIcon } from './TooltipIcon';
import styles from './UserList.less';

export function UserList({ goNext }: { goNext: () => void }) {
  const toolTips = '包含继承自企业设置的成员，以及单独为此空间添加的成员';

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <span className={styles.titleText}>
          已添加的指定成员
          <TooltipIcon className={styles.titleIcon} height={14} toolTips={toolTips} width={14} />
        </span>
        <Button icon={<PlusIcon height={16} width={16} />} size="small" type="link" onClick={goNext}>
          添加更多成员
        </Button>
      </div>
      <div className={styles.list}>list</div>
    </div>
  );
}
