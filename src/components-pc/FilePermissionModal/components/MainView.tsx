import { Tabs } from 'antd';

import type { CollapseListItem } from '../hooks/use-file-permission';
import type { TabListItem, UpdateData } from '../hooks/use-permission-setting';
import { CollaborationPermissionTab } from './CollaborationPermissionTab';
import { CollapseItem } from './CollapseItem';

interface MainViewProps {
  tabList: TabListItem[];
  transformCollapseList: (list: TabListItem['list']) => CollapseListItem[];
  updateData: UpdateData;
  goNext: () => void;
}

export function MainView({ tabList, transformCollapseList, updateData, goNext }: MainViewProps) {
  const items = tabList.map(({ list, ...rest }) => {
    const collapseList = transformCollapseList(list).map(({ key, label }) => {
      const originalItem = list.find((item) => item.id === key);
      return {
        key,
        label,
        children: originalItem ? <CollapseItem onUpdate={updateData} onViewAll={goNext} {...originalItem} /> : null,
      };
    });

    return {
      ...rest,
      children: <CollaborationPermissionTab list={collapseList} />,
    };
  });

  return <Tabs defaultActiveKey={tabList[0]?.key} items={items} />;
}
