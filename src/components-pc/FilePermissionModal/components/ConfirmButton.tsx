import type { ButtonProps } from 'antd';
import { Button, Popconfirm } from 'antd';
import type { ReactNode } from 'react';

interface ConfirmButtonProps extends Omit<ButtonProps, 'onClick'> {
  /** 是否需要显示确认弹框 */
  needConfirm: boolean;
  /** 确认弹框的标题 */
  confirmTitle: string;
  /** 确认弹框的描述 */
  confirmDescription: string;
  /** 确认按钮文案 */
  confirmText: string;
  /** 取消按钮文案 */
  cancelText: string;
  /** 点击确认后的回调 */
  onConfirm: () => void;
  /** 点击取消后的回调（可选） */
  onCancel?: () => void;
  /** 按钮内容 */
  children: ReactNode;
}

export function ConfirmButton({
  needConfirm,
  confirmTitle,
  confirmDescription,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  children,
  ...buttonProps
}: ConfirmButtonProps) {
  if (needConfirm) {
    return (
      <Popconfirm
        cancelText={cancelText}
        description={confirmDescription}
        okText={confirmText}
        title={confirmTitle}
        onCancel={onCancel}
        onConfirm={onConfirm}
      >
        <Button {...buttonProps}>{children}</Button>
      </Popconfirm>
    );
  }

  return (
    <Button {...buttonProps} onClick={onConfirm}>
      {children}
    </Button>
  );
}
