.content {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  :global(.ant-checkbox-wrapper) {
    margin-right: 12px;
    color: var(--theme-text-color-default);
  }

  :global(.ant-checkbox-label) {
    color: var(--theme-text-color-primary);
    font-size: 12px;
  }
}

.item {
  padding: 4px;
  display: flex;
  align-items: center;
  color: var(--theme-primary-color);
  font-size: 12px;
  cursor: pointer;

  &:hover {
    border-radius: 2px;
    background: var(--theme-hover-bg-color);
  }

  &.disabled {
    .text,
    .arrow {
      cursor: not-allowed;
      color: var(---Disabled30, rgba(65, 70, 75, 30%));
    }
  }
}

.text {
  color: var(---Guidanceblue-6, #5ba0e7);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}

.arrow {
  color: var(---Guidanceblue-6, #5ba0e7);
}
