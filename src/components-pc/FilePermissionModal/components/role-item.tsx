import { ReactComponent as SelectedIcon } from '@/assets/images/svg/selected.svg';

import styles from './role-item.less';

export function RoleItem({ children, selected }: { selected: boolean; children: React.ReactNode }) {
  return (
    <span className={styles.container}>
      <span className={styles.text}>{children}</span>
      {selected && <SelectedIcon className={styles.selected} height="18" width="18" />}
    </span>
  );
}

export function StrongText({ children }: { children: React.ReactNode }) {
  return <span className={styles.span}>{children}</span>;
}
