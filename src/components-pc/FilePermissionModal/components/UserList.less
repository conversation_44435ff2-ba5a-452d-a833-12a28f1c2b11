.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleText {
  display: flex;
  align-items: center;
  color: var(---Secondary60, rgba(65, 70, 75, 60%));
  font-size: 12px;
  line-height: 20px;
}

.titleBtn {
  color: var(---Disabled30, rgba(65, 70, 75, 30%));
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}

.titleIcon {
  margin-left: 4px;
}

.list {
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid var(---Divider-Lightertransparency-10, rgba(65, 70, 75, 10%));
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: flex-start;
}
