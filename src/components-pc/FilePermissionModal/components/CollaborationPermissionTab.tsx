import { Collapse } from 'antd';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';

import type { CollapseListItem } from '../hooks/use-file-permission';
import styles from './Tabs.less';

export interface CollaborationPermissionTabProps {
  list: CollapseListItem[];
}

export function CollaborationPermissionTab({ list }: CollaborationPermissionTabProps) {
  return (
    <div className={styles.container}>
      {list.map((item) => (
        <Collapse
          key={item.key}
          className={styles.collapse}
          defaultActiveKey={item.key}
          expandIcon={() => <ArrowRight />}
          expandIconPosition="end"
          items={[item]}
        />
      ))}
    </div>
  );
}
