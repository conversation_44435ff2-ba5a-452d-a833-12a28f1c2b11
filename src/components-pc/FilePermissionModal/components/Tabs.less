.container {
  height: 337px;
  overflow: auto;
}

.collapse.collapse.collapse {
  margin-bottom: 8px;

  :global(.ant-collapse-header) {
    padding: 12px;
  }

  :global(.ant-collapse-header-text) {
    color: var(---Default100, #41464b);
    font-size: 12px;
  }

  :global(.ant-collapse-content) {
    border-top: 0;
    background: var(--theme-layout-color-bg-new-page);
  }

  :global(.ant-collapse-expand-icon) {
    svg {
      transition: transform 0.3s ease;
    }
  }

  :global(.ant-collapse-item-active .ant-collapse-expand-icon) {
    svg {
      transform: rotate(90deg);
    }
  }

  :global(.ant-collapse-content-box) {
    padding: 0 12px 12px;
  }
}
