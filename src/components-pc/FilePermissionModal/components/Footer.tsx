import { Button, Checkbox, Popconfirm } from 'antd';

import styles from './Footer.less';
import { TooltipIcon } from './TooltipIcon';

export function Footer({
  showCheckbox,
  toolTips,
  onClose,
  changed,
}: {
  showCheckbox?: boolean;
  toolTips?: string;
  onClose: () => void;
  changed: boolean;
}) {
  return (
    <footer className={styles.footer}>
      <div className={styles.footerLeft}>
        {showCheckbox && (
          <>
            <Checkbox>
              <span className={styles.inheritText}>继承上级目录权限设置</span>
            </Checkbox>
            <TooltipIcon height={20} toolTips={toolTips} width={20} />
          </>
        )}
      </div>
      <div className={styles.footerRight}>
        <Popconfirm
          cancelText="No"
          description="Are you sure to delete this task?"
          okText="Yes"
          title="Delete the task"
          onConfirm={confirm}
        >
          <Button className={styles.saveBtn} size="small" type="primary">
            保存修改
          </Button>
        </Popconfirm>

        <Button className={styles.cancelBtn} size="small" onClick={onClose}>
          取消
        </Button>
      </div>
    </footer>
  );
}
