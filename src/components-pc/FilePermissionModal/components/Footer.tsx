import { Button, Checkbox, Popconfirm } from 'antd';

import styles from './Footer.less';
import { TooltipIcon } from './TooltipIcon';

export function Footer({
  showCheckbox,
  toolTips,
  onClose,
  changed,
}: {
  showCheckbox?: boolean;
  toolTips?: string;
  onClose: () => void;
  changed: boolean;
}) {
  return (
    <footer className={styles.footer}>
      <div className={styles.footerLeft}>
        {showCheckbox && (
          <>
            <Checkbox>
              <span className={styles.inheritText}>继承上级目录权限设置</span>
            </Checkbox>
            <TooltipIcon height={20} toolTips={toolTips} width={20} />
          </>
        )}
      </div>
      <div className={styles.footerRight}>
        <Button className={styles.saveBtn} size="small" type="primary">
          保存修改
        </Button>

        {changed ? (
          <Popconfirm
            cancelText="再想想"
            description="保存修改后，将影响企业内所有有继承自企业权限的空间、文件夹和文件"
            okText="确认保存"
            title="保存修改的权限?"
            onCancel={() => {
              // 用户选择"再想想"，不关闭弹框
            }}
            onConfirm={() => {
              // TODO: 这里需要实现实际的保存逻辑
              onClose();
            }}
          >
            <Button className={styles.cancelBtn} size="small">
              取消
            </Button>
          </Popconfirm>
        ) : (
          <Button className={styles.cancelBtn} size="small" onClick={onClose}>
            取消
          </Button>
        )}
      </div>
    </footer>
  );
}
