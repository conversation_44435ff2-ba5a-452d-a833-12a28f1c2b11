import { Checkbox } from 'antd';

import { ConfirmButton } from './ConfirmButton';
import styles from './Footer.less';
import { TooltipIcon } from './TooltipIcon';

export function Footer({
  showCheckbox,
  toolTips,
  onClose,
  changed,
  onSave,
}: {
  showCheckbox?: boolean;
  toolTips?: string;
  onClose: () => void;
  onSave?: () => void;
  changed: boolean;
}) {
  return (
    <footer className={styles.footer}>
      <div className={styles.footerLeft}>
        {showCheckbox && (
          <>
            <Checkbox>
              <span className={styles.inheritText}>继承上级目录权限设置</span>
            </Checkbox>
            <TooltipIcon height={20} toolTips={toolTips} width={20} />
          </>
        )}
      </div>
      <div className={styles.footerRight}>
        <ConfirmButton
          cancelText="再想想"
          className={styles.saveBtn}
          confirmDescription="保存修改后，将影响企业内所有有继承自企业权限的空间、文件夹和文件"
          confirmText="确认保存"
          confirmTitle="保存修改的权限?"
          needConfirm={changed}
          size="small"
          type="primary"
          onConfirm={() => {
            if (onSave) {
              onSave();
            }
            onClose();
          }}
        >
          保存修改
        </ConfirmButton>

        <ConfirmButton
          cancelText="再想想"
          className={styles.cancelBtn}
          confirmDescription="保存修改后，将影响企业内所有有继承自企业权限的空间、文件夹和文件"
          confirmText="确认保存"
          confirmTitle="保存修改的权限?"
          needConfirm={changed}
          size="small"
          onCancel={() => {
            // 用户选择"再想想"，不关闭弹框
          }}
          onConfirm={() => {
            if (onSave) {
              onSave();
            }
            onClose();
          }}
        >
          取消
        </ConfirmButton>
      </div>
    </footer>
  );
}
