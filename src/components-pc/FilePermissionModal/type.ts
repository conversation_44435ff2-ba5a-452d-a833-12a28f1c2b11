export enum FileRoleType {
  Collaborator = 'Collaborator',
  Viewer = 'Viewer',
}

export const PermissionBasicItems = {
  canCreateChildFile: {
    id: 'canCreateChildFile',
    roleType: FileRoleType.Collaborator,
  },
  canExport: {
    id: 'canExport',
    roleType: FileRoleType.Viewer,
  },
  canPrint: {
    id: 'canPrint',
    roleType: FileRoleType.Viewer,
  },
  canCopy: {
    id: 'canCopy',
    roleType: FileRoleType.Viewer,
  },
  canDuplicate: {
    id: 'canDuplicate',
    roleType: FileRoleType.Viewer,
  },
  canManageCollaborator: {
    id: 'canManageCollaborator',
    roleType: FileRoleType.Collaborator,
  },
  canManageAdmin: {
    id: 'canManageAdmin',
    roleType: FileRoleType.Collaborator,
  },
  canAddOutsider: {
    id: 'canAddOutsider',
    roleType: FileRoleType.Collaborator,
  },
  canChangeShareMode: {
    id: 'canChangeShareMode',
    roleType: FileRoleType.Collaborator,
  },
  canChangeTeamShareMode: {
    id: 'canChangeTeamShareMode',
    roleType: FileRoleType.Collaborator,
  },
  canCustomizeAdvancedPermission: {
    id: 'canCustomizeAdvancedPermission',
    roleType: FileRoleType.Collaborator,
  },
};
