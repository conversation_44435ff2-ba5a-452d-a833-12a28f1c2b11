import type { CheckboxChangeEvent } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';

import type { RolePermission } from '@/service/files-team-settings.type';

import { RoleItem, StrongText } from '../components/role-item';
import { FileRoleType } from '../type';

const FileRoleNameMap = {
  [FileRoleType.Collaborator]: '协作者',
  [FileRoleType.Viewer]: '访问者',
};

const DropdownItems = [
  {
    label: <>文件{<StrongText>{'管理者'}</StrongText>}</>,
    value: 99,
    key: '99',
  },

  {
    label: (
      <>
        有{<StrongText>{'编辑权限'}</StrongText>}的{FileRoleNameMap[FileRoleType.Collaborator]}
      </>
    ),
    value: 60,
    key: '60',
  },
  {
    label: (
      <>
        有{<StrongText>{'评论权限'}</StrongText>}的{FileRoleNameMap[FileRoleType.Collaborator]}
      </>
    ),
    value: 40,
    key: '40',
  },
  {
    label: (
      <>
        有{<StrongText>{'阅读权限'}</StrongText>}的{FileRoleNameMap[FileRoleType.Collaborator]}
      </>
    ),
    value: 20,
    key: '20',
  },
];

export interface UseRoleItemProps {
  data?: RolePermission[];
  updateData?: (id: number, allow: boolean) => void;
}

export function useRoleItem({ data, updateData }: UseRoleItemProps) {
  const [activeKey, setActiveKey] = useState(99);
  const [checked, setChecked] = useState(!!data);

  // 根据data筛选可用的DropdownItems
  const filteredDropdownItems = useMemo(() => {
    if (!data || data.length === 0) {
      return DropdownItems;
    }

    // 获取data中所有的targetId
    const availableTargetIds = data.map((item) => item.targetId);

    // 筛选出在data中存在的DropdownItems
    return DropdownItems.filter((item) => availableTargetIds.includes(item.value));
  }, [data]);

  const itemLabel = useMemo(() => {
    return filteredDropdownItems.map(({ label, key, value }) => ({
      key,
      value,
      label: <RoleItem selected={activeKey === value}>{label}</RoleItem>,
    }));
  }, [activeKey, filteredDropdownItems]);

  const itemText = useMemo(() => {
    return filteredDropdownItems.find((item) => item.value === activeKey)?.label;
  }, [activeKey, filteredDropdownItems]);

  const onClick = useCallback(
    (e: { key: string }) => {
      const newKey = Number(e.key);
      setActiveKey(newKey);

      if (updateData && data) {
        data.forEach((item) => {
          updateData(item.targetId, item.targetId === newKey);
        });
      }
    },
    [updateData, data],
  );

  const handleChecked = useCallback(
    (e: CheckboxChangeEvent) => {
      const isChecked = e.target.checked;
      setChecked(isChecked);

      if (updateData && data) {
        if (isChecked) {
          data.forEach((item) => {
            updateData(item.targetId, item.targetId === activeKey);
          });
        } else {
          data.forEach((item) => {
            updateData(item.targetId, false);
          });
        }
      }
    },
    [updateData, data, activeKey],
  );

  useEffect(() => {
    if (!data) {
      // 如果没有data，设置为第一个可用选项或默认值99
      const defaultKey = filteredDropdownItems.length > 0 ? filteredDropdownItems[0].value : 99;
      setActiveKey(defaultKey);
      setChecked(false);
      return;
    }
    // 按顺序判断allow，true，返回
    const key = data.find((item) => item.allow)?.targetId;
    if (key) {
      setActiveKey(key);
      setChecked(true);
    } else {
      // 如果没有allow为true的项，设置为第一个可用选项
      const defaultKey = filteredDropdownItems.length > 0 ? filteredDropdownItems[0].value : 99;
      setActiveKey(defaultKey);
      setChecked(false);
    }
  }, [data, filteredDropdownItems]);

  return {
    onClick,
    itemText,
    itemLabel,
    checked,
    handleChecked,
  };
}
