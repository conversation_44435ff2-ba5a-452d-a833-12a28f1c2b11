import { useCallback, useState } from 'react';

import type { TabListItem } from './use-permission-setting';
import { usePermissionSetting } from './use-permission-setting';
import { useSelected } from './useSelected';

export enum ViewType {
  MAIN = 'main',
  USER_LIST = 'userList',
  USER_ADD = 'userAdd',
}

export const VIEW_TITLES = {
  [ViewType.MAIN]: '高级权限设置',
  [ViewType.USER_LIST]: '成员列表',
  [ViewType.USER_ADD]: '添加指定成员',
} as const;

export type CollapseListItem = {
  key: string;
  label: string;
};

const NAVIGATION_MAP: Record<ViewType, ViewType | null> = {
  [ViewType.MAIN]: null,
  [ViewType.USER_LIST]: ViewType.MAIN,
  [ViewType.USER_ADD]: ViewType.USER_LIST,
};

const FORWARD_NAVIGATION_MAP: Record<ViewType, ViewType | null> = {
  [ViewType.MAIN]: ViewType.USER_LIST,
  [ViewType.USER_LIST]: ViewType.USER_ADD,
  [ViewType.USER_ADD]: null,
};

export function useFilePermission() {
  const { tabList, updateData, changed, isLoading, error } = usePermissionSetting();
  const [currentView, setCurrentView] = useState<ViewType>(ViewType.MAIN);

  const title = VIEW_TITLES[currentView];
  const showGoBack = currentView !== ViewType.MAIN;

  const { select, selected } = useSelected();

  const goBack = useCallback(() => {
    const previousView = NAVIGATION_MAP[currentView];
    if (previousView) {
      setCurrentView(previousView);
    }
  }, [currentView]);

  const goNext = useCallback(() => {
    const nextView = FORWARD_NAVIGATION_MAP[currentView];
    if (nextView) {
      setCurrentView(nextView);
    }
  }, [currentView]);

  const transformCollapseList = useCallback(
    (list: TabListItem['list']): CollapseListItem[] =>
      list.map(({ id, name }) => ({
        key: id,
        label: name,
      })),
    [],
  );

  return {
    title,
    currentView,
    showGoBack,
    goBack,
    goNext,
    select,
    selected,
    tabList,
    updateData,
    transformCollapseList,
    changed,
    isLoading,
    error,
  };
}
