import { ZERO } from '@/configs/configs';
import { SELECT_BASIC } from '@/contexts/departments/type';
import { useOrgSelect } from '@/hooks/useOrgSelect';
import { formatterOpenSelectedList } from '@/pages/pc/Enterprise/Departments/utils';

export function useSelected() {
  const {
    loading,
    crumbs,
    orgList,
    selectedList,
    orgSelectAll,
    clearAllSelected,
    onScroll,
    onSearch,
    searchResult,
    searchLoading,
  } = useOrgSelect({
    pageSize: 50,
    admin: true,
    enableSelectAllEnterprise: true,
  });

  return {
    select: {
      loading: loading || searchLoading,
      onSearch,
      title: '',
      placeholder: '搜索',
      selectAll: orgSelectAll,
      breadcrumb: crumbs
        ? {
            crumbs,
          }
        : undefined,
      search: searchResult
        ? {
            ...SELECT_BASIC,
            data: searchResult,
          }
        : undefined,
      org: {
        ...SELECT_BASIC,
        onScroll,
        data: orgList,
      },
    },
    selected: {
      header: {
        title: (
          <>
            <div>{'已选择的部门/成员'}</div>
            <>({selectedList?.length ?? ZERO})</>
          </>
        ),
        onPress: clearAllSelected,
        pressText: '清空',
        disabled: selectedList?.length === ZERO || loading,
      },
      list: selectedList,
      itemRender: formatterOpenSelectedList,
    },
  };
}
