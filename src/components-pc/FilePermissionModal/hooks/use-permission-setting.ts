import { useCallback, useEffect, useMemo, useState } from 'react';

import { getTeamSettings } from '@/service/files-team-settings';
import type { PermissionsInfo } from '@/service/files-team-settings.type';

import { usePermission } from './use-permission';

export type TabList = ReturnType<typeof usePermissionSetting>['tabList'];
export type UpdateData = ReturnType<typeof usePermissionSetting>['updateData'];
export type TabListItem = TabList[number];
export type ItemId = TabListItem['list'][number]['id'];

type LoadingState = {
  isLoading: boolean;
  error: string | null;
};

const createRolePermissions = (values: number[] = []) =>
  values.map((targetId) => ({
    targetId,
    allow: true,
    inherited: false,
  }));

export function usePermissionSetting() {
  const { permissionTitleMap, permissionCategoryOptionMap, defaultPermission } = usePermission();

  const [data, setData] = useState<PermissionsInfo>({} as PermissionsInfo);
  const [changed, setChanged] = useState(false);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: true,
    error: null,
  });

  const getTab = useCallback(
    (key: keyof typeof permissionCategoryOptionMap, info: PermissionsInfo) => {
      return {
        key,
        label: permissionCategoryOptionMap[key].name,
        list: permissionCategoryOptionMap[key].content.map((id) => {
          const item = defaultPermission[id];
          return {
            id: id,
            name: permissionTitleMap[id].enterprise,
            teamRole:
              info[id]?.team_role ||
              (item && 'teamRole' in item && item.teamRole
                ? Object.entries(item.teamRole || {}).map(([targetId, config]) => ({
                    targetId: Number(targetId),
                    allow: (config as { enable?: boolean })?.enable || false,
                    inherited: false,
                  }))
                : []),
            fileRole: info[id]?.file_role || createRolePermissions(item?.fileRole?.fileRole?.values),
            fileRoleOutsider:
              info[id]?.file_role_outsider || createRolePermissions(item?.fileRole?.fileRoleOutsider?.values),
          };
        }),
      };
    },
    [permissionCategoryOptionMap, permissionTitleMap, defaultPermission],
  );

  const tabList = useMemo(() => {
    if (Object.keys(data).length === 0) {
      return [];
    }
    return [getTab('CollabPermission', data), getTab('ControlPermission', data)];
  }, [data, getTab]);

  const getSettings = useCallback(async () => {
    try {
      setLoadingState({ isLoading: true, error: null });
      const res = await getTeamSettings();
      setData(res.data.permissions);
      setLoadingState({ isLoading: false, error: null });
    } catch (error) {
      console.error('Failed to load team settings:', error);
      setLoadingState({
        isLoading: false,
        error: error instanceof Error ? error.message : '加载权限设置失败',
      });
    }
  }, []);

  const updateData = useCallback(
    (
      id: ItemId,
      updater: Partial<PermissionsInfo[ItemId]> | ((prev: PermissionsInfo[ItemId]) => Partial<PermissionsInfo[ItemId]>),
    ) => {
      setChanged(true);
      setData((pre) => ({
        ...pre,
        [id]: {
          ...pre[id],
          ...(typeof updater === 'function' ? updater(pre[id]) : updater),
        },
      }));
    },
    [],
  );

  useEffect(() => {
    getSettings();
  }, [getSettings]);

  return {
    tabList,
    updateData,
    changed,
    isLoading: loadingState.isLoading,
    error: loadingState.error,
  };
}
