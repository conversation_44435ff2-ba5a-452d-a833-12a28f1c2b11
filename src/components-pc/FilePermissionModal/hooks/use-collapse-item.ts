import { useCallback, useMemo } from 'react';

import { FileRoleList } from '../constants';
import type { TabListItem, UpdateData } from './use-permission-setting';

type CollapseItemData = TabListItem['list'][number];
type TeamRoleItem = NonNullable<CollapseItemData['teamRole']>[number];

export interface CheckboxConfig {
  tooltip?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
}

const TEAM_ROLE_TYPES = {
  ADMIN: 0,
  CREATOR: 1,
} as const;

export function useCollapseItem(info: CollapseItemData, onUpdate: UpdateData) {
  const updateTeamRoleItem = useCallback(
    (index: number, allow: boolean) => {
      if (onUpdate) {
        onUpdate(info.id, (prev) => ({
          team_role: prev.team_role?.map((item, i) => (i === index ? { ...item, allow } : item)),
        }));
      }
    },
    [onUpdate, info.id],
  );

  const updateFileRole = useCallback(
    (roleType: 'fileRole' | 'fileRoleOutsider', targetId: number, allow: boolean) => {
      if (onUpdate) {
        const dbFieldName = roleType === 'fileRole' ? 'file_role' : 'file_role_outsider';
        onUpdate(info.id, (prev) => {
          return {
            [dbFieldName]: prev[dbFieldName]?.map((item) =>
              item.targetId === targetId ? { ...item, allow } : item,
            ) || [
              {
                targetId,
                allow,
                inherited: false,
              },
            ],
          };
        });
      }
    },
    [onUpdate, info.id],
  );

  const createCheckboxConfig = useCallback(
    (roleItem: TeamRoleItem, index: number): CheckboxConfig => ({
      checked: roleItem.allow,
      onChange: (checked: boolean) => updateTeamRoleItem(index, checked),
    }),
    [updateTeamRoleItem],
  );

  const teamRole = useMemo(() => info.teamRole || [], [info.teamRole]);

  const roleList = useMemo(
    () =>
      FileRoleList.map(([key, { name }]) => ({
        key,
        label: name,
        data: info[key],
        updateData: (targetId: number, allow: boolean) => updateFileRole(key, targetId, allow),
      })),
    [info, updateFileRole],
  );

  const admin = useMemo(() => {
    const adminRole = teamRole[TEAM_ROLE_TYPES.ADMIN];
    return adminRole ? createCheckboxConfig(adminRole, TEAM_ROLE_TYPES.ADMIN) : undefined;
  }, [teamRole, createCheckboxConfig]);

  const creator = useMemo(() => {
    const creatorRole = teamRole[TEAM_ROLE_TYPES.CREATOR];
    return creatorRole ? createCheckboxConfig(creatorRole, TEAM_ROLE_TYPES.CREATOR) : undefined;
  }, [teamRole, createCheckboxConfig]);

  return {
    admin,
    creator,
    roleList,
  };
}
