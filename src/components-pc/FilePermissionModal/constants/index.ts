import { FileRoleType } from '../type';

export const FileRoleItemsOrder = [99, 60, 40, 20];

export const FileRoleList = [
  ['fileRole', { name: '企业内部' }],
  ['fileRoleOutsider', { name: '企业外部' }],
] as const;

export const FileRoleNameMap = {
  [FileRoleType.Collaborator]: '协作者',
  [FileRoleType.Viewer]: '访问者',
};

export const FileRoleTipNameMap = {
  [FileRoleType.Collaborator]: '协作者',
  [FileRoleType.Viewer]: '协作者和公开链接访问者',
};

export const DefaultPermission = {
  canCreateChildFile: {
    fileRole: {
      fileRole: {
        values: [99, 60],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60],
        cancelAble: true,
      },
    },
  },
  canCopy: {
    fileRole: {
      fileRole: {
        values: [60, 40, 20],
        cancelAble: false,
        specialTip: `不能取消勾选，因为有编辑权限的访问者默认都可以复制`,
      },
      fileRoleOutsider: {
        values: [60, 40, 20],
        cancelAble: false,
        specialTip: `不能取消勾选，因为有编辑权限的访问者默认都可以复制`,
      },
    },
  },
  canManageCollaborator: {
    fileRole: {
      fileRole: {
        values: [99, 60],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60],
        cancelAble: true,
      },
    },
  },
  canManageAdmin: {
    fileRole: {
      fileRole: {
        values: [99],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99],
        cancelAble: true,
      },
    },
  },
  canCustomizeAdvancedPermission: {
    teamRole: {
      '1': { enable: true, allowChangeExpandModeOnly: true },
      '2': { enable: true, allowChangeExpandModeOnly: true },
    },
    fileRole: {
      fileRole: {
        values: [99],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99],
        cancelAble: true,
      },
    },
    user: {
      enable: true,
      allowChangeExpandModeOnly: true,
    },
  },
  canAddOutsider: {
    fileRole: {
      fileRole: {
        values: [99, 60],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60],
        cancelAble: true,
      },
    },
  },
  canChangeShareMode: {
    fileRole: {
      fileRole: {
        values: [99, 60],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60],
        cancelAble: true,
      },
    },
  },
  canChangeTeamShareMode: {
    fileRole: {
      fileRole: {
        values: [99, 60],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60],
        cancelAble: true,
      },
    },
  },
  canExport: {
    fileRole: {
      fileRole: {
        values: [99, 60, 40, 20],
        cancelAble: true,
      },
      fileRoleOutsider: {
        values: [99, 60, 40, 20],
        cancelAble: true,
      },
      fileRoleAnonymous: {
        values: [60, 40, 20],
        cancelAble: true,
      },
    },
  },
};

export const PermissionTitleMap = {
  canCreateChildFile: {
    enterprise: `谁能创建或移入新内容`,
    space: `谁能在当前空间创建或移入新内容`,
    folder: `谁能在当前文件夹里创建或移入新内容`,
    file: null,
  },
  canExport: {
    enterprise: `谁能导出或下载企业文件`,
    space: `谁能导出、下载当前空间的文件`,
    folder: `谁能导出、下载当前文件夹里的文件`,
    file: `谁能导出、下载当前文件`,
  },
  canPrint: {
    enterprise: `谁能打印企业文件`,
    space: `谁能打印当前空间的文件`,
    folder: `谁能打印当前文件夹里的文件`,
    file: `谁能打印当前文件`,
  },
  canCopy: {
    enterprise: `谁能复制企业文档或表格的文本`,
    space: `谁能复制当前空间里文档、表格的文本`,
    folder: `谁能复制当前文件夹里文档、表格的文本`,
    file: `谁能复制当前文件的文本`,
  },
  canDuplicate: {
    enterprise: `谁能为企业文件创建副本、保存为模板、解压缩`,
    space: `谁能为当前空间里的文件创建副本、保存为模板、解压缩`,
    folder: `谁能为当前文件夹里的文件创建副本、保存为模板、解压缩`,
    file: `谁能为当前文件创建副本、保存为模板、解压缩`,
  },
  canManageCollaborator: {
    enterprise: `谁能为企业文件添加协作者、修改协作者权限`,
    space: `谁能为当前空间里的文件添加协作者、修改协作者权限`,
    folder: `谁能为当前文件夹添加协作者、修改协作者权限`,
    file: `谁能为当前文件添加协作者、修改协作者权限`,
  },
  canManageAdmin: {
    enterprise: `谁能为企业文件添加、删除管理者权限`,
    space: `谁能为当前空间添加、删除管理者权限`,
    folder: `谁能为当前文件夹添加、删除管理者权限`,
    file: `谁能为当前文件添加、删除管理者权限`,
  },
  canAddOutsider: {
    enterprise: `谁能为企业文件添加外部协作者`,
    space: `谁能为当前空间及其文件添加外部协作者`,
    folder: `谁能为当前文件夹及其文件添加外部协作者`,
    file: `谁能为当前文件添加外部协作者`,
  },
  canChangeShareMode: {
    enterprise: `谁能开启或修改企业文件的对外公开链接`,
    space: `谁能开启或修改当前空间文件的对外公开链接`,
    folder: `谁能开启或修改当前文件夹内文件的对外公开链接`,
    file: `谁能开启或修改当前文件的对外公开链接`,
  },
  canChangeTeamShareMode: {
    enterprise: `谁能开启或修改企业文件的企业内部公开链接`,
    space: `谁能开启或修改当前空间文件的企业内部公开链接`,
    folder: `谁能开启或修改当前文件夹内文件的企业内部公开链接`,
    file: `谁能开启或修改当前文件的企业内部公开链接`,
  },
  canCustomizeAdvancedPermission: {
    enterprise: `谁能修改高级权限`,
    space: `谁能修改当前空间的高级权限`,
    folder: `谁能修改当前文件夹的高级权限`,
    file: `谁能修改当前文件的高级权限`,
  },
};
