import { Splitter } from 'antd';
import type { ReactNode } from 'react';
import { useMemo, useState } from 'react';

import type { DataType, FileRecord, TreeViewAction } from '@/model/Desktop';
import { getFileIcon } from '@/utils/file';

import FilePreviewContent from './filePreviewContent';
import FileTree from './fileTree';
import styles from './index.less';
import { getFileIconByType } from './utils/fileIcon';

interface DataNode {
  title: ReactNode;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
  icon?: ReactNode;
}

interface FilePreviewProps {
  fileData: FileRecord[];
  handleContextMenu: (
    e: React.MouseEvent,
    record: FileRecord,
    rightClickItems?: DataType[],
    treeViewAction?: TreeViewAction,
  ) => void;
  setSelectedRowKeys: (keys: React.Key[]) => void;
}

function FilePreview(props: FilePreviewProps) {
  const { fileData, handleContextMenu, setSelectedRowKeys } = props;
  const [previewGuid, setPreviewGuid] = useState<string>('');
  const initTreeData: DataNode[] = useMemo(() => {
    return [...fileData].map((item) => ({
      title: (
        <span className={styles.titleIcon}>
          <span>{item.name}</span>
          {item.starred ? <img height={20} src={getFileIcon({ type: 'favorites' })} width={20} /> : null}
        </span>
      ),
      key: item.guid,
      isLeaf: !item.isFolder,
      icon: getFileIconByType(item),
      record: item,
    }));
  }, [fileData]);
  const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
    list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, key, children) };
      }
      return node;
    });
  return (
    <div className={styles.filePreviewContainer}>
      <Splitter>
        <Splitter.Panel defaultSize="263" max="452" min="263">
          <FileTree
            handleContextMenu={handleContextMenu}
            initTreeData={initTreeData}
            setPreviewGuid={setPreviewGuid}
            setSelectedRowKeys={setSelectedRowKeys}
            updateTreeData={updateTreeData}
          />
        </Splitter.Panel>
        <Splitter.Panel>
          <FilePreviewContent fileGuid={previewGuid} />
        </Splitter.Panel>
      </Splitter>
    </div>
  );
}

export default FilePreview;
