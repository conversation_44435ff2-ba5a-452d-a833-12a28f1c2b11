import shortcutIcon from '@/assets/images/fileIcon/shortcut.png';
import type { FileRecord } from '@/model/Desktop';
import { getFileIcon } from '@/utils/file';

/**
 * 根据文件记录获取文件图标
 * @param record 文件记录
 * @returns 文件图标
 */
export const getFileIconByType = (record: FileRecord) => {
  let type = null;
  if (record.isShortcut && record.shortcutSource) {
    type = record.shortcutSource.type;
  } else {
    type = record.type;
  }
  return (
    <div style={{ position: 'relative' }}>
      <img height={20} src={getFileIcon({ type: type, isSpace: record.isSpace })} width={20} />
      {record.isShortcut && <img src={shortcutIcon} style={{ position: 'absolute', bottom: 7, left: 3 }} />}
    </div>
  );
};
