.previewContent {
  height: calc(100% - 6px);
  display: flex;
  position: relative;

  .collapseBtn {
    position: absolute;
    right: 15px;
    top: 15px;
  }

  :global {
    .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
      background: transparent;
    }
  }

  .iconRotate {
    transform: scaleX(-1);
  }

  .iframeLoader {
    height: 100%;
    flex: 1;
  }

  .iframe {
    height: calc(100% - 50px);
  }

  .iframeLoaderSpin {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    :global {
      .ant-spin-nested-loading {
        width: 100px;
      }

      .ant-spin-text {
        margin-top: 30px;
      }
    }
  }

  .sidebar {
    width: 338px;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    box-shadow: rgba(0, 0, 0, 8%) 1px 0 0 inset;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 30px;

      h4 {
        font-size: 13px;
        line-height: 20px;
        font-weight: 500;
        flex: 1 1 auto;
      }
    }

    .content {
      padding: 30px;
    }
  }

  .sidebarCollapsed {
    width: 72px;
    box-shadow: rgba(0, 0, 0, 8%) 1px 0 0 inset;

    .sidebarInner {
      visibility: hidden;
    }
  }
}

.previewEmptyPrompt {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;

  .emptyContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    div {
      color: var(--theme-text-color-secondary);
    }
  }

  .emptyIcon {
    margin-bottom: 16px;
  }

  .emptyTitle {
    margin-bottom: 16px;
  }
}
