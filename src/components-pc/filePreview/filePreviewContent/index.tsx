import { Button } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import * as fileApi from '@/api/File';
import { catchApiResult } from '@/api/Request';
import shortcutIcon from '@/assets/images/fileIcon/shortcut.png';
import { ReactComponent as FolderShortcutIcon } from '@/assets/images/svg/desktop-preview/file-folder.svg';
import { ReactComponent as FileSelectIcon } from '@/assets/images/svg/desktop-preview/no-file-select.svg';
import { ReactComponent as NoAuthorizationIcon } from '@/assets/images/svg/desktop-preview/noAuth.svg';
import { ReactComponent as FileToggleIcon } from '@/assets/images/svg/desktop-preview/preview-right.svg';
import Description from '@/components-pc/description';
import { getFileInformationHeader } from '@/components-pc/fileInformation/utils/fileInformation';
import { fm } from '@/modules/Locale';
import { useFilePreviewStore } from '@/store/filePreview';

import ErrorPage from './ErrorPage';
import IframeLoader from './IframeLoader';
import styles from './index.less';

interface FilePreviewContentProps {
  fileGuid: string;
}

const FilePreviewContent: React.FC<FilePreviewContentProps> = ({ fileGuid }) => {
  const { fileDetail, setFileDetail, isShortcutFromRecord } = useFilePreviewStore();
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [isFolder, setIsFolder] = useState(false);
  const [isShortcut, setIsShortcut] = useState(false);
  const [showErrorPage, setShowErrorPage] = useState(false);

  // 提前调用所有 fm 函数
  // const getDetailFailedText = fm('File.getDetailFailed');
  const selectPreviewFileText = fm('File.selectPreviewFile');
  const previewErrorText = fm('Error.previewError');
  const previewErrorDesText = fm('Error.previewErrorDes');
  const folderShortcutErrorText = fm('Error.folderShortcutError');

  const initGetFile = useCallback(async () => {
    setShowErrorPage(false);
    setIsFolder(false);
    setIsShortcut(false);
    const [err, res] = await catchApiResult(fileApi.fileDetail(fileGuid));
    if (err) {
      setShowErrorPage(true);
      return;
    }
    setFileDetail(res?.data);
    setIsFolder(res?.data?.isFolder);
    setIsShortcut(res?.data?.isShortcut);
    if (res?.data?.isShortcut && res?.data?.shortcutSource?.type === 'folder') {
      setIsFolder(true);
    }
  }, [fileGuid, setFileDetail, setShowErrorPage, setIsFolder, setIsShortcut]);

  useEffect(() => {
    if (!fileGuid) return;
    initGetFile();
  }, [fileGuid, initGetFile]);

  // 文件夹快捷方式全部跳到缺省页
  if (isShortcut && isFolder) {
    const FolderShortcutIconWrapper = () => {
      return (
        <div style={{ position: 'relative' }}>
          <img src={shortcutIcon} style={{ position: 'absolute', bottom: 5, left: 3, width: 10 }} />
          <FolderShortcutIcon />
        </div>
      );
    };
    return <ErrorPage icon={<FolderShortcutIconWrapper />} subtitle={folderShortcutErrorText} />;
  }

  if (showErrorPage) {
    return <ErrorPage icon={<NoAuthorizationIcon />} subtitle={previewErrorDesText} title={previewErrorText} />;
  }

  return (
    <div className={styles.previewContent}>
      {!fileGuid || isFolder ? (
        <ErrorPage icon={<FileSelectIcon />} subtitle={selectPreviewFileText} />
      ) : (
        fileDetail && <IframeLoader fileGuid={fileGuid} />
      )}
      {!!fileDetail && !isFolder && !!fileGuid && (
        <>
          <Button className={styles.collapseBtn} type="text" onClick={() => setIsCollapsed(!isCollapsed)}>
            {isCollapsed ? <FileToggleIcon /> : <FileToggleIcon className={styles.iconRotate} />}
          </Button>
          <div className={isCollapsed ? styles.sidebarCollapsed : styles.sidebar}>
            <div className={styles.sidebarInner}>
              <header className={styles.header}>
                <h4>{getFileInformationHeader(fileDetail?.isCloudFile || false, isShortcutFromRecord)}</h4>
              </header>
              <div className={styles.content}>{fileDetail && <Description fileDetail={fileDetail} />}</div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default FilePreviewContent;
