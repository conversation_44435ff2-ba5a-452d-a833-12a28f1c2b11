import styles from './index.less';

interface ErrorPageProps {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
}
const ErrorPage: React.FC<ErrorPageProps> = (props) => {
  const { title, subtitle, icon } = props;

  return (
    <div className={styles.previewEmptyPrompt}>
      <div className={styles.emptyContent}>
        {icon && <span className={styles.emptyIcon}>{icon}</span>}
        {title && <div className={styles.emptyTitle}>{title}</div>}
        {subtitle && <div className={styles.emptySubtitle}>{subtitle}</div>}
      </div>
    </div>
  );
};

export default ErrorPage;
