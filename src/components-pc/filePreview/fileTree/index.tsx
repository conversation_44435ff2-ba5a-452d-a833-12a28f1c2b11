import { Tree } from 'antd';
import type { EventDataNode } from 'antd/es/tree';
import isEqual from 'lodash/isEqual';
import type { ReactNode } from 'react';
import React, { useEffect, useState } from 'react';

import { getFileDir } from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { useTableFilterSort } from '@/components/FunnelFilter/hooks';
import type { TableItem } from '@/components/FunnelFilter/types';
import { useVisibleHeightObserver } from '@/hooks/useVisibleHeightObserver';
import type { DataType, FileRecord, TreeViewAction } from '@/model/Desktop';
import { useFilePreviewStore } from '@/store/filePreview';
import { useListStatusCacheStore } from '@/store/listStatusCache';
import { getFileIcon } from '@/utils/file';

import styles from '../index.less';
import { getFileIconByType } from '../utils/fileIcon';
import style from './index.less';

interface DataNode {
  title: ReactNode;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
  icon?: ReactNode;
  record?: FileRecord;
}

interface FileTreeProps {
  initTreeData: DataNode[];
  updateTreeData: (list: DataNode[], key: React.Key, children: DataNode[]) => DataNode[];
  setPreviewGuid: (guid: string) => void;
  handleContextMenu: (
    e: React.MouseEvent,
    record: FileRecord,
    rightClickItems?: DataType[],
    treeViewAction?: TreeViewAction,
  ) => void;
  setSelectedRowKeys: (keys: React.Key[]) => void;
}

const FileTree: React.FC<FileTreeProps> = ({
  initTreeData,
  updateTreeData,
  setPreviewGuid,
  handleContextMenu,
  setSelectedRowKeys,
}) => {
  const { ref, visibleHeight } = useVisibleHeightObserver();
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [childrenData, setChildrenData] = useState<FileRecord[]>([]);
  const filterKeys = useListStatusCacheStore((state) => state.desktopPageFilterKeys);
  const sortKeys = useListStatusCacheStore((state) => state.desktopPageSortKeys);
  const { setIsShortcutFromRecord } = useFilePreviewStore();
  const [key, setKey] = useState('');
  const { tableData } = useTableFilterSort({
    data: childrenData as unknown as TableItem[],
    filterKeys,
    sortKeys,
  });

  useEffect(() => {
    setTreeData(initTreeData);
  }, [initTreeData]);

  useEffect(() => {
    const newChildren = tableData.map((item: TableItem) => ({
      key: item.guid,
      title: (
        <span className={styles.titleIcon}>
          <span>{item.name}</span>
          {item.starred ? <img height={20} src={getFileIcon({ type: 'favorites' })} width={20} /> : null}
        </span>
      ),
      isLeaf: !item.isFolder,
      loading: false,
      icon: getFileIconByType(item as FileRecord),
      record: item,
    }));
    setTreeData((origin) => updateTreeData(origin, key, newChildren as DataNode[]));
  }, [key, tableData, updateTreeData]);

  const onLoadData = async (treeNode: EventDataNode<DataNode>) => {
    const { key, isLeaf } = treeNode;
    if (isLeaf) return;

    const [err, res] = await catchApiResult(getFileDir(key));
    if (err) {
      console.error('err', err);
    }
    if (res?.status === 200) {
      const listData = res?.data?.list || [];
      setChildrenData(listData as unknown as FileRecord[]);
      setKey(key);
    }
  };

  const handleLeafClick = async (_selectedKeys: React.Key[], info: { node: DataNode }) => {
    const { node } = info;
    setIsShortcutFromRecord(node.record?.isShortcut || false);
    if (node.isLeaf) {
      // 是快捷方式，但是不是文件夹的快捷方式，guid设置为快捷方式资源里的guid
      if (node.record?.isShortcut && node.record?.shortcutSource?.type !== 'folder') {
        setPreviewGuid(node.record?.shortcutSource?.guid || '');
      } else {
        setPreviewGuid(node.key);
      }
      setSelectedRowKeys([node.key]);
    } else {
      if (node.record?.isShortcut) {
        setPreviewGuid(node.record?.shortcutSource?.guid || '');
      } else {
        setPreviewGuid(node.key);
      }
      setSelectedRowKeys([node.key]);
      await onLoadData(node as EventDataNode<DataNode>);
      setExpandedKeys((prev) => {
        if (prev.includes(node.key)) {
          return prev.filter((key) => key !== node.key);
        } else {
          return [...prev, node.key];
        }
      });
    }
  };

  return (
    <div ref={ref} className={style.treeBox}>
      <Tree
        showIcon
        virtual
        blockNode={true}
        expandedKeys={expandedKeys}
        height={visibleHeight}
        loadData={onLoadData}
        treeData={treeData}
        onExpand={(keys) => setExpandedKeys(keys)}
        onRightClick={(info: { event: React.MouseEvent; node: EventDataNode<DataNode> }) => {
          if (info.node.record) {
            handleContextMenu(info.event, info.node.record, undefined, { onLoadData, setPreviewGuid });
          }
        }}
        onSelect={handleLeafClick}
      />
    </div>
  );
};

export default React.memo(FileTree, (prevProps, nextProps) => {
  return isEqual(prevProps.initTreeData, nextProps.initTreeData);
});
