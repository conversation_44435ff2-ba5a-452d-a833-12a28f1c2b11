import type { TableProps } from 'antd';
import { Table } from 'antd';
import { memo, useMemo } from 'react';

import { useVisibleHeightObserver } from '@/hooks/useVisibleHeightObserver';
import { useXTable } from '@/hooks/useXTable';

import style from './index.less';
const GenericTable = <T extends object>(props: TableProps<T>) => {
  const { ref, visibleHeight, visibleWidth } = useVisibleHeightObserver();
  const { columns } = useXTable<T>({
    tableWidth: visibleWidth - 20,
    isRowSelection: props.rowSelection !== undefined,
    columns: props.columns,
  });
  const tableHeight = useMemo(() => {
    return visibleHeight - 54;
  }, [visibleHeight]);
  const isVirtual = useMemo(() => {
    if (!props.dataSource) {
      return false;
    }
    return props.dataSource.length > 20;
  }, [props.dataSource]);

  return (
    <div className={style.listView} style={{ padding: props.rowSelection !== undefined ? '0 40px 0 0' : '0 40px' }}>
      <div ref={ref} className={style.listViewTable}>
        <Table<T> {...props} columns={columns} scroll={{ y: tableHeight }} virtual={isVirtual} />
      </div>
    </div>
  );
};

export const XTable = memo(GenericTable) as <T extends object>(props: TableProps<T>) => JSX.Element;
