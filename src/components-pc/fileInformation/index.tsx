import { Button } from 'antd';

import { ReactComponent as OpenFileIcon } from '@/assets/images/svg/desktop-preview/open-file.svg';
import { ReactComponent as FileCloseIcon } from '@/assets/images/svg/desktop-preview/preview-close.svg';
import { FileTypeImage } from '@/components-pc/FileTypedimage';
import type { ImgType } from '@/components-pc/FileTypedimage/model';
import { fm } from '@/modules/Locale';
import { useFilePreviewStore } from '@/store/filePreview';

import Description from '../description';
import styles from './index.less';
import { getFileInformationHeader } from './utils/fileInformation';

interface FileInformationProps {
  handleCloseFileInfo: () => void;
}
function FileInformation(props: FileInformationProps) {
  const { fileDetail, isShortcutFromRecord } = useFilePreviewStore();
  const { handleCloseFileInfo } = props;

  // 提前调用所有 fm 函数
  const selectFileToPreviewText = fm('File.selectFileToPreview');
  const fileInfoText = fm('File.fileInfo');

  if (!fileDetail) {
    return (
      <div className={styles.fileInformation}>
        <header className={styles.header}>
          <h4>{fileInfoText}</h4>
          <span>
            <Button type="text" onClick={handleCloseFileInfo}>
              <FileCloseIcon />
            </Button>
          </span>
        </header>
        <div className={styles.emptyContent}>
          <div className={styles.emptyContentInner}>
            <OpenFileIcon className={styles.emptyIcon} />
            <div>{selectFileToPreviewText}</div>
          </div>
        </div>
      </div>
    );
  }

  const { isCloudFile } = fileDetail;

  return (
    <div className={styles.fileInformation}>
      <header className={styles.header}>
        <h4>{getFileInformationHeader(isCloudFile || false, isShortcutFromRecord || false)}</h4>
        <span>
          <Button type="text" onClick={handleCloseFileInfo}>
            <FileCloseIcon />
          </Button>
        </span>
      </header>
      <div className={styles.content}>
        <div className={styles.thumbnail}>
          <FileTypeImage height={120} type={fileDetail?.type as ImgType} width={120} />
        </div>
        <div className={styles.description}>
          <Description fileDetail={fileDetail} />
        </div>
      </div>
    </div>
  );
}

export default FileInformation;
