import { fm2 } from '@/modules/Locale';

export const getFileInformationHeader = (isCloudFile: boolean, isShortcut: boolean) => {
  const cloudFileInfoText = fm2('File.cloudFileInfo');
  const fileInfoText = fm2('File.fileInfo');
  const shortcutInfoText = fm2('File.shortcutInfo');
  if (isShortcut) {
    return shortcutInfoText;
  } else if (isCloudFile) {
    return cloudFileInfoText;
  } else {
    return fileInfoText;
  }
};
