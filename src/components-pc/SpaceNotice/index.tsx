import { Button } from 'antd';
import { useEffect, useState } from 'react';

import { getTimelines } from '@/api/Space';
import { ReactComponent as NoNoticeIcon } from '@/assets/images/space/no-notice.svg';
import { ReactComponent as FileCloseIcon } from '@/assets/images/svg/desktop-preview/preview-close.svg';
import type { Timelines } from '@/model/Space';
import { useFormatMessage } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import { FileCard } from './components/FileCard';
import { NoticeTimeLine } from './components/NoticeTimeLine';
import styles from './index.less';

interface SpaceNoticeProps {
  handleCloseFileInfo: () => void;
  guid: string;
}

export function SpaceNotice(props: SpaceNoticeProps) {
  const { handleCloseFileInfo, guid } = props;
  const { formatTime } = useFormatTime();

  const [timelines, setTimelines] = useState<Timelines[]>();

  const i18nText = {
    noticeTips: useFormatMessage('Space.noticeTips'),
    noticeDescription: useFormatMessage('Space.noticeDescription'),
    fileInfoText: useFormatMessage('Space.notice'),
  };

  const init = async () => {
    const response = await getTimelines(guid);
    setTimelines(response.list);
  };
  useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.spaceNotice}>
      <header className={styles.header}>
        <h4>{i18nText.fileInfoText}</h4>
        <span>
          <Button type="text" onClick={handleCloseFileInfo}>
            <FileCloseIcon />
          </Button>
        </span>
      </header>
      {timelines?.length ? (
        <div className={styles.timelines}>
          {timelines.map((item) => {
            return (
              <div key={item.timestamp}>
                <span className={styles.timelinesTime}>{formatTime(item.timestamp, 'message')}</span>
                <FileCard
                  guid={item.file.guid}
                  name={item.file.name}
                  total={item.actions.length}
                  type={item.file.type}
                />
                <NoticeTimeLine actions={item.actions} />
              </div>
            );
          })}
        </div>
      ) : (
        <div className={styles.emptyContent}>
          <div className={styles.emptyContentInner}>
            <NoNoticeIcon className={styles.emptyIcon} />
            <div>{i18nText.noticeTips}</div>
            <div>{i18nText.noticeDescription}</div>
          </div>
        </div>
      )}
    </div>
  );
}
