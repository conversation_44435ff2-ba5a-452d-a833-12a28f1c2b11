import type { FormatTimeLineActions, TimeLineActions } from '@/model/Space';
import { ActionType } from '@/model/Space';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
import { openFile } from '@/utils/file';

export const noticeOpen = ({ type, guid }: { type: string; guid: string }) => {
  let prefixUrl = '';
  switch (type) {
    case FileIconType.Doc:
      prefixUrl = '/docs/';
      break;
    case FileIconType.Word:
      prefixUrl = '/docx/';
      break;
    case FileIconType.Sheet:
      prefixUrl = '/sheets/';
      break;
    case FileIconType.Presentation:
      prefixUrl = '/presentation/';
      break;
    case FileIconType.Form:
      prefixUrl = '/forms/';
      break;
    case FileIconType.Board:
      prefixUrl = '/boards/';
      break;
    case FileIconType.Mindmap:
      prefixUrl = '/mindmaps/';
      break;
    default:
      prefixUrl = '/folder/';
      break;
  }
  openFile({ type, guid, url: `${prefixUrl}${guid}`, model: 'new' });
};

export const mergeCommentActions = (activities: TimeLineActions[]): FormatTimeLineActions[] => {
  if (!activities || activities.length === 0) return [];

  const result: FormatTimeLineActions[] = [];
  let currentGroup = null;

  for (const activity of activities) {
    // 处理连续的评论(type=2)或讨论(type=3)
    if (activity.type === ActionType.Comment || activity.type === ActionType.Discuss) {
      if (currentGroup && currentGroup.type === activity.type) {
        // 添加到当前组
        if (activity.type === ActionType.Comment) {
          if (activity.comment) {
            currentGroup.comment?.push(activity.comment);
          }
        } else {
          if (activity.discussion) {
            currentGroup.discussion?.push(activity.discussion);
          }
        }
        currentGroup.user.push(activity.user);
      } else {
        // 创建新组
        currentGroup = {
          type: activity.type,
          comment: activity.type === ActionType.Comment && activity.comment ? [activity.comment] : null,
          discussion: activity.type === ActionType.Discuss && activity.discussion ? [activity.discussion] : null,
          user: [activity.user],
          timestamp: activity.timestamp,
        };
        result.push(currentGroup);
      }
    } else {
      currentGroup = null;
      result.push({
        type: activity.type,
        comment: null,
        discussion: null,
        user: [activity.user],
        timestamp: activity.timestamp,
      });
    }
  }

  return result;
};
