.timelinesLines {
  position: relative;
  padding: 20px 0 20px 24px;

  :global {
    .ant-timeline-item:first-child .ant-timeline-item-head::before {
      content: '';
      position: absolute;
      height: 12px;
      width: 2px;
      top: -12px;
      left: 8px;
      background-color: var(--theme-box-shadow-color-level6);
      display: block;
    }

    .ant-timeline-item-tail {
      inset-block-start: 18px;
      height: calc(100% - 28px);
    }

    .ant-timeline-item-last .ant-timeline-item-content {
      min-height: 8px;
    }

    .ant-timeline-item-last {
      padding-bottom: 0;
    }

    .ant-timeline-item-head {
      background-color: unset;
    }
  }
}

.timelinesInfo {
  display: flex;
  gap: 8px;
  line-height: 24px;
}

.timelinesUser {
  :global {
    .ant-typography {
      color: var(--transparency90);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.timelinesAction {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
}

.timelinesMore {
  color: var(--theme-basic-color-notice);
  font-size: 14px;
  line-height: 24px;

  &:hover {
    cursor: pointer;
  }
}
