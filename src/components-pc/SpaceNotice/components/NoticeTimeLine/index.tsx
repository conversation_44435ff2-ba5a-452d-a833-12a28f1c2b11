import { Timeline, Typography } from 'antd';
import { uniq } from 'lodash';
import type { ReactNode } from 'react';
import React, { useMemo, useState } from 'react';

import { ReactComponent as CommentIcon } from '@/assets/images/space/comment.svg';
import { ReactComponent as FileCreationIcon } from '@/assets/images/space/fileCreation.svg';
import { ReactComponent as MoreIcon } from '@/assets/images/space/more.svg';
import { ReactComponent as UpdateIcon } from '@/assets/images/space/update.svg';
import type { ActionUser, TimeLineActions } from '@/model/Space';
import { ActionType } from '@/model/Space';
import { useFormatMessage } from '@/modules/Locale';

import { mergeCommentActions } from '../../utils';
import { CommentCard } from '../CommentCard';
import { DiscussionCard } from '../DiscussionCard';
import styles from './index.less';

const default_length = 3;

interface Props {
  actions: TimeLineActions[];
}

interface Items {
  color?: string;
  dot?: ReactNode;
  label?: ReactNode;
  children?: ReactNode;
  position?: 'left' | 'right';
}

interface UserNameProps {
  user: ActionUser[];
}

export const NoticeTimeLine: React.FC<Props> = (props) => {
  const { actions } = props;
  const [expanded, setExpanded] = useState(false);

  const i18nText = {
    noticeUpdate: useFormatMessage('Space.noticeUpdate'),
    noticeComment: useFormatMessage('Space.noticeComment'),
    noticeDiscuss: useFormatMessage('Space.noticeDiscuss'),
    noticeCreate: useFormatMessage('Space.noticeCreate'),
    noticeMoreBefore: useFormatMessage('Space.noticeMoreBefore'),
    noticeMoreAffter: useFormatMessage('Space.noticeMoreAffter'),
  };

  const AllUserName: React.FC<UserNameProps> = (props) => {
    const { user } = props;
    const allName = user.map((item) => item.name && item.name)?.filter((item) => !!item);
    if (!allName.length) return null;
    const uniqName = uniq(allName);
    const ellipsisName = uniqName.join('、');
    return (
      <span className={styles.timelinesUser}>
        <Typography.Text ellipsis={{ tooltip: ellipsisName }} style={{ maxWidth: 120 }}>
          {ellipsisName}
        </Typography.Text>
      </span>
    );
  };

  const timeLineItems = useMemo<Items[]>(() => {
    let currentActions: Items[] = [];

    const newActions = mergeCommentActions(actions);

    const allActions = newActions?.map((item) => {
      const items: Items = {};
      switch (item.type) {
        case ActionType.Edit:
          items.dot = <UpdateIcon />;
          items.children = (
            <div className={styles.timelinesInfo}>
              <AllUserName user={item.user} />
              <span className={styles.timelinesAction}>{i18nText.noticeUpdate}</span>
            </div>
          );
          break;
        case ActionType.Comment:
          items.dot = <CommentIcon />;
          items.children = (
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div className={styles.timelinesInfo}>
                <AllUserName user={item.user} />
                <span className={styles.timelinesAction}>{i18nText.noticeComment}</span>
              </div>
              {item.comment?.map((commentItem, index) => {
                return <CommentCard key={index} comment={commentItem} user={item.user[index]} />;
              })}
            </div>
          );
          break;
        case ActionType.Discuss:
          items.dot = <CommentIcon />;
          items.children = (
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div className={styles.timelinesInfo}>
                <AllUserName user={item.user} />
                <span className={styles.timelinesAction}>{i18nText.noticeDiscuss}</span>
              </div>
              {item.discussion?.map((discussionItem, index) => {
                return <DiscussionCard key={index} discussion={discussionItem} user={item.user[index]} />;
              })}
            </div>
          );
          break;
        case ActionType.FileCreation:
          items.dot = <FileCreationIcon />;
          items.children = (
            <div className={styles.timelinesInfo}>
              <AllUserName user={item.user} />
              <span className={styles.timelinesAction}>{i18nText.noticeCreate}</span>
            </div>
          );
          break;
        default:
          items.dot = <MoreIcon />;
          items.children = (
            <div className={styles.timelinesInfo}>
              <AllUserName user={item.user} />
            </div>
          );
          break;
      }
      return items;
    });
    if (!expanded && allActions.length > default_length) {
      const defaultActions: Items[] = allActions.slice(0, default_length);
      const otherLength = allActions.length - default_length;
      defaultActions.push({
        dot: <MoreIcon />,
        children: (
          <div className={styles.timelinesMore} onClick={() => setExpanded(true)}>
            <span>{`${i18nText.noticeMoreBefore} ${otherLength} ${i18nText.noticeMoreAffter}`}</span>
          </div>
        ),
      });
      currentActions = defaultActions;
    } else {
      currentActions = allActions;
    }
    return currentActions;
  }, [actions, expanded]);

  return <Timeline className={styles.timelinesLines} items={timeLineItems} />;
};
