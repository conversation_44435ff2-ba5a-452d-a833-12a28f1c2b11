import { Avatar, Typography } from 'antd';
import React from 'react';

import UserCardPopover from '@/components/UserCardPopover';
import type { ActionComment, ActionUser } from '@/model/Space';
import { useFormatTime } from '@/utils/file';

import styles from './index.less';

interface Props {
  user: ActionUser;
  comment: ActionComment;
}

export const CommentCard: React.FC<Props> = (props) => {
  const { comment, user } = props;

  const { formatTime } = useFormatTime();

  return (
    <div className={styles.commentCard}>
      <span className={styles.commentIcon}>
        <UserCardPopover userId={user.id}>{<Avatar size={32} src={user.avatar || ''} />}</UserCardPopover>
      </span>
      <div className={styles.commentContent}>
        <div className={styles.commentMessage}>
          <Typography.Paragraph
            ellipsis={{
              rows: 2,
              tooltip: comment?.content,
            }}
            style={{ maxWidth: 120, marginBottom: 6 }}
          >
            {comment?.content}
          </Typography.Paragraph>
        </div>
        <div className={styles.commentInfo}>
          <span>
            <Typography.Paragraph
              ellipsis={{ rows: 1, tooltip: { title: formatTime(comment?.createdAt || null) } }}
              style={{ maxWidth: 80 }}
            >
              {formatTime(comment?.createdAt || null)}
            </Typography.Paragraph>
          </span>
          <span>
            <Typography.Paragraph ellipsis={{ rows: 1, tooltip: { title: user.name } }} style={{ maxWidth: 80 }}>
              {user.name}
            </Typography.Paragraph>
          </span>
        </div>
      </div>
    </div>
  );
};
