.timelinesCard {
  display: flex;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-white);
  box-shadow: 0 2px 4px 0 var(--theme-box-shadow-color-level4);

  &:hover {
    box-shadow: 0 4px 10px var(--theme-table-color-header-gray-bg);
    cursor: pointer;
  }
}

.timelinesName {
  :global {
    .ant-typography {
      color: var(--theme-text-color-deep);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
    }
  }
}

.timelinesTotal {
  color: var(--theme-text-color-secondary);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
