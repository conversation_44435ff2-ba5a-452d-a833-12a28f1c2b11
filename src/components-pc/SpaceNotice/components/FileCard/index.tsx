import { Typography } from 'antd';
import React from 'react';

import { FileIcon } from '@/components/fileList/components/FileIcon';
import { useFormatMessage } from '@/modules/Locale';

import { noticeOpen } from '../../utils';
import styles from './index.less';

interface Props {
  type: string;
  name: string;
  total: number;
  guid: string;
}

export const FileCard: React.FC<Props> = (props) => {
  const { type, name, guid, total } = props;

  const openNoticeFile = () => {
    noticeOpen({ type, guid });
  };

  const totalNotice = useFormatMessage('Space.totalNotice', { total });

  return (
    <div className={styles.timelinesCard} onClick={openNoticeFile}>
      <span className={styles.timelinesIcon}>
        <FileIcon height={32} type={type} width={32} />
      </span>
      <div>
        <div className={styles.timelinesName}>
          <Typography.Paragraph ellipsis={{ rows: 1, tooltip: { title: name } }} style={{ maxWidth: 218 }}>
            {name}
          </Typography.Paragraph>
        </div>
        <div className={styles.timelinesTotal}>{totalNotice}</div>
      </div>
    </div>
  );
};
