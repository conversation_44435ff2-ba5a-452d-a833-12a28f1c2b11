import { Button, Col, Input, Row, Space, Tooltip, Typography } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import { getAnnouncements, putAnnouncements } from '@/api/Space';
import { ReactComponent as FileCloseIcon } from '@/assets/images/svg/desktop-preview/preview-close.svg';
import type { AnnouncementsResponse } from '@/model/Space';
import { useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { useFormatTime } from '@/utils/file';

import styles from './index.less';

interface FileInformationProps {
  guid: string;
  handleCloseFileInfo: () => void;
  /** 空间创建人id */
  createdBy?: number;
}

export function SpaceAnnouncement(props: FileInformationProps) {
  const { formatTime } = useFormatTime();

  const meId = useMeStore((state) => state.me.id);

  const { handleCloseFileInfo, guid, createdBy } = props;

  const [model, setModel] = useState(false);

  const [value, setValue] = useState<string>();

  const [announcementLog, setAnnouncementLog] = useState<AnnouncementsResponse[]>();

  const i18nText = {
    me: useFormatMessage('SearchCenter.me'),
    clickEditAnnouncement: useFormatMessage('Space.clickEditAnnouncement'),
    spaceAnnouncement: useFormatMessage('Space.spaceAnnouncement'),
    announcementMaxTips: useFormatMessage('Space.announcementMaxTips'),
    clear: useFormatMessage('Space.clear'),
    publish: useFormatMessage('Space.publish'),
    cancel: useFormatMessage('FilePathPicker.cancel'),
    publishOn: useFormatMessage('Space.publishOn'),
  };

  const clear = () => {
    setValue(undefined);
  };

  const rePublish = (content: string) => {
    if (createdBy !== meId) return;
    setModel(true);
    setValue(content);
  };

  const clickPublish = () => {
    if (createdBy !== meId) return;
    setModel(true);
  };

  const init = useCallback(async () => {
    const response = await getAnnouncements(guid);
    setAnnouncementLog(response);
  }, []);

  const publish = async () => {
    const response = await putAnnouncements({ guid, announcement: value });
    if (response) {
      setAnnouncementLog([response]);
    } else {
      setAnnouncementLog([]);
    }
    setModel(false);
  };

  useEffect(() => {
    init();
  }, [init]);

  return (
    <div className={styles.spaceAnnouncement}>
      <header className={styles.header}>
        <h4>{i18nText.spaceAnnouncement}</h4>
        <span>
          <Button type="text" onClick={handleCloseFileInfo}>
            <FileCloseIcon />
          </Button>
        </span>
      </header>
      {model ? (
        <div className={styles.announcementInput}>
          <Input.TextArea
            maxLength={500}
            placeholder={i18nText.announcementMaxTips}
            style={{ height: 230, resize: 'none', fontSize: '13px' }}
            value={value}
            onChange={(e) => setValue(e.target.value)}
          />
          <Row>
            <Col span={6}>
              {value && (
                <Button type="text" onClick={clear}>
                  {i18nText.clear}
                </Button>
              )}
            </Col>
            <Col offset={6} span={12}>
              <Space>
                <Button color="default" variant="solid" onClick={publish}>
                  {i18nText.publish}
                </Button>
                <Button
                  onClick={() => {
                    setModel(false);
                    setValue(undefined);
                  }}
                >
                  {i18nText.cancel}
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      ) : announcementLog?.length ? (
        <div className={styles.announcementContent}>
          {announcementLog.map((item, index) => {
            return (
              <Space key={index} direction="vertical" style={{ width: '100%' }}>
                <Tooltip title={createdBy === meId ? i18nText.clickEditAnnouncement : undefined}>
                  <div className={styles.announcementText} onClick={() => rePublish(item.content)}>
                    {item.content}
                  </div>
                </Tooltip>
                <div className={styles.announcementInfo}>
                  <Typography.Text ellipsis={{ tooltip: item.author.name }} style={{ maxWidth: 100 }}>
                    {item.author.id === meId ? i18nText.me : item.author.name}
                  </Typography.Text>
                  <span>{i18nText.publishOn}</span>
                  <span>{formatTime(item.createdAt * 1000)}</span>
                </div>
              </Space>
            );
          })}
        </div>
      ) : (
        <div className={styles.announcementPlaceholder} onClick={clickPublish}>
          {i18nText.clickEditAnnouncement}
        </div>
      )}
    </div>
  );
}
