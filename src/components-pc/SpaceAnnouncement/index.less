.spaceAnnouncement {
  width: 338px;
  height: 100%;
  display: flex;
  border-left: 1px solid var(--theme-basic-color-black);
  flex-direction: column;
  flex: 0 0 auto;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 30px 0;
    color: var(--theme-text-color-secondary);

    h4 {
      font-size: 13px;
      line-height: 20px;
      font-weight: 500;
      flex: 1 1 auto;
      color: var(--theme-text-color-default);
    }

    :global {
      .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
        background: transparent;
        color: var(--theme-button-icon-color-hover);
      }
    }
  }
}

.announcementContent {
  margin: 16px 30px;
}

.announcementText {
  font-size: 12px;
  padding-left: 2px;
  margin-bottom: 12px;

  &:hover {
    cursor: pointer;
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.announcementInfo {
  padding-left: 2px;
  display: flex;
  gap: 4px;
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
  color: var(--theme-text-color-secondary);

  :global {
    .ant-typography {
      line-height: 20px;
      font-size: 12px;
      color: var(--theme-text-color-secondary);
    }
  }
}

.announcementPlaceholder {
  margin: 16px 30px;
  height: 244px;
  color: var(--theme-text-color-disabled);
  font-size: 12px;

  &:hover {
    cursor: text;
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.announcementInput {
  height: 284px;
  margin: 16px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
