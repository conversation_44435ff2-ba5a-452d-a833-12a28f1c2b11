import { CheckOutlined, EllipsisOutlined } from '@ant-design/icons';
import { But<PERSON>, Dropdown } from 'antd';
import type { Key } from 'react';
import React, { useCallback } from 'react';

import { ReactComponent as DesktopSvg } from '@/assets/images/empty/desktop.svg';
import { FileName } from '@/components/fileList/components/FileName';
import { NoData } from '@/components/fileList/components/NoData';
import UserCardPopover from '@/components/UserCardPopover';
import type { DataType, FileRecord, FilterType } from '@/model/Desktop';
import { i18nText } from '@/model/Desktop';
import { fm } from '@/modules/Locale';
import { useFilePreviewStore } from '@/store/filePreview';
import { UserClickImgOriginListTyp } from '@/store/listStatusCache';
import type { FileDetail } from '@/types/api';
import { formatFileSizeHuman, openFile, useFormatTime } from '@/utils/file';

import { XTable } from '../table';

interface ListViewProps {
  tableData: FileRecord[];
  loading: boolean;
  selectedRowKeys: Key[];
  noData: boolean;
  setSelectedRowKeys: (selectedRowKeys: Key[]) => void;
  handleContextMenu: (e: React.MouseEvent, record: FileRecord, rightClickItems: DataType[]) => void;
  filterType: FilterType | string;
  setFilterType: (filterType: FilterType | string) => void;
  handleSortChange: (params: { selectKeys: string[] }) => void;
  sortKeys: string[];
  setRightClickItems: any;
}

const ListView = React.memo(
  ({
    loading,
    selectedRowKeys,
    noData,
    tableData,
    setSelectedRowKeys,
    handleContextMenu,
    filterType,
    setFilterType,
    handleSortChange,
    sortKeys,
    setRightClickItems,
  }: ListViewProps) => {
    const { formatTime } = useFormatTime();
    const { setFileDetail, setIsShortcutFromRecord } = useFilePreviewStore();

    // 提前调用所有 fm 函数
    const fileNameText = fm('File.fileName');
    const createNameText = fm('File.createName');
    const updateTimeText = fm('File.updateTime');
    const createdAtText = fm('File.createdAt');
    const fileSizeText = fm('File.fileSize');

    const handleTableTimeChange = useCallback(
      (key: string) => {
        const sectKey = key === 'updatedAt' ? 'sortByUpdateTime' : 'sortByNewest';
        const newSelectKeys = [...sortKeys, sectKey].filter((item) => [sectKey, 'sortFolderTop'].includes(item));
        handleSortChange({ selectKeys: [...new Set(newSelectKeys)] });
      },
      [handleSortChange, sortKeys],
    );

    const columns = [
      {
        title: fileNameText,
        dataIndex: 'name',
        render: (value: string, record: FileRecord) => {
          return <FileName clickImgOriginType={UserClickImgOriginListTyp.DESKTOP} name={value} record={record} />;
        },
      },
      {
        title: createNameText,
        dataIndex: ['user', 'name'],
        ellipsis: true,
        align: 'center' as const,
        render: (value: string, record: FileRecord) => {
          return (
            <UserCardPopover placement="leftTop" trigger={['click']} userId={record.userId}>
              {value}
            </UserCardPopover>
          );
        },
      },
      {
        title: () => {
          return (
            <Dropdown
              menu={{
                items: [
                  {
                    label: updateTimeText,
                    key: 'updatedAt',
                    icon: filterType === 'updatedAt' ? <CheckOutlined /> : <span />,
                  },
                  {
                    label: createdAtText,
                    key: 'createdAt',
                    icon: filterType === 'createdAt' ? <CheckOutlined /> : <span />,
                  },
                ],
                onClick: ({ key }: { key: FilterType | string }) => {
                  setFilterType(key);
                  handleTableTimeChange(key);
                },
              }}
              overlayClassName="updateTimeFilter"
              trigger={['click']}
            >
              <Button size="small" style={{ fontSize: '12px' }} type="text">
                <span>{filterType === 'updatedAt' ? updateTimeText : createdAtText}</span>
              </Button>
            </Dropdown>
          );
        },
        dataIndex: filterType || 'createdAt',
        ellipsis: true,
        render: (value: number) => <>{formatTime(value * 1000)}</>,
      },
      {
        title: fileSizeText,
        dataIndex: 'fileSize',
        render: (value: number) => formatFileSizeHuman(value),
      },
      {
        title: '',
        dataIndex: 'options',
        align: 'center' as const,
        render: (_: number, record: FileRecord) => {
          return (
            <EllipsisOutlined
              className="more"
              onClick={(event) => {
                event.stopPropagation();
                handleContextMenu(event, record, setRightClickItems(selectedRowKeys as string[]));
              }}
            />
          );
        },
      },
    ];
    return !noData ? (
      <XTable<FileRecord>
        virtual
        columns={columns}
        dataSource={tableData}
        loading={loading}
        pagination={false}
        rowKey="guid"
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys,
          onChange: (selectedRowKeys: Key[]) => {
            setSelectedRowKeys(selectedRowKeys);
            if (selectedRowKeys.length > 0) {
              const selectedFile = tableData.find(
                (record) => record.guid === selectedRowKeys[selectedRowKeys.length - 1],
              );
              if (selectedFile) {
                setFileDetail(selectedFile as unknown as FileDetail);
                setIsShortcutFromRecord(selectedFile.isShortcut || false);
              }
            } else {
              setFileDetail(null);
            }
          },
        }}
        onRow={(record: FileRecord) => {
          return {
            record,
            onClick: () => {
              setFileDetail(record as FileDetail);
              setSelectedRowKeys([record.guid]);
            },
            onDoubleClick: (event) => {
              const isCheckbox = (event.target as HTMLElement)?.closest('.ant-checkbox-wrapper');
              if (isCheckbox) return;
              const newArg = record;
              if (record.isShortcut) {
                const { url, type } = record.shortcutSource!;
                newArg.type = type;
                newArg.url = url;
              }
              openFile(newArg);
            },
            onContextMenu: (event) => {
              let keys = [...selectedRowKeys];
              if (selectedRowKeys.includes(record.guid)) {
                // 存在
              } else {
                // 不存在
                keys = [record.guid];
                setSelectedRowKeys(keys);
              }
              const rightClickItems = setRightClickItems(keys as string[]);
              handleContextMenu(event, record, rightClickItems);
            },
          };
        }}
      />
    ) : (
      <NoData description={i18nText.noDesktopDescription} img={<DesktopSvg />} title={i18nText.noDesktopTitle} />
    );
  },
);

export default ListView;
