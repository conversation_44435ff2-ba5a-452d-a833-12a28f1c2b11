.content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 540px;
  position: relative;
  overflow: hidden;

  .formEmptyContent {
    display: flex;
    transform: scale(0.8);
  }

  .emptyItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    box-sizing: border-box;

    &:hover {
      box-shadow: var(--theme-box-shadow-color-level6);
    }

    .icon {
      width: 320px;
      height: 200px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .title {
      text-align: center;
      font-size: 28px;
      font-weight: 500;
      line-height: 48px; /* 171.429% */
    }

    .subTitle {
      text-align: center;
      font-size: 14px;
      line-height: 24px; /* 171.429% */
      color: var();
    }
  }

  .listContent {
    display: flex;
    width: 100%;
    height: 100%;

    .scrollContent {
      // height: 100%;
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      justify-content: start;

      .dataItem {
        width: 230px;
        height: 172px;
        position: relative;
        overflow: hidden;
        border-radius: 4px;
        border: 1px solid var(--theme-card-info-bg-hover-border);
        cursor: pointer;

        &:hover {
          .footerContent {
            bottom: 0;
          }
        }

        img {
          width: 100%;
          height: 124px;
        }

        .footerContent {
          box-sizing: border-box;
          width: 100%;
          padding: 14px 16px;
          gap: 14px;
          display: flex;
          flex-direction: column;
          position: absolute;
          bottom: -46px;
          left: 0;
          background-color: var(--theme-basic-color-bg-default);
          border: 0 1px solid var(--theme-card-info-bg-hover-border);
          transition: bottom 0.3s ease;

          .title {
            gap: 6px;
            display: flex;
            align-items: center;

            img {
              width: 16px;
              height: 16px;
            }

            span {
              white-space: nowrap; /* 禁止换行 */
              overflow: hidden; /* 溢出内容隐藏 */
              text-overflow: ellipsis; /* 溢出内容显示为省略号 */
              display: inline-block; /* 需要设置为块级或行内块级元素 */
              max-width: 100%;
            }
          }

          .buttons {
            display: flex;
            justify-content: space-between;

            button {
              width: 94px;
              height: 32px;
            }
          }
        }
      }

      .formEmptyContent {
        display: flex;
        gap: 16px;
        height: 172px;
        transform: scale(1);
      }

      .emptyItem {
        width: 230px;
        height: 172px;
        padding: 20px;
        gap: 4px;
        border-radius: 4px;
        border: 1px solid var(--theme-card-info-bg-hover-border);

        .icon {
          width: 72px;
          height: 72px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .title {
          text-align: center;
          margin-top: 8px;
          font-size: 16px;
          line-height: 24px;
        }

        .subTitle {
          text-align: center;
          font-size: 12px;
          line-height: 20px;
          color: var(--theme-text-color-secondary);
        }
      }
    }
  }
}
