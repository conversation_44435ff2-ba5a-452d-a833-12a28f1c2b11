.aiButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 24px;
  bottom: 30px;
  z-index: 1009;
}

.guideContent {
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 290px;
  box-sizing: border-box;

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: var(--theme-text-color-header);
  }

  .guideText {
    margin: 8px 0;
    color: var(--theme-text-color-header);
    font-size: 13px;
    line-height: 20px;
  }

  .btnDiv {
    display: flex;
    justify-content: flex-end;

    .okBtn {
      width: 48px;
    }
  }
}

.ai-popover {
  position: absolute;
  z-index: 1000;
  cursor: pointer;
  border-radius: 18px;
  // border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-white);
  box-shadow: var(--theme-box-shadow-color-level6);
}

.ai-button {
  img {
    width: 100%;
    height: 100%;
  }
}

// 添加Popover容器样式
#ai-btn-pop {
  // :global {
  .ant-popover {
    position: absolute;
    border: 1px solid var(--theme-separator-color-lighter);
    border-radius: 8px;

    .ant-popover-content {
      border-radius: 8px;
      overflow: hidden;
      background: var(--theme-layout-color-bg-white);
      box-shadow: var(--theme-box-shadow-color-level6);

      .ant-popover-inner {
        border: 0;
      }
    }
  }
  // }
}
