import './index.less';
import '@/utils/bootstrap';

import { <PERSON><PERSON>, Popover } from 'antd';
import { useEffect, useState } from 'react';

import aiPng from '@/assets/images/ai/help.png';
import { useAiSdk } from '@/hooks/useAiSdk';
import { CustomEventName } from '@/model/CustomEvent';
import { fm } from '@/modules/Locale';
import type { AiSDKProp } from '@/store/TemplateLib';
import { onCustomEvent } from '@/utils/customEvent';
import { interceptFetchRequest } from '@/utils/ShimoSDK/APIAdaptor';

import { type AiSDK, type FileBasic, FileType } from '../EditorSDK/AI';
export const SYSTEM_AI_PRESENTATION = 'system-ai-presentation-guide';
export const SYSTEM_AI_DOC = 'system-ai-doc-guide';
export const SYSTEM_AI_LIST = 'system-ai-list-guide';
const isPc = process.env.PLATFORM === 'pc';
if (isPc) window.initSDK([interceptFetchRequest]);
export const AiButton = ({
  style = {
    position: 'absolute',
    right: '24px',
    bottom: '74px',
  },
  ...prop
}: {
  open: boolean;
  style?: React.CSSProperties;
  arrow: boolean;
  type: string;
}) => {
  const [show, setShow] = useState(prop.open);
  const { initSdk } = useAiSdk();
  const [sdk, setSdk] = useState<AiSDK>();

  const closePop = (type: string) => {
    setShow(false);
    switch (type) {
      case FileType.newdoc:
        localStorage.setItem(SYSTEM_AI_DOC, '1');
        break;
      case FileType.presentation:
        localStorage.setItem(SYSTEM_AI_PRESENTATION, '1');
        break;
      default:
        localStorage.setItem(SYSTEM_AI_LIST, '1');
        break;
    }
  };
  const onClose = () => {
    closePop(prop.type);
  };

  useEffect(() => {
    let removeListener: (() => void) | null = null;
    let removeListener1: (() => void) | null = null;
    const fetchSdk = async (detail: AiSDKProp) => {
      const mySdk = await initSdk(detail);
      setSdk(mySdk);
      removeListener = onCustomEvent<{ files: FileBasic[] }>(CustomEventName.aiTemplateFiles, (detail) => {
        const files = detail.files.map((file) => {
          return {
            guid: file.guid,
            name: file.name,
            type: file.type,
          };
        });
        mySdk?.changeFileList(files);
      });
    };

    removeListener1 = onCustomEvent<AiSDKProp>(CustomEventName.aiTemplateInitAI, (detail) => {
      fetchSdk(detail);
    });
    return () => {
      removeListener?.();
      removeListener1?.();
    };
  }, []);
  const content = () => {
    return (
      <div className={'guideContent'}>
        <div className={'title'}>{fm('AI.popTitle')}</div>
        <div className="guideText">{fm('AI.popContent')}</div>
        <div className={'btnDiv'}>
          <Button className="sm-btn sm-btn-normal-primary okBtn" onClick={onClose}>
            {fm('RecoverFileModal.ok')}
          </Button>
        </div>
      </div>
    );
  };

  const openDialog = () => {
    onClose();
    sdk?.showDialog();
  };

  return isPc ? (
    <>
      <Popover
        arrow={prop.arrow}
        content={content}
        // @ts-ignore
        getPopupContainer={() => document.getElementById('ai-btn-pop') ?? document.body}
        open={show}
        placement="left"
      >
        {prop.type !== FileType.newdoc ? (
          <img alt="icon" className="ai-popover" src={aiPng} style={style} onClick={openDialog} />
        ) : (
          <span className="ai-popover" style={style}>
            {' '}
          </span>
        )}
      </Popover>
      {prop.type === FileType.newdoc ? (
        <img
          alt="icon"
          className="ai-popover"
          src={aiPng}
          style={{
            position: 'absolute',
            right: '24px',
            bottom: '74px',
          }}
          onClick={openDialog}
        />
      ) : null}
    </>
  ) : null;
};
