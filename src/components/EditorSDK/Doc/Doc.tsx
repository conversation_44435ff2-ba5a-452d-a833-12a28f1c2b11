import './style.css';

import { useCallback } from 'react';
import { FileType, ReadyState } from 'shimo-js-sdk-shared';

import { CustomEventName } from '@/model/CustomEvent';
import { getBrandConfig } from '@/utils/Brand';
import { handleSaveStatus, handleSyncStatus } from '@/utils/controllers/collaboration';
import { emitCustomEvent } from '@/utils/customEvent';
import { renderEditorBanner } from '@/utils/EditorBanner/renderEditorBanner';
import { getBoolean } from '@/utils/env';
import { renderUserCard } from '@/utils/RenderUserCard';
import type { EventCallback, ShimoSDK } from '@/utils/ShimoSDK';
import { fetchContent } from '@/utils/ShimoSDK';
import { createSaveStatus, createSyncStatus } from '@/utils/ShimoSDK/collaboration';

import { createCheckPermission } from '../../CheckPermission';
import { ShimoFileType } from '../../CheckPermission/scripts';
import { EditorMode, EditorTypeEnum } from '../AI';
const loadSdk = async () => {
  return new Promise<any>(function handler(resolve, reject) {
    if (window.SDKDoc) {
      resolve(window.SDKDoc);
    } else {
      setTimeout(() => {
        handler(resolve, reject);
      }, 50);
    }
  });
};
export const useInitializer = () => {
  const initializer = useCallback(async () => {
    let sdk: ShimoSDK | undefined;
    await (async () => {
      const elm = document.createElement('div');
      elm.style.height = '100%';
      elm.className = 'sm-docs-container';
      const rootElm = document.querySelector('#editor-content')!;
      rootElm.appendChild(elm);

      const { file, user } = window;

      sdk = await window.createSDK({
        fileType: FileType.Document,
        file,
      });

      sdk.setReadyState(ReadyState.LoadingEditor).catch((e: unknown) => {
        console.error(e);
      });

      const { head, content } = await fetchContent(file);
      const saveStatus = createSaveStatus(sdk);
      const syncStatus = createSyncStatus(sdk);

      handleSaveStatus(saveStatus, sdk);
      handleSyncStatus(syncStatus);

      const socketResponse = window.initSocket(sdk, { fileGuid: file.guid });
      const socket = socketResponse.socket;
      sdk.addRuntimeUpdater(socketResponse.configUpdater);

      const checkPermission = await createCheckPermission(ShimoFileType.rdoc);
      const renderNotificationBar = (props: { dom: HTMLDivElement }) => {
        const { dom } = props;
        renderEditorBanner({
          socket,
          dom,
        });
      };

      // 确保SDKDoc已经加载完成再createSDK2
      await loadSdk();

      const editor = await window.SDKDoc.createSDK2({
        content: {
          data: content,
        },
        collaboration: {
          head,
          offlineEditable: getBoolean('PRIVATE_DEPLOY') ? getBoolean('CUSTOMIZED_OFFLINE') : true,
          saveStatus: {
            onChangeState: saveStatus.onChangeState,
            onError: saveStatus.onError,
          },
          syncStatus: {
            onChangeState: syncStatus.onChangeState,
            onError: syncStatus.onError,
          },
        },

        file,
        user,
        socket,
        container: elm,
        ...sdk.getDefaultCreateEditorOptions(),
        renderUserCard,
        checkPermission,
        renderNotificationBar,
        getBrandConfig,
        plugins: {
          followMode: true,
        },
      });

      rootElm.classList.add('rendered');
      window.rendered = true;

      window.__SM__ = {
        ...window.__SM__,
        editor,
      };
      emitCustomEvent(CustomEventName.aiTemplateInitAI, {
        guid: file.guid,
        editorSDK: editor,
        editorMode: file.permissions.editable ? EditorMode.edit : EditorMode.preview,
        editorType: EditorTypeEnum.doc,
      });

      editor.on('titleChange', (newTitle: string) => {
        emitCustomEvent(CustomEventName.editorTitleChange, { newTitle });
      });

      emitCustomEvent(CustomEventName.socketReady, { socket });

      sdk.editor = editor;

      sdk.setEditorMethodInvoker(editor);

      sdk.onAddEventListener = (event: string, callback: EventCallback) => {
        editor.on(event, callback);
      };

      sdk.setReadyState(ReadyState.Ready).catch((e: unknown) => {
        console.error(e);
      });
      sdk.markPerformanceEntry('editor_render_end');
    })().catch((e) => {
      if (sdk) {
        sdk.setReadyState(ReadyState.Failed, e).catch((e: unknown) => {
          console.error(e);
        });
      }
      console.error(e);
    });
  }, []);

  return { initializer };
};
