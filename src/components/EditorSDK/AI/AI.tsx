import type { DocEditor, PresentationEditor } from '@/utils/ShimoSDK/types';

export type SDK = PresentationEditor | DocEditor;
export const SYSTEM_AI_PRESENTATION = 'system-ai-presentation-guide';
export const SYSTEM_AI_DOC = 'system-ai-doc-guide';
export const SYSTEM_AI_LIST = 'system-ai-list-guide';

export enum FileType {
  presentation = 'presentation',
  newdoc = 'newdoc',
}
// 文件对象
export interface FileBasic {
  guid: string;
  name: string;
  type: FileType; // 目前只允许 轻文档 和 幻灯片
}
export interface APIConfig {
  url: string;
  method?: string;
  headers?: Record<string, string> | null;
  query?: Record<string, string>;
  body?: Record<string, unknown>;
  withCredentials?: boolean;
}
export interface AiSDK {
  showDialog: () => void;
  hideDialog: () => void;
  changeFileList: (files: FileBasic[]) => void;
}

// 编辑器类型
export enum EditorTypeEnum {
  doc = 'doc', // 轻文档
  docx = 'docx', // 传统文档
  presentation = 'presentation', // 幻灯片
  sheet = 'sheet', // 专业表格
  pdf = 'pdf', // pdf
  table = 'table', // 应用表格
}

export enum EditorMode {
  edit = 'edit',
  preview = 'preview',
} //不传表示非套件

// TODO: 这里需要提供 ai 对应的 umd 加载 loader；改为react 组件方式
declare global {
  interface Window {
    LizardAISDK?: {
      createSDK: (p: {
        token?: string;
        fileGuid?: string;
        editorSDK?: SDK;
        editorType?: EditorTypeEnum;
        editorMode?: EditorMode;
        api?: Record<string, APIConfig>;
        icon?: string;
        name?: string;
        generateFileTypes?: FileType[]; // 允许生成的文件类型 ['newdoc']
        uploadFileTypes?: FileType[]; // 允许上传的文件类型
        welcome?: string;
        i18n?: {
          language?: string;
          direction?: 'ltr' | 'rtl';
        };
        openTemplate: (props: { onOk: (templateId: string) => void; onCancel: () => void }) => void;
      }) => AiSDK;
    };
  }
}
