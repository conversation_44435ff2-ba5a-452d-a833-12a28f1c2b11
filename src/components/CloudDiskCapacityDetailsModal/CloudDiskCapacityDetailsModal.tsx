import s18n from '@shimo/simple-i18n';
import type { ModalProps } from 'antd';
import React, { memo } from 'react';

import { StyledIframe, StyledModal } from './CloudDiskCapacityDetailsModal.styled';
import type { CloudDiskCapacityDetailsModalParams } from './hooks/useCloudDiskCapacityDetailsModal';
import { useCloudDiskCapacityDetailsModal } from './hooks/useCloudDiskCapacityDetailsModal';

const CLOUD_DISK_CAPACITY_DETAILS_URL = '/capacity-detail';

export type CapacityDetailsModalProps = ModalProps & CloudDiskCapacityDetailsModalParams;

export const CloudDiskCapacityDetailsModal = memo((props?: CapacityDetailsModalProps) => {
  const { onUpgradeOrRenew, onBuyPress, ...modalProps } = props ?? {};
  useCloudDiskCapacityDetailsModal({ onBuyPress, onUpgradeOrRenew });

  return (
    <StyledModal centered={true} footer={null} title={s18n('容量来源明细')} width={618} {...modalProps}>
      <StyledIframe src={CLOUD_DISK_CAPACITY_DETAILS_URL} />
    </StyledModal>
  );
});
