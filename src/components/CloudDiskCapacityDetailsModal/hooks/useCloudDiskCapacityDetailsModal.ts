import { useCallback, useEffect } from 'react';

import { MessageType } from '../enums/enums';

export interface CloudDiskCapacityDetailsModalParams {
  onUpgradeOrRenew?: () => void;
  onBuyPress?: () => void;
}

export function useCloudDiskCapacityDetailsModal(params: CloudDiskCapacityDetailsModalParams) {
  const { onUpgradeOrRenew, onBuyPress } = params;
  const handleMessageFromChild = useCallback(
    (event: { data: { type: string } }) => {
      const { data } = event;
      switch (data.type) {
        case MessageType.Buy:
          onBuyPress?.();
          break;
        case MessageType.Renew:
          onUpgradeOrRenew?.();
          break;
      }
    },
    [onUpgradeOrRenew, onBuyPress],
  );

  useEffect(() => {
    addEventListener('message', handleMessageFromChild);
    return () => {
      removeEventListener('message', handleMessageFromChild);
    };
  }, [handleMessageFromChild]);
}
