import type { ReactNode } from 'react';
import React, { useContext, useMemo } from 'react';

export interface IBreadcrumbItemProps {
  id: string;
  label: React.ReactNode;
}

export interface IBreadcrumbItem extends IBreadcrumbItemProps {
  onClick?: (item: IBreadcrumbItemProps) => void;
  width: number;
  icon?: ReactNode;
  name: string;
}

interface IBreadcrumbContextProps {
  children?: React.ReactNode;
  separator?: React.ReactNode;
  hideItems?: IBreadcrumbItem[];
  replaceItem?: IBreadcrumbItem;
  onItemClick?: (item: IBreadcrumbItemProps) => void;
}

const Context = React.createContext<IBreadcrumbContextProps>({
  separator: '>',
  hideItems: [],
});

export const BreadcrumbContext: React.FC<IBreadcrumbContextProps> = (props) => {
  const { children, separator, onItemClick, hideItems, replaceItem } = props;

  const contextValue = useMemo(() => {
    return {
      separator: separator || '/',
      hideItems: hideItems || [],
      replaceItem,
      onItemClick,
    };
  }, [separator, onItemClick, hideItems]);

  return <Context.Provider value={contextValue}>{children}</Context.Provider>;
};

export const useBreadcrumbContext = () => {
  const contextValue = useContext(Context);
  return contextValue;
};
