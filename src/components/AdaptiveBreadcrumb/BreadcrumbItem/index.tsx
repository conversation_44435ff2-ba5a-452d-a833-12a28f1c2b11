import { Dropdown } from 'antd';
import React, { useMemo } from 'react';

import desktopPng from '@/assets/images/common/desktop.png';

import type { IBreadcrumbItemProps } from '../BreadcrumbContext';
import { useBreadcrumbContext } from '../BreadcrumbContext';
import style from '../index.less';

export const BreadcrumbItem: React.FC<IBreadcrumbItemProps> = (props) => {
  const { id, label } = props;
  const { separator, hideItems = [], replaceItem } = useBreadcrumbContext();
  const isHide = useMemo(() => {
    return !!hideItems.find((item) => item.id === id || item.id === id);
  }, [hideItems, id]);

  const isReplace = useMemo(() => {
    return !!(replaceItem && (replaceItem.id === id || replaceItem.id === id));
  }, [replaceItem, id]);

  return (
    <div className={style.breadcrumbItem} data-id={id} data-label={label}>
      {isHide ? null : isReplace ? (
        <Dropdown
          menu={{
            items: [replaceItem!, ...hideItems]?.map((item) => {
              return {
                icon: item?.icon,
                label: item?.label,
                key: item?.id,
              };
            }),
          }}
          placement="bottomRight"
          trigger={['click']}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img className={style.breadcrumbItemLabelImg} src={desktopPng} />
            <div>{separator}</div>
          </div>
        </Dropdown>
      ) : (
        <>
          <div className={style.breadcrumbItemLabel}>{label}</div>
          <div>{separator}</div>
        </>
      )}
    </div>
  );
};
