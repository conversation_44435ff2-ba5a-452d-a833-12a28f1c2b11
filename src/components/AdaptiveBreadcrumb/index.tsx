import React, { useLayoutEffect, useRef, useState } from 'react';

import { ReactComponent as DesktopSvg } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as FileFolder } from '@/assets/images/sidepanel/folder.svg';
import { ReactComponent as FileSpace } from '@/assets/images/sidepanel/space.svg';

import type { IBreadcrumbItem, IBreadcrumbItemProps } from './BreadcrumbContext/index';
import { BreadcrumbContext } from './BreadcrumbContext/index';
import style from './index.less';
interface IBreadcrumbProps {
  onClick?: (item: IBreadcrumbItemProps) => void;
  separator?: React.ReactNode;
  children: React.ReactNode;
  list: { title: React.ReactNode; name: string; id: string }[];
}

export const AdaptiveBreadcrumb: React.FC<IBreadcrumbProps> = (props) => {
  const breadContainerRef = useRef<HTMLDivElement>(null);
  const breadShadowRef = useRef<HTMLDivElement>(null);
  const [hideItems, setHideItems] = useState<IBreadcrumbItem[]>([]);
  const [replaceItem, setReplaceItem] = useState<IBreadcrumbItem>();
  const { children, list, onClick, separator = '/' } = props;
  useLayoutEffect(() => {
    const handleChange = () => {
      if (breadShadowRef.current && breadContainerRef.current) {
        let currentWidth = 0;
        const { width: maxWidth } = breadContainerRef.current.getBoundingClientRect();
        const items: IBreadcrumbItem[] = [];
        const breadcrumbShadow = breadShadowRef.current;
        const breadcrumbChildren = breadcrumbShadow.children;
        if (list.length < 3) return;
        for (let i = 0; i < list.length; i++) {
          const child = breadcrumbChildren[i] as HTMLElement;
          const { width } = child.getBoundingClientRect();
          const { id } = child.dataset;
          currentWidth += width;
          items.push({
            icon: i === 0 ? list[i].id === 'Desktop' ? <DesktopSvg /> : <FileSpace /> : <FileFolder />,
            id: id!,
            name: list[i].name,
            label: list[i].title,
            width,
            onClick,
          });
        }
        if (maxWidth <= currentWidth) {
          // 60 是下拉按钮默认操作的宽度
          const diff = currentWidth - maxWidth + 60;
          let index = 1;
          let diffWidth = 0;
          const hideItems = [];
          // count - 1 最后一个永远保留
          for (let i = index; i < list.length - 2; i++, index++) {
            const item = items[i];
            diffWidth += item.width;
            if (diffWidth >= diff) break;
          }
          for (let i = 1; i < index; i++) {
            console.log(items[i]);
            hideItems.push(items[i]);
          }
          setHideItems(hideItems);
          setReplaceItem(items[0]);
        }
      }
    };
    handleChange();
    window.addEventListener('resize', handleChange);
    return () => {
      setHideItems([]);
      setReplaceItem(undefined);
    };
  }, [children, list]);
  return (
    <div ref={breadContainerRef} className={style.breadcrumbContainer}>
      <div className={style.breadcrumb}>
        <BreadcrumbContext hideItems={hideItems} replaceItem={replaceItem} separator={separator} onItemClick={onClick}>
          {children}
        </BreadcrumbContext>
      </div>
      <div ref={breadShadowRef} className={style.breadcrumb} style={{ position: 'absolute', visibility: 'hidden' }}>
        {children}
      </div>
    </div>
  );
};
