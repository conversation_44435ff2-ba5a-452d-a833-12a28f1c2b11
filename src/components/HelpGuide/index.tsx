import { CloseOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { useFormatMessage } from '@/modules/Locale';

import styles from './index.less';
import type { HelpGuideKey, HelpGuideProps } from './interface';
import { HelpGuideContent } from './interface';
import { ShortcutKey } from './shortcutKey';
import { UserManual } from './userManual';

export interface ShowGuideRef {
  show: (data: { fileType: string; key: string }) => void;
  hide: () => void;
}

const ShowGuide = ({ open, children }: { open: boolean; children: React.ReactNode }) => {
  return open ? children : null;
};

export const HelpGuide = forwardRef<ShowGuideRef, HelpGuideProps>((props, ref) => {
  const [open, setOpen] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string>(HelpGuideContent.helpDocGuide);
  const helpGuideRef = useRef<HTMLDivElement>(null);
  const [fileType, setFileType] = useState<string>();

  useImperativeHandle(ref, () => ({
    show: (data: { fileType: string; key: string }) => {
      setFileType(data.fileType);
      setOpen(true);
      setActiveKey(data.key);
    },
    hide: () => {
      setOpen(false);
    },
  }));
  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (helpGuideRef.current && !helpGuideRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    },
    [setOpen],
  );

  useEffect(() => {
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open, handleClickOutside]);

  return (
    <ShowGuide open={open}>
      <div ref={helpGuideRef} className={styles.helpGuide}>
        <Tabs
          activeKey={activeKey}
          className={styles.helpGuideTabs}
          items={[
            {
              label: useFormatMessage('helpGuide.userGuide'),
              key: HelpGuideContent.helpDocGuide,
              disabled: fileType === 'board',
            },
            { label: useFormatMessage('helpGuide.shortcutKey'), key: HelpGuideContent.helpDocShortcut },
          ]}
          tabBarExtraContent={{
            right: <CloseOutlined className={styles.helpGuideClose} onClick={() => setOpen(false)} />,
          }}
          onChange={(key) => setActiveKey(key as HelpGuideKey)}
        />

        {/* 使用指南组件 白板暂时不展示指南 */}
        <ShowGuide open={activeKey === HelpGuideContent.helpDocGuide && fileType !== 'board'}>
          <UserManual fileType={fileType} />
        </ShowGuide>

        {/* 快捷键组件  */}
        <ShowGuide open={activeKey === HelpGuideContent.helpDocShortcut}>
          <ShortcutKey fileType={fileType} />
        </ShowGuide>
      </div>
    </ShowGuide>
  );
});

HelpGuide.displayName = 'HelpGuide';
