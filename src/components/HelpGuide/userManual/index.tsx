// 使用指南组件
import { CheckOutlined } from '@ant-design/icons';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { fm, fm2 } from '@/modules/Locale';

import List from '../components/List';
import { VideoPlay } from '../components/VideoPlay';
import { getUserManualConfig } from '../utils';
import styles from './styles.less';

interface CheckItem {
  id?: number;
  url: string;
  value: string;
  detail: string;
  icon?: React.ReactNode;
}

export function UserManual({ fileType }: { fileType?: string }) {
  // 当前选中id
  const [checkId, setCheckId] = useState<number>();
  // 可设置最大范围
  const [max, setMax] = useState<number>();
  // 可设置最小范围
  const [min, setMin] = useState<number>();

  const onInternational = useCallback((text: string) => {
    return fm2(text);
  }, []);

  const configItem = useMemo(
    () => (fileType ? getUserManualConfig(fileType, onInternational) : []),
    [fileType, onInternational],
  );

  const configKeys = useMemo(() => Object.keys(configItem), [configItem]);

  const allHelpConfigList: CheckItem[] = useMemo(() => {
    return Object.keys(configItem).reduce<CheckItem[]>((acc, key) => {
      const items = configItem?.[key as keyof typeof configItem] || [];
      return [...acc, ...items];
    }, []);
  }, [configItem]);

  // init 初始化选中
  useEffect(() => {
    if (Array.isArray(allHelpConfigList)) {
      const [first] = allHelpConfigList;
      setCheckId(first.id);
    }
  }, [allHelpConfigList]);

  // 当前选中数据
  const checkInfo = useMemo(() => {
    return (
      allHelpConfigList?.find((item) => item.id === checkId) || {
        id: 1,
        url: '',
        value: '',
        detail: '',
      }
    );
  }, [checkId, allHelpConfigList]);

  useEffect(() => {
    const ans: number[] = allHelpConfigList.map((item) => item.id || 1);
    setMax(Math.max(...ans));
    setMin(Math.min(...ans));
  }, [allHelpConfigList]);

  // 渲染列表项的函数
  const renderListItems = useCallback(
    (items: CheckItem[]) => (
      <div className={styles.userManualList}>
        {items?.map((item) => (
          <ul key={item.value} className={styles.userManualItem} onClick={() => setCheckId(item.id)}>
            <li className={styles.userManualIcon}>
              <div>
                {item.icon}
                {item.value}
              </div>

              {checkId === item.id ? <CheckOutlined /> : null}
            </li>
          </ul>
        ))}
      </div>
    ),
    [checkId],
  );

  return (
    <>
      <VideoPlay
        downDisable={max === checkId}
        subDetail={checkInfo.detail}
        title={checkInfo.value}
        upDisable={min === checkId}
        url={checkInfo.url}
        onDown={() => {
          setCheckId((prev) => (prev === max ? prev : (prev || 0) + 1));
        }}
        onUp={() => {
          setCheckId((prev) => (prev === min ? prev : (prev || 0) - 1));
        }}
      />

      <div className={styles.userManual}>
        {configKeys.map((key) => {
          return (
            <List key={key} title={fm(`helpGuide.${key}`)}>
              {renderListItems(configItem?.[key as keyof typeof configItem] || [])}
            </List>
          );
        })}
      </div>
    </>
  );
}
