.shortcut<PERSON>ey {
  padding: 0 30px;
  height: calc(100% - 62px);
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
    display: none;
  }

  /* 额外兼容移动端 */
  -webkit-overflow-scrolling: touch;
}

.shortcutKeyTitle {
  line-height: 20px;
  font-size: 14px;
  padding-left: 3px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgb(229, 229, 229);
  padding-top: 22px;
  margin: 0;
  font-weight: 400;
}

.paddingTop0 {
  padding-top: 0;
}

.listContentClassName {
  margin-bottom: 0;
}

.shortcutKeyItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 5px 0;
  min-height: 44px;
  flex: 1;
  border-bottom: 1px solid rgb(229, 229, 229);
}

.shortcutKeyIcon {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.operator {
  display: flex;
  gap: 10px;
  flex: 1;
  font-size: 12px;

  > span {
    display: inline-block;
    box-sizing: border-box;
    padding: 0 3px;
    min-width: 16px;
    background-image: linear-gradient(-180deg, rgb(247, 247, 247), rgb(236, 236, 237));
    border-radius: 2px;
    line-height: 16px;
    text-align: center;
    font-style: normal;
  }
}
