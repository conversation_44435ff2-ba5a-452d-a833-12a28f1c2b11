// 快捷键组件
import { Typography } from 'antd';
import { useCallback, useMemo } from 'react';

import { fm2 } from '@/modules/Locale';
import { isWindows } from '@/utils/platform';

import List from '../components/List';
import { getConfigByType } from '../utils';
import styles from './styles.less';

interface ListItem {
  value: React.ReactNode;
  icon: React.ReactNode;
  mac: string[];
  win?: string[];
}

export function ShortcutKey({ fileType }: { fileType?: string }) {
  // 判断当前操作系统是否为Windows
  const isWin = isWindows();

  const onInternational = useCallback((text: string) => {
    return fm2(text);
  }, []);

  const configKeys = useMemo(
    () => (fileType ? Object.keys(getConfigByType(fileType, onInternational)) : []),
    [fileType, onInternational],
  );

  const configItem = useMemo(
    () => (fileType ? getConfigByType(fileType, onInternational) : {}),
    [fileType, onInternational],
  );

  // 渲染数据列的组件
  const renderListItems = (items: ListItem[]) => (
    <div className={styles.shortcutKeyList}>
      {items?.map((item) => (
        <ul key={item.mac?.join('')}>
          <li className={styles.shortcutKeyItem}>
            <div className={styles.shortcutKeyIcon}>
              <span>{item.icon}</span>
              <Typography.Text
                ellipsis={{
                  tooltip: item.value,
                }}
                style={{
                  fontSize: '12px',
                  maxWidth: 100,
                }}
              >
                {item.value}
              </Typography.Text>
            </div>

            <div className={styles.operator}>
              {(isWin && item.win ? item.win : item.mac)?.map((operator) => (
                <span key={operator} className={styles.operatorItem}>
                  {operator}
                </span>
              ))}
            </div>
          </li>
        </ul>
      ))}
    </div>
  );

  return (
    <div className={styles.shortcutKey}>
      {configKeys?.map((key) => {
        const item = configItem?.[key as keyof typeof configItem];

        return (
          <List
            key={key}
            listContentClassName={styles.listContentClassName}
            title={fm2(`helpGuide.${key}`)}
            titleClassName={styles.shortcutKeyTitle}
          >
            {renderListItems(item as ListItem[])}
          </List>
        );
      })}
    </div>
  );
}
