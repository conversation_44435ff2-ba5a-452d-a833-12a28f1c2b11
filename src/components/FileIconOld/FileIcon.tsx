import { useFileIcon24 } from '../../contexts/file-icons';
import { StyledContainer, StyledIconImage } from './FileIcon.styled';
import type { FileIconType } from './type';

export interface IconProps {
  size?: number;
  iconType: `${FileIconType}`;
  className?: string;
}

export const FileIcon = (props: IconProps) => {
  const { iconType, className, size = 24 } = props;

  const fileIcon24 = useFileIcon24(iconType);

  return (
    <StyledContainer className={className}>
      <StyledIconImage $size={size} srcSet={fileIcon24} />
    </StyledContainer>
  );
};
