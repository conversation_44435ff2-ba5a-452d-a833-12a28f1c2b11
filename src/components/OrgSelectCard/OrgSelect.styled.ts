import styled from 'styled-components';

import { OrgList } from './OrgList';

export const StyledOrgSelect = styled.div`
  box-sizing: border-box;
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
`;

export const StyledOrgBreadcrumb = styled.div`
  box-sizing: border-box;
  margin: 0 12px 8px;
  flex: 0 0 20px;
`;

export const StyledOrgContainer = styled.div`
  box-sizing: border-box;
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
`;

export const StyledOrgList = styled(OrgList)`
  flex: 1;
`;

export const StyledOrgSearchList = styled(OrgList)`
  &.ant-list {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
  }
`;

export const StyledLoadingContainer = styled.div`
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-layout-color-bg-white);
`;
