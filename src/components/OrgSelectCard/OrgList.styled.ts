import { Checkbox, List } from 'antd';
import styled from 'styled-components';

export const StyledOrgList = styled(List)`
  min-height: 0;
  background: var(--white);

  & .rc-virtual-list-scrollbar {
    right: 0;
    left: unset !important;
  }
`;

export const StyledCheckbox = styled(Checkbox)<{ $height: number }>`
  &.ant-checkbox-wrapper {
    box-sizing: border-box;
    width: 100%;
    height: ${({ $height }) => `${$height - 4}px`};
    margin-bottom: 4px;
    overflow: hidden;
    padding-left: 12px;

    &:active,
    &:hover {
      background-color: var(--color-bg-hover);
    }

    & > span {
      display: block;
      overflow: hidden;
      padding: 0;

      &:first-child {
        flex: 0 0 16px;
        margin-right: 8px;
      }

      &:last-child {
        flex: 1;
        min-width: 0;
      }
    }
  }
`;

export const StyledItem = styled.div<{ $height: number }>`
  box-sizing: border-box;
  display: block;
  width: 100%;
  height: ${({ $height }) => `${$height - 4}px`};
  padding-bottom: 4px;
  padding-left: 12px;

  &:hover {
    background-color: var(--color-fill-2);
  }
`;
