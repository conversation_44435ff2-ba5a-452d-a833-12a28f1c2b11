import React, { memo } from 'react';

import EmptyPng from '@/assets/components/empty/<EMAIL>';
import { fm } from '@/modules/Locale';

import { StyledOrgSelectEmpty, StyledOrgSelectEmptyImage, StyledOrgSelectEmptyText } from './OrgSelectEmpty.styled';

export const OrgSelectEmpty = memo(() => {
  return (
    <StyledOrgSelectEmpty>
      <StyledOrgSelectEmptyImage src={EmptyPng} />
      <StyledOrgSelectEmptyText>{fm('Organization.noData')}</StyledOrgSelectEmptyText>
    </StyledOrgSelectEmpty>
  );
});
