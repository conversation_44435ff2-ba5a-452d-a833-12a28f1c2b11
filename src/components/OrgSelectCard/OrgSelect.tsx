import React, { memo } from 'react';

import { ZERO } from '../../configs/configs';
import type { OrgBreadcrumbProps } from './OrgBreadcrumb';
import { OrgBreadcrumb } from './OrgBreadcrumb';
import type { OrgListProps } from './OrgList';
import {
  StyledLoadingContainer,
  StyledOrgBreadcrumb,
  StyledOrgContainer,
  StyledOrgList,
  StyledOrgSearchList,
  StyledOrgSelect,
} from './OrgSelect.styled';
import type { OrgSelectAllProps } from './OrgSelectAll';
import { OrgSelectAll } from './OrgSelectAll';
import { OrgSelectEmpty } from './OrgSelectEmpty';

export interface OrgSelectedProps<T = any> {
  loading: boolean;
  className?: string;
  org: OrgListProps<T>;
  search?: OrgListProps<T>;
  breadcrumb?: Pick<OrgBreadcrumbProps, 'crumbs'>;
  selectAll?: OrgSelectAllProps;
}

export const OrgSelect = memo((props: OrgSelectedProps) => {
  const { loading, org, search, selectAll, breadcrumb, className } = props;
  return (
    <StyledOrgSelect className={className}>
      {breadcrumb && (
        <StyledOrgBreadcrumb>
          <OrgBreadcrumb {...breadcrumb} />
        </StyledOrgBreadcrumb>
      )}
      <StyledOrgContainer>
        {selectAll && <OrgSelectAll {...selectAll} />}
        <StyledOrgList {...org} />
        {org.data.length === ZERO && !search && <OrgSelectEmpty />}
      </StyledOrgContainer>
      {search && <StyledOrgSearchList {...search} />}
      {!!search && search.data.length === ZERO && <OrgSelectEmpty />}
      {loading && <StyledLoadingContainer>{/* <LoadingGif size={LOADING_ICON_SIZE_MINE} /> */}</StyledLoadingContainer>}
    </StyledOrgSelect>
  );
});
