import type { ReactNode } from 'react';
import React, { memo } from 'react';

import { LinkButton } from '../LinkButton';
import { StyledOrgSelectedHeader, StyledOrgSelectedTitleContainer } from './OrgSelectedHeader.styled';
export interface OrgSelectedHeaderProps {
  title: ReactNode;
  onPress?: () => void;
  pressText?: string;
  disabled?: boolean;
}

export const OrgSelectedHeader = memo((props: OrgSelectedHeaderProps) => {
  const { title, onPress, pressText, disabled } = props;

  return (
    <StyledOrgSelectedHeader>
      <StyledOrgSelectedTitleContainer>{title}</StyledOrgSelectedTitleContainer>
      {onPress && pressText && (
        <LinkButton disabled={disabled} onClick={onPress}>
          {pressText}
        </LinkButton>
      )}
    </StyledOrgSelectedHeader>
  );
});
