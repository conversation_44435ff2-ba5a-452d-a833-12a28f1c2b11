import { RedoOutlined } from '@ant-design/icons';
import styled from 'styled-components';

export const StyledOrgBreadcrumb = styled.div`
  box-sizing: border-box;
  display: flex;
  width: 100%;
  height: 22px;
  overflow: hidden;
  font-size: 13px;
`;

export const StyledOrgBreadcrumbDirection = styled.div`
  flex: 1;
  box-sizing: border-box;
  display: flex;
`;

export const StyledResetIconContainer = styled.div`
  box-sizing: border-box;
  flex: 0 0 32px;
  display: flex;
  align-items: center;
  width: 32px;
`;

export const StyledResetIcon = styled(RedoOutlined)`
  display: block;
  width: 20px;
  height: 20px;
  margin: 0 auto;
  color: var(--theme-text-color-secondary);

  &:hover,
  &:active {
    cursor: pointer;
  }
`;
