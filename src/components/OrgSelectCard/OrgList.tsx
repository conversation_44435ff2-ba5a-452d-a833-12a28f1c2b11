import { Checkbox, List } from 'antd';
import classNames from 'classnames';
import VirtualList from 'rc-virtual-list';
import type { ReactNode, UIEvent } from 'react';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import styles from './OrgList.less';
export interface OrgListProps<T = any> {
  data: T[];
  itemHeight: number;
  itemKey: string;
  onScroll?: () => void;
  itemRender: (item: T) => ReactNode;
  className?: string;
}

export const OrgList = memo((props: OrgListProps) => {
  const { data, itemKey, onScroll, itemHeight, itemRender, className } = props;
  const [height, setHeight] = useState(100);
  const listRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<ResizeObserver | null>(null);

  const onScrollHandler = useCallback(
    (e: UIEvent) => {
      if (onScroll && Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - height) <= 1) {
        onScroll();
      }
    },
    [height, onScroll],
  );

  useEffect(() => {
    observerRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    });

    if (listRef.current) {
      observerRef.current?.observe?.(listRef.current);
    }
    return () => {
      observerRef.current?.disconnect?.();
    };
  }, []);

  return (
    <List ref={listRef} className={classNames(className, styles.styledOrgList, styles.styledCheckbox)}>
      <VirtualList data={data} height={height} itemHeight={itemHeight} itemKey={itemKey} onScroll={onScrollHandler}>
        {(item) => {
          return item?.checkable ? (
            <Checkbox
              checked={item.checked}
              disabled={item?.disabled}
              style={{ height: itemHeight }}
              onChange={item?.onCheck}
            >
              {itemRender(item)}
            </Checkbox>
          ) : (
            <div className={styles.styledItem} style={{ height: itemHeight }} onClick={item?.onPress}>
              {itemRender(item)}
            </div>
          );
        }}
      </VirtualList>
    </List>
  );
});
