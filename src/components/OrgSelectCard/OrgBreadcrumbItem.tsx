import classnames from 'classnames';
import React, { memo } from 'react';

import {
  StyledOrgBreadcrumbIcon,
  StyledOrgBreadcrumbItem,
  StyledOrgBreadcrumbItemArrow,
} from './OrgBreadcrumbItem.styled';
import type { CrumbsType } from './type';

interface OrgBreadcrumbItemProps extends CrumbsType {
  isScroll: boolean;
}

export const OrgBreadcrumbItem = memo((props: OrgBreadcrumbItemProps) => {
  const { title, onClick, isScroll } = props;

  return (
    <>
      <StyledOrgBreadcrumbItem
        $isScroll={isScroll}
        className={classnames({
          'last-item': !onClick,
          'allow-click': !!onClick,
        })}
        title={title}
        onClick={onClick}
      >
        {title}
      </StyledOrgBreadcrumbItem>
      {!!onClick && (
        <StyledOrgBreadcrumbIcon>
          <StyledOrgBreadcrumbItemArrow />
        </StyledOrgBreadcrumbIcon>
      )}
    </>
  );
});
