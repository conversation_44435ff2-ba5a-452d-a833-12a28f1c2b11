import styled from 'styled-components';

import { Image } from '../Image';

export const StyledOrgSelectedEmpty = styled.div`
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 80px;
`;

export const StyledOrgSelectedEmptyImage = styled(Image)`
  box-sizing: border-box;
  display: inline-block;
  width: 160px;
  height: 130px;
`;

export const StyledOrgSelectedEmptyText = styled.div`
  box-sizing: border-box;
  width: 166px;
  font-size: 13px;
  line-height: 20px;
  word-break: break-word;
  color: var(--theme-text-color-secondary);
  text-align: center;
  cursor: default;
`;
