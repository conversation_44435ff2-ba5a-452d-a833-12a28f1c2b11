import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { memo, useCallback } from 'react';

import { fm2 } from '@/modules/Locale';

import { OrgSelectAllStyled } from './OrgSelectAll.styled';

export interface OrgSelectAllProps {
  checked: boolean;
  indeterminate: boolean;
  onPress: (value: boolean) => void;
}

export const OrgSelectAll = memo((props: OrgSelectAllProps) => {
  const { onPress, ...extra } = props;
  const onClickHandler = useCallback(
    (e: CheckboxChangeEvent) => {
      onPress(e.target.checked);
    },
    [onPress],
  );
  return (
    <OrgSelectAllStyled {...extra} onChange={onClickHandler}>
      {fm2('hanover.selectAll')}
    </OrgSelectAllStyled>
  );
});
