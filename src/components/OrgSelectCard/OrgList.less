.styledOrgList {
  min-height: 0;
  background: var(--theme-layout-color-bg-white);

  & .rc-virtual-list-scrollbar {
    right: 0;
    left: unset !important;
  }
}

.styledCheckbox {
  :global {
    .ant-checkbox-wrapper {
      box-sizing: border-box;
      width: 100%;
      margin-bottom: 4px;
      overflow: hidden;
      padding-left: 12px;

      &:active,
      &:hover {
        background-color: var(--theme-menu-color-bg-hover);
      }

      & > span {
        display: block;
        overflow: hidden;
        padding: 0;

        &:first-child {
          flex: 0 0 16px;
          margin-right: 8px;
        }

        &:last-child {
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}

.styledItem {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding-bottom: 4px;
  padding-left: 12px;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}
