import { Tooltip } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';

import { fm } from '@/modules/Locale';
import { isRtl } from '@/themes/RtlAdapterProvider';

import { ZERO } from '../../configs/configs';
import {
  StyledOrgBreadcrumb,
  StyledOrgBreadcrumbDirection,
  StyledResetIcon,
  StyledResetIconContainer,
} from './OrgBreadcrumb.styled';
import { OrgBreadcrumbItem } from './OrgBreadcrumbItem';
import type { CrumbsType } from './type';

export interface OrgBreadcrumbProps {
  crumbs: CrumbsType[];
  className?: string;
}
export const OrgBreadcrumb = memo(({ crumbs, className }: OrgBreadcrumbProps) => {
  const [isScroll, setIsScroll] = useState(false);
  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (ref.current && ref.current.clientWidth < ref.current.scrollWidth) {
      ref.current.scrollTo(isRtl() ? -ref?.current?.scrollWidth : ref?.current?.scrollWidth, ZERO);
      setIsScroll(true);
    } else {
      ref.current?.scrollTo?.(ZERO, ZERO);
      setIsScroll(false);
    }
  }, [crumbs]);

  return (
    <StyledOrgBreadcrumb ref={ref} className={className}>
      <StyledOrgBreadcrumbDirection>
        {crumbs.map((crumb) => (
          <OrgBreadcrumbItem key={crumb.id} {...crumb} isScroll={isScroll} />
        ))}
      </StyledOrgBreadcrumbDirection>
      {crumbs?.[0]?.onClick && (
        <StyledResetIconContainer>
          <Tooltip placement="top" title={fm('Organization.backToFirstLevel')}>
            <div onClick={crumbs?.[0]?.onClick}>
              <StyledResetIcon />
            </div>
          </Tooltip>
        </StyledResetIconContainer>
      )}
    </StyledOrgBreadcrumb>
  );
});
