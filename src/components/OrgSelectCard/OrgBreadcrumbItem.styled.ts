import styled from 'styled-components';

import { ReactComponent as RightArrowSvg } from '@/assets/icon/right-arrow-16.svg';
import { isRtl } from '@/themes/RtlAdapterProvider';

export const StyledOrgBreadcrumbItem = styled.div<{ $isScroll: boolean }>`
  box-sizing: border-box;
  white-space: nowrap;
  cursor: default;
  color: var(--theme-text-color-secondary);

  &.allow-click {
    &:hover {
      color: var(--theme-text-color-default);
      cursor: pointer;
    }
  }

  &.last-item {
    font-weight: 500;
    max-width: '${({ $isScroll }) => ($isScroll ? '80px' : 'unset')}';
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--theme-breadcrumb-color-active);
  }
`;

export const StyledOrgBreadcrumbIcon = styled.div`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
`;
export const StyledOrgBreadcrumbItemArrow = styled(RightArrowSvg)`
  width: 14px;
  height: 14px;
  color: var(--theme-text-color-secondary);
  transform: '${() => (isRtl() ? 'rotate(180deg) ' : 'unset')}';
`;
