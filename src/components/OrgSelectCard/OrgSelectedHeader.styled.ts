import styled from 'styled-components';

export const StyledOrgSelectedHeader = styled.div`
  box-sizing: border-box;
  flex: 0 0 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 38px;
  width: 100%;
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledOrgSelectedTitleContainer = styled.div`
  box-sizing: border-box;
  flex: 1;
  display: flex;
  min-width: 0;
  margin-right: 8px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: default;
`;
