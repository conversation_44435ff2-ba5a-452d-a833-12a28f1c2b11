import type { ReactNode } from 'react';

import { OrgType } from '../../service/userOrg/response';

export interface CrumbsType {
  id: number;
  title: string;
  onClick?: () => void;
}

export interface OrgItemType {
  id: number;
  title: string;
  avatar?: string;
  email?: string;
  icon?: ReactNode;
  type: OrgType;
  checked: boolean;
  checkable: boolean;
  teamRole?: string;
  onPress?: () => void;
  onCheck?: () => void;
  onCancel?: () => void;
}

export interface OrgSelectAll {
  checked: boolean;
  indeterminate: boolean;
  onPress: (value: boolean) => void;
}

export { OrgType };
