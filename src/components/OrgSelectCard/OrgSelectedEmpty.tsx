import React, { memo } from 'react';

import EmptyPng from '@/assets/components/empty/<EMAIL>';
import { fm } from '@/modules/Locale';

import {
  StyledOrgSelectedEmpty,
  StyledOrgSelectedEmptyImage,
  StyledOrgSelectedEmptyText,
} from './OrgSelectedEmpty.styled';

export const OrgSelectedEmpty = memo(() => {
  return (
    <StyledOrgSelectedEmpty>
      <StyledOrgSelectedEmptyImage src={EmptyPng} />
      <StyledOrgSelectedEmptyText>{fm('Organization.noMember')}</StyledOrgSelectedEmptyText>
      <StyledOrgSelectedEmptyText>{fm('Organization.searchOrExpand')}</StyledOrgSelectedEmptyText>
    </StyledOrgSelectedEmpty>
  );
});
