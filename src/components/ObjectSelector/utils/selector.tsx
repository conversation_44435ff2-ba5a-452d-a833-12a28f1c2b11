import { Avatar } from 'antd';
import type { Dispatch, MutableRefObject, ReactNode, SetStateAction } from 'react';
import styled from 'styled-components';

import { CompanyIconSvg } from '@/assets/main/ConpanyIconSvg';
import { DepartmentIconSvg } from '@/assets/main/DepartmentIconSvg';
import { getFileIcon } from '@/utils/fileIcon';

import { IconTypes } from '../enums/enums';
import type { SelectItemBasicType, SelectItemType } from '../hooks/types';
import {
  StyledClearIcon,
  StyledSelectedItemContainer,
  StyledSelectedItemDescription,
  StyledSelectedItemInfo,
  StyledSelectedItemInfoContent,
  StyledSelectedItemTitle,
} from '../Selected.styled';
import { StyledArrowIcon, StyledItemContainer, StyledItemContainerLabel } from '../SelectList.styled';
import type { OrgTreeItemType } from '../services/type';
import { convertBasicSelectList } from './convert';

export const StyledIcon = styled.div`
  display: flex;
  align-items: center;
  flex: 0 0 24px;
  min-width: 0;
  margin-right: 8px;

  & > svg,
  & img {
    width: 24px;
    height: 24px;
  }
`;

export const StyledAvatar = styled(Avatar)`
  &.ant-avatar {
    width: 24px;
    height: 24px;
    flex: 0 0 24px;
    margin-right: 8px;
  }
`;

export function getIcon(type?: IconTypes): ReactNode | undefined {
  switch (type) {
    case IconTypes.Company:
      return <CompanyIconSvg />;
    case IconTypes.Department:
      return <DepartmentIconSvg />;
    case IconTypes.Space:
      return getFileIcon({ type: 1, subType: 2, isSpace: true });
    default:
      return undefined;
  }
}

export function renderSelectItem(item: SelectItemType): ReactNode {
  const { label, avatar, onPress, iconType } = item;
  const icon = getIcon(iconType);
  return (
    <StyledItemContainer className={item?.onPress && 'link'} onClick={onPress}>
      {icon && <StyledIcon>{icon}</StyledIcon>}
      {avatar && <StyledAvatar src={avatar}>{label}</StyledAvatar>}
      <StyledItemContainerLabel title={label}>{label}</StyledItemContainerLabel>
      {onPress && <StyledArrowIcon />}
    </StyledItemContainer>
  );
}

interface SelectedItemBasicType extends SelectItemBasicType {
  description?: string;
  onPress?: () => void;
}

export function renderSelectedItem(item: SelectedItemBasicType): ReactNode {
  const { label, avatar, iconType, onPress, description } = item;
  const icon = getIcon(iconType);
  return (
    <StyledSelectedItemContainer>
      <StyledSelectedItemInfo>
        {icon && <StyledIcon>{icon}</StyledIcon>}
        {avatar && <StyledAvatar src={avatar}>{label}</StyledAvatar>}
        <StyledSelectedItemInfoContent>
          <StyledSelectedItemTitle title={label}>{label}</StyledSelectedItemTitle>
          {description && <StyledSelectedItemDescription>{description}</StyledSelectedItemDescription>}
        </StyledSelectedItemInfoContent>
        {onPress && <StyledClearIcon as="div" onClick={onPress} />}
      </StyledSelectedItemInfo>
    </StyledSelectedItemContainer>
  );
}

export function scrollHandler(params: {
  total: number;
  current: number;
  pageSize: number;
  list: OrgTreeItemType[];
  isEndRef: MutableRefObject<boolean>;
  pageCurrentRef: MutableRefObject<number>;
  setList: Dispatch<SetStateAction<SelectItemBasicType[]>>;
}) {
  const { total, list, setList, current, pageSize, isEndRef, pageCurrentRef } = params;
  if (!list || list?.length <= 0 || list.length < pageSize) {
    isEndRef.current = true;
  }
  setList((oldList) => {
    const newList = pageCurrentRef.current === current ? oldList.concat(convertBasicSelectList(list)) : oldList;
    if (total === newList.length) {
      isEndRef.current = true;
    }
    return newList;
  });
}
