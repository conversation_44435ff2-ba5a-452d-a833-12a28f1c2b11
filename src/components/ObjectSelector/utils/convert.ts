import { IconTypes } from '../enums/enums';
import type { SelectItemBasicType, SelectItemType } from '../hooks/types';
import type { OrgTreeItemType } from '../services/type';

export function convertBasicSelectList(list: OrgTreeItemType[]): SelectItemBasicType[] {
  return (
    list?.map?.(({ id, name, key, ...item }) => {
      const basicItem: SelectItemBasicType = {
        id,
        key,
        label: name,
      };
      if ('iconType' in item) {
        basicItem.iconType = item.iconType;
      } else {
        basicItem.avatar = item.avatar;
        basicItem.email = item.email;
      }
      if ('creator' in item) {
        basicItem.creator = item.creator;
      }
      return basicItem;
    }) ?? []
  );
}

export function formatterAllowCheck(params: {
  isSelectableCompany: boolean;
  list: SelectItemBasicType[];
}): SelectItemType[] {
  const { list, isSelectableCompany } = params;
  return list.map((item) => {
    const basicItem: SelectItemType = {
      ...item,
      allowCheck: true,
    };
    if ('iconType' in item && item.iconType && [IconTypes.Company, IconTypes.Department].includes(item.iconType)) {
      basicItem.allowCheck = isSelectableCompany;
    }
    return basicItem;
  });
}

export function convertSelectList(params: {
  list?: SelectItemBasicType[];
  isSearch?: boolean;
  checkSelected: (item: SelectItemBasicType) => boolean;
  updateChecked: (params: { checked: boolean; item: any }) => void;
  isSelectableCompany: boolean;
  onPressItem?: (departmentId: number) => void;
}): SelectItemType[] | undefined {
  const { list, onPressItem, checkSelected, updateChecked, isSelectableCompany } = params;
  if (!list) {
    return undefined;
  }
  return formatterAllowCheck({ list, isSelectableCompany }).map((item) => {
    const basicItem: SelectItemType = {
      ...item,
      checked: checkSelected(item),
      onCheck: (checked: boolean) => {
        updateChecked({ checked, item });
      },
    };
    if ('iconType' in item && item.iconType && [IconTypes.Company, IconTypes.Department].includes(item.iconType)) {
      basicItem.onPress = onPressItem
        ? () => {
            if (typeof item.id === 'number') {
              onPressItem(item.id);
            }
          }
        : undefined;
    }
    return basicItem;
  });
}
