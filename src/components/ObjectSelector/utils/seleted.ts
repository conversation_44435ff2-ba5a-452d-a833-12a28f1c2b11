import type { SelectedItemBasicType, SelectItemBasicType } from '../hooks/types';

export function convertSelectToSelected(item: SelectItemBasicType): SelectedItemBasicType {
  const { key, id, label, avatar, iconType, creator } = item;
  const basicItem: SelectedItemBasicType = {
    key,
    id,
    label,
    avatar,
    creator,
    iconType,
  };
  if ('email' in item) {
    basicItem.description = item.email;
  }
  return basicItem;
}

export function updateSelectedHandler(
  arr: SelectedItemBasicType[],
  cur: SelectedItemBasicType,
): SelectedItemBasicType[] {
  if (!arr.find((item) => item.id === cur.id)) {
    arr.push(cur);
  }
  return arr;
}
