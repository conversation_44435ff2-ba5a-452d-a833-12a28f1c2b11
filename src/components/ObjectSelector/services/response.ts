export interface OrgUsersResponse {
  total: number;
  departmentUserIDs: number[];
  users: {
    id: number;
    teamId: number;
    teamRole: string;
    name: string;
    avatar?: string;
    email?: string;
    canBother: boolean;
    handoverMenu: {
      token: string;
      hasHistory: boolean;
    };
  }[];
}

interface DepartmentResponseType {
  id: number;
  name: string;
  teamId: number;
  allMemberCount: number;
  canBother: boolean;
}

export interface OrgDepartmentsResponse {
  rootVisible: boolean;
  currentDepartment: DepartmentResponseType;
  subdepartments: DepartmentResponseType[];
}

interface UserResponse {
  id: number;
  name: string;
  avatar: string;
  isOutsider: boolean;
  canBother: boolean;
  email?: string;
  parentDepartments: {
    id: number;
    name: string;
    teamId: number;
    canBother: boolean;
  }[];
}

interface DepartmentResponse {
  id: number;
  name: string;
  avatar: string;
  isOutsider: boolean;
  canBother: boolean;
  email?: string;
  parentDepartments: {
    id: number;
    name: string;
    teamId: number;
    canBother: boolean;
  }[];
}

export interface SpaceItemResponse {
  guid: string;
  name: string;
  created: {
    id: number;
    name: string;
    avatar: string;
    email: string;
  };
  totalSize: number;
  usedSize: number;
  isLimited: boolean;
}

export interface SpaceResponse {
  pageNum: number;
  pageSize: number;
  totalPage: number;
  totalSize: number;
  items: SpaceItemResponse[];
}

interface SearchUserResponse {
  type: 'user';
  user: UserResponse;
  department: never;
}

interface SearchDepartmentResponse {
  type: 'department';
  department: DepartmentResponse;
  user: never;
}

export type SearchOrgItemResponse = SearchUserResponse | SearchDepartmentResponse;

export interface SearchOrgResponse {
  next: number;
  results: SearchOrgItemResponse[];
}
