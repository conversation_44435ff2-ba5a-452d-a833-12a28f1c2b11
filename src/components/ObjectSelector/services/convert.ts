import { ROOT_DEPARTMENT } from '../../../configs/configs';
import { IconTypes } from '../enums/enums';
import type { OrgDepartmentsResponse, OrgUsersResponse, SearchOrgItemResponse, SpaceItemResponse } from './response';
import type { OrgTreeItemType, OrgTreeType } from './type';

export function convertOrgTree(response: {
  user: OrgUsersResponse;
  departmentId: number;
  department?: OrgDepartmentsResponse;
}): OrgTreeItemType[] {
  const { user, department, departmentId } = response;
  const { users } = user;
  const userList =
    users?.map?.(({ id, name, avatar, email }) => ({
      key: `${departmentId}-${id}`,
      name,
      email: email ?? '',
      avatar: avatar ?? '',
      id,
    })) ?? [];
  if (!department) {
    return userList;
  }
  const { subdepartments } = department;

  const departmentList =
    subdepartments?.map?.(({ id, name }) => ({
      key: `${departmentId}-${id}`,
      id,
      name,
      iconType: id === ROOT_DEPARTMENT ? IconTypes.Company : IconTypes.Department,
    })) ?? [];

  return [...departmentList, ...userList];
}

export function convertSpaceList(list?: SpaceItemResponse[]): OrgTreeItemType[] {
  return (
    list?.map(({ guid, name, created }) => ({
      id: guid,
      key: guid,
      name,
      iconType: IconTypes.Space,
      creator: {
        ...created,
      },
    })) ?? []
  );
}

export function convertSearchOrgList(response: SearchOrgItemResponse[]): OrgTreeType['list'] {
  return (
    response?.map?.(({ type, user, department }) => {
      if (type === 'user') {
        const { id, name, avatar, email } = user;
        return {
          key: `user-${id}`,
          id,
          name,
          avatar,
          email: email ?? '',
        };
      }
      const { id, name } = department;
      return {
        key: `department-${id}`,
        id,
        name,
        iconType: id === ROOT_DEPARTMENT ? IconTypes.Company : IconTypes.Department,
      };
    }) ?? []
  );
}
