import { DEFAULT_SEARCH_LIMIT, ROOT_DEPARTMENT, ZERO } from '../../../configs/configs';
import { IconTypes } from '../enums/enums';
import { getOrgDepartmentsRequest, getOrgUsersRequest, getSpaceRequest, searchOrgRequest } from './api';
import { convertOrgTree, convertSearchOrgList, convertSpaceList } from './convert';
import type { OrgUsersParams, SearchOrgFilterParams, SpaceParams } from './request';
import type { OrgTreeType, SpaceListType } from './type';

export async function getOrgTree(params: OrgUsersParams): Promise<OrgTreeType> {
  return Promise.all([getOrgUsersRequest(params), getOrgDepartmentsRequest(params)]).then(([user, department]) => {
    const { total } = user;
    const {
      currentDepartment: { id, name },
    } = department;
    return {
      total,
      currentDepartment: {
        id,
        name,
        key: `${id}`,
        iconType: id === ROOT_DEPARTMENT ? IconTypes.Company : IconTypes.Department,
      },
      list: convertOrgTree({
        user,
        department,
        departmentId: params.departmentId,
      }),
    };
  });
}

export async function getOrgUsers(params: OrgUsersParams): Promise<Omit<OrgTreeType, 'currentDepartment'>> {
  return getOrgUsersRequest(params).then((user) => {
    const { total } = user;
    return {
      total,
      list: convertOrgTree({ user, departmentId: params.departmentId }),
    };
  });
}

export async function getSpace(params: SpaceParams): Promise<SpaceListType> {
  return getSpaceRequest(params).then((res) => {
    const { items, totalSize = ZERO } = res ?? {};
    return {
      total: totalSize,
      list: convertSpaceList(items),
    };
  });
}

export async function searchOrg(
  params: {
    keyword: string;
  } & SearchOrgFilterParams,
): Promise<OrgTreeType['list']> {
  const { keyword, department, user } = params;
  const { includeDisabledMember = true } = user ?? {};
  return searchOrgRequest({
    keyword,
    limit: DEFAULT_SEARCH_LIMIT,
    filter: {
      department,
      user: {
        includeDisabledMember,
        includeRecentContact: false,
        includeTeamMember: true,
      },
    },
  }).then(convertSearchOrgList);
}
