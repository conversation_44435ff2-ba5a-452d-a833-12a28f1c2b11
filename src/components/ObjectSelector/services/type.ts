import type { IconTypes } from '../enums/enums';

interface OrgTreeUserItemType {
  key: string;
  name: string;
  email: string;
  avatar: string;
  id: number;
}

interface OrgTreeDepartmentType {
  key: string;
  name: string;
  iconType: IconTypes;
  id: number;
}

interface SpaceType {
  key: string;
  id: string;
  iconType: IconTypes.Space;
  name: string;
  creator: {
    id: number;
    name: string;
    avatar?: string;
    email?: string;
  };
}

export type OrgTreeItemType = OrgTreeUserItemType | OrgTreeDepartmentType | SpaceType;

export interface OrgTreeType {
  total: number;
  currentDepartment: OrgTreeDepartmentType;
  list: OrgTreeItemType[];
}

export interface SpaceListType {
  list: OrgTreeItemType[];
  total: number;
}
