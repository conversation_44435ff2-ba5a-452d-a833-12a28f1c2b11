import type { StringifiableRecord } from 'query-string';

export interface OrgDepartmentsParams extends StringifiableRecord {
  isAdmin: boolean;
  departmentId: number;
}

export interface OrgUsersParams extends OrgDepartmentsParams {
  page: number;
  perPage: number;
}

export interface SpaceParams extends StringifiableRecord {
  pageNum: number;
  pageSize: number;
  spaceName?: string;
}

export interface SearchOrgFilterParams {
  department?: unknown;
  user?: {
    includeRecentContact?: boolean;
    includeTeamMember?: boolean;
    includeDisabledMember?: boolean;
  };
}

export interface SearchOrgParams {
  keyword: string;
  limit?: number;
  filter?: SearchOrgFilterParams;
}
