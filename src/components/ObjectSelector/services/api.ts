import { CommonApi } from '@/api/Request';
import type { ResponseDataType } from '@/service/types';

import type { OrgDepartmentsParams, OrgUsersParams, SearchOrgParams, SpaceParams } from './request';
import type { OrgDepartmentsResponse, OrgUsersResponse, SearchOrgResponse, SpaceResponse } from './response';

export async function getOrgUsersRequest(params: OrgUsersParams) {
  const { isAdmin, departmentId, ...query } = params;
  const res = await CommonApi.get<OrgUsersResponse>(
    `/org/departments/${departmentId}${isAdmin ? '/admin' : ''}/users`,
    {
      params: query,
    },
  );
  return res.data;
}

export async function getOrgDepartmentsRequest(params: OrgDepartmentsParams) {
  const { isAdmin, departmentId } = params;
  const res = await CommonApi.get<OrgDepartmentsResponse>(
    `/org/departments/${departmentId}${isAdmin ? '/admin' : ''}/shadow_subtree`,
  );
  return res.data;
}

export async function getSpaceRequest(query: SpaceParams) {
  const res = await CommonApi.get<ResponseDataType<SpaceResponse>>(`/file/quota/spaces_quota`, {
    params: query,
  });
  return res.data.data;
}

export async function searchOrgRequest(params: SearchOrgParams) {
  const { ...postParams } = params;
  const res = await CommonApi.post<SearchOrgResponse>(`/search`, postParams);
  return res.data?.results ?? [];
}
