import { Checkbox } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { memo, useCallback } from 'react';
import styled from 'styled-components';

import { fm } from '@/modules/Locale';

const StyledSelectAll = styled(Checkbox)`
  flex: 0 0 20px;
  height: 20px;

  &.ant-checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding-left: 12px;
  }
`;

export interface SelectAllProps {
  checked: boolean;
  disabled?: boolean;
  indeterminate: boolean;
  onPress: (checked: boolean) => void;
}

export const SelectAll = memo((props: SelectAllProps) => {
  const { checked, disabled, onPress, indeterminate } = props;

  const i18n_selectAll = fm('CapacityManage.selectAll');

  const onChangeHandler = useCallback(
    (event: CheckboxChangeEvent) => {
      const {
        target: { checked },
      } = event;
      onPress(checked);
    },
    [onPress],
  );

  return (
    <StyledSelectAll checked={checked} disabled={disabled} indeterminate={indeterminate} onChange={onChangeHandler}>
      {i18n_selectAll}
    </StyledSelectAll>
  );
});
