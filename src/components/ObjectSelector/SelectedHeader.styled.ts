import styled from 'styled-components';

import { LinkButton } from '../LinkButton';

export const StyledSelectedHeader = styled.div`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 12px;
  width: 100%;
  height: 38px;
  flex: 0 0 38px;
  border-bottom: 1px solid var(--color-border-separator-lighter);
`;

export const StyledSelectedTitle = styled.div`
  flex: 1;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

export const StyledLinkButton = styled(LinkButton)`
  &.ant-btn {
    margin-left: 8px;
    height: 22px;
  }
`;
