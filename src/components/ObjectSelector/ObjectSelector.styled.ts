import { Divider } from 'antd';
import styled from 'styled-components';

export const StyledObjectSelector = styled.div<{ $height: number }>`
  display: flex;
  width: 100%;
  /* stylelint-disable-next-line function-no-unknown */
  height: ${({ $height }) => `${$height}px`};
  border-radius: 4px;
  border: 1px solid var(--color-border-separator-lighter);
  color: var(--theme-basic-color-primary);
`;

export const StyledSelect = styled.div`
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
  padding-top: 8px;
`;

export const StyledSelectTitle = styled.div`
  flex: 0 0 22px;
  margin-bottom: 8px;
  padding: 0 12px;
  height: 22px;
  font-size: 14px;
  line-height: 22px;
  cursor: default;
  color: var(--theme-basic-color-primary);
`;

export const StyledSelectContainer = styled.div`
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  overflow: hidden;
`;

export const StyledDivider = styled(Divider)`
  &.ant-divider {
    width: auto;
    min-width: 0;
    margin: 0 12px 8px;
  }
`;

export const StyledSelectTreeContainer = styled.div`
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

export const StyledLoading = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-basic-color-bg-default);
`;
