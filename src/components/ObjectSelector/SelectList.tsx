import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { ReactNode } from 'react';
import React, { memo, type UIEvent, useCallback, useEffect, useRef, useState } from 'react';

import { LoadingGif } from '@/components/LoadingOld/LoadingGif';

import { LOADING_ICON_SIZE_MINE, ZERO } from '../../configs/configs';
import { StyledLoading } from './ObjectSelector.styled';
import { StyledCheckbox, StyledItem, StyledItemGap, StyledSelectList, StyledVirtualList } from './SelectList.styled';
import { renderSelectItem } from './utils/selector';

interface SelectListConfigsType<T extends { id: number | string }> {
  itemHeight: number;
  onScroll?: () => void;
  renderItem?: (item: T) => ReactNode;
}

export interface SelectListProps<T extends { id: number | string }> {
  data: T[];
  loading?: boolean;
  configs: SelectListConfigsType<T>;
  renderEmpty: () => ReactNode;
  className?: string;
}

export const SelectList = memo(<T extends { id: number | string }>(props: SelectListProps<T>) => {
  const { data, loading, configs, className, renderEmpty } = props;
  const { onScroll, renderItem, itemHeight } = configs;
  const [height, setHeight] = useState(ZERO);
  const listRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<ResizeObserver | null>(null);

  const onChangeCheckHandler = useCallback((onChangeFun?: (checked: boolean) => void) => {
    return (event: CheckboxChangeEvent) => {
      const {
        target: { checked },
      } = event;
      onChangeFun?.(checked);
    };
  }, []);

  const renderItemHandler = useCallback(
    (item: any) => {
      const node = renderItem ? renderItem(item) : renderSelectItem(item);

      const { checked, onCheck, allowCheck } = item;
      if (allowCheck) {
        return (
          <StyledItemGap>
            <StyledItem $height={itemHeight}>
              <StyledCheckbox checked={checked} onChange={onChangeCheckHandler(onCheck)}>
                {node}
              </StyledCheckbox>
            </StyledItem>
          </StyledItemGap>
        );
      }

      return (
        <div style={{ paddingBottom: 4 }}>
          <StyledItem $height={itemHeight}>{node}</StyledItem>
        </div>
      );
    },
    [renderItem, itemHeight, onChangeCheckHandler],
  );

  const onScrollHandler = useCallback(
    (e: UIEvent) => {
      if (onScroll && Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - height) <= 1) {
        onScroll();
      }
    },
    [height, onScroll],
  );

  useEffect(() => {
    observerRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        console.log('entry.contentRect.height: ', entry.contentRect.height);
        setHeight(entry.contentRect.height);
      }
    });

    if (listRef.current) {
      observerRef.current?.observe?.(listRef.current);
    }
    return () => {
      observerRef.current?.disconnect?.();
    };
  }, [loading]);

  if (loading) {
    return (
      <StyledLoading>
        <LoadingGif size={LOADING_ICON_SIZE_MINE} />
      </StyledLoading>
    );
  }

  if (data.length === ZERO && !loading) {
    return renderEmpty();
  }

  return (
    <StyledSelectList ref={listRef} className={className}>
      <StyledVirtualList
        data={data}
        height={height}
        itemHeight={itemHeight + 4}
        itemKey="id"
        onScroll={onScrollHandler}
      >
        {renderItemHandler}
      </StyledVirtualList>
    </StyledSelectList>
  );
});
