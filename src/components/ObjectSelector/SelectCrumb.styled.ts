import styled from 'styled-components';

import { ReactComponent as RightArrowSvg } from '@/assets/icon/right-arrow.svg';
import { ReactComponent as ResetSvg } from '@/assets/main/reset.svg';
import { isRtl as isrtl } from '@/themes/RtlAdapterProvider';

export const StyledSelectCrumb = styled.div`
  display: flex;
  height: 20px;
  font-size: 13px;
  margin: 0 12px 8px;
  flex: 0 0 20px;
`;

export const StyledCrumbContainer = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
  height: 100%;
  overflow: hidden;
`;

export const StyledCrumbContent = styled.div<{ $isScroll: boolean }>`
  cursor: default;
  white-space: nowrap;
  margin-right: 4px;
  height: 20px;
  color: var(--theme-basic-color-primary);

  &:last-child {
    margin-right: unset;
    font-weight: 500;
    color: var(--theme-basic-color-primary);
    /* stylelint-disable-next-line function-no-unknown */
    max-width: ${({ $isScroll }) => ($isScroll ? '80px' : 'unset')};

    & svg {
      display: none;
      flex: 0;
    }
  }
`;

export const StyledCrumbReset = styled.div`
  flex: 0 0 32px;
  display: flex;
  align-items: center;
  width: 32px;
  height: 100%;
`;
export const StyledResetIcon = styled(ResetSvg)`
  display: block;
  width: 20px;
  height: 20px;
  margin: 0 auto;
  background-color: transparent;
  color: var(--theme-basic-color-primary);

  &:hover {
    background-color: var(--theme-basic-color-bg-gray);
  }

  &:active {
    background-color: var(--theme-basic-color-bg-gray);
  }

  &:hover,
  &:active {
    cursor: pointer;
  }
`;

export const StyledCrumbItem = styled.div`
  display: flex;
  height: 22px;
  align-items: center;
`;

export const StyledCrumbItemLabel = styled.div`
  flex: 1;
  height: 22px;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &.action:hover {
    cursor: pointer;
    color: var(--theme-basic-color-primary);
  }
`;

export const StyledCrumbIcon = styled.div`
  box-sizing: border-box;
  height: 22px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;

export const StyledArrowIcon = styled(RightArrowSvg)`
  flex: 0 0 14px;
  width: 14px;
  height: 14px;
  margin-left: 4px;
  color: var(--theme-basic-color-primary);
  /* stylelint-disable-next-line function-no-unknown */
  transform: ${() => (isrtl() ? 'rotate(180deg)' : 'unset')};
`;
