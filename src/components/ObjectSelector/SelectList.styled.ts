import { Checkbox, List } from 'antd';
import VirtualList from 'rc-virtual-list';
import styled from 'styled-components';

import { ReactComponent as RightArrowSvg } from '@/assets/icon/right-arrow.svg';

import { isRtl as isrtl } from '../../themes/RtlAdapterProvider';

export const StyledSelectList = styled(List)`
  min-height: 0;
  background: var(--color-bg-layout-white);
`;

export const StyledVirtualList = styled(VirtualList)`
  & .rc-virtual-list-scrollbar {
    right: 0;
    left: unset !important;
  }
`;

export const StyledItemGap = styled.div`
  padding-bottom: 4px;
`;

export const StyledItem = styled.div<{ $height: number }>`
  display: block;
  box-sizing: border-box;
  padding: 4px 12px;
  /* stylelint-disable-next-line function-no-unknown */
  height: ${({ $height }) => `${$height}px`};

  &:active,
  &:hover {
    background-color: var(--theme-basic-color-bg-gray);
  }
`;

export const StyledCheckbox = styled(Checkbox)`
  &.ant-checkbox-wrapper {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow: hidden;

    & > span {
      display: block;
      overflow: hidden;
      padding: 0;

      &:first-child {
        flex: 0 0 16px;
        margin-right: 8px;
      }

      &:last-child {
        flex: 1;
        min-width: 0;
      }
    }
  }
`;

export const StyledItemContainer = styled.div`
  display: flex;
  height: 100%;
  align-items: center;
  color: var(--theme-basic-color-primary);

  &.link {
    color: var(--theme-basic-color-primary);

    &:hover,
    &:active {
      cursor: pointer;
    }
  }
`;

export const StyledItemContainerLabel = styled.div`
  font-size: 13px;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 10px;
`;

export const StyledArrowIcon = styled(RightArrowSvg)`
  width: 16px;
  height: 16px;
  flex: 0 0 16px;
  margin-left: 8px;
  /* stylelint-disable-next-line function-no-unknown */
  transform: ${() => (isrtl() ? 'rotate(180deg)' : 'unset')};
`;
