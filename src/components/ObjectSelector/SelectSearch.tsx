import type { FormInstance } from 'antd';
import { Form, Input } from 'antd';
import debounce from 'lodash/debounce';
import React, { memo, useCallback } from 'react';
import styled from 'styled-components';

import { DEBOUNCE_INPUT_WAIT } from '../../configs/configs';

export const StyledSearchInput = styled(Input.Search)`
  margin-bottom: 8px;
  padding: 0 12px;
  flex: 0 0 32px;
  min-height: 0;

  &.ant-input-search > .ant-input-group {
    & .ant-input-affix-wrapper:not(:last-child) {
      border-radius: 2px 0 0 2px;
    }

    & > .ant-input-group-addon:last-child .ant-input-search-button {
      border-radius: 0 4px 4px 0;
    }
  }
`;

export interface SelectSearchFormType {
  searchValue: string;
}

export interface SelectSearchProps {
  form: FormInstance<SelectSearchFormType>;
  disabled: boolean;
  placeholder?: string;
  onSearch: (value: string) => void;
}

export const SelectSearch = memo((props: SelectSearchProps) => {
  const { form, disabled, onSearch, placeholder } = props;

  const onChangeHandler = useCallback(
    (_: SelectSearchFormType, { searchValue }: SelectSearchFormType) => {
      onSearch(searchValue);
    },
    [onSearch],
  );

  return (
    <Form disabled={disabled} form={form} onValuesChange={debounce(onChangeHandler, DEBOUNCE_INPUT_WAIT)}>
      <Form.Item noStyle name="searchValue">
        <StyledSearchInput allowClear placeholder={placeholder} />
      </Form.Item>
    </Form>
  );
});
