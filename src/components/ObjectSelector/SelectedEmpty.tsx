import type { ReactNode } from 'react';
import React, { memo } from 'react';
import styled from 'styled-components';

import { fm } from '@/modules/Locale';

import EmptyPng from '../../assets/components/empty/<EMAIL>';
import { Image } from '../Image';

export interface SelectedEmptyProps {
  title?: string | any[] | ReactNode;
  subtitle?: string | any[] | ReactNode;
}

export const StyledSelectedEmpty = styled.div`
  flex: 1;
  background: var(--color-bg-layout-white);
`;

export const StyledImage = styled(Image)`
  display: block;
  margin: 80px auto 0;
  width: 160px;
  height: 130px;
`;

export const StyledText = styled.div`
  text-align: center;
  width: 166px;
  min-height: 20px;
  margin: 0 auto;
  font-size: 13px;
  line-height: 20px;
  word-break: break-word;
  color: var(--theme-basic-color-primary);
`;

export const SelectedEmpty = memo((props: SelectedEmptyProps) => {
  const { title, subtitle } = props;

  const i18n_noData = fm('CapacityManage.noData');

  return (
    <StyledSelectedEmpty>
      <StyledImage src={EmptyPng} />
      <StyledText>{title ?? i18n_noData}</StyledText>
      {subtitle && <StyledText>{subtitle}</StyledText>}
    </StyledSelectedEmpty>
  );
});
