import type { ReactNode } from 'react';
import React, { memo } from 'react';

import { StyledLinkButton, StyledSelectedHeader, StyledSelectedTitle } from './SelectedHeader.styled';

export interface SelectedHeaderProps {
  title: string | any[] | ReactNode;
  action?: {
    text: string;
    disabled?: boolean;
    onPress: () => void;
  };
}

export const SelectedHeader = memo((props: SelectedHeaderProps) => {
  const { title, action } = props;
  return (
    <StyledSelectedHeader>
      <StyledSelectedTitle>{title}</StyledSelectedTitle>
      {action && (
        <StyledLinkButton disabled={action?.disabled} onClick={action?.onPress}>
          {action.text}
        </StyledLinkButton>
      )}
    </StyledSelectedHeader>
  );
});
