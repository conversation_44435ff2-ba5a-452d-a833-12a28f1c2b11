import React, { memo } from 'react';
import styled from 'styled-components';

import { fm } from '@/modules/Locale';

import EmptyPng from '../../assets/components/empty/<EMAIL>';
import { Image } from '../Image';

export const StyledSelectEmpty = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-bg-layout-white);
`;

export const StyledImage = styled(Image)`
  display: block;
  margin: 80px auto 0;
  width: 160px;
  height: 130px;
`;

export const StyledText = styled.div`
  text-align: center;
  line-height: 20px;
  min-height: 20px;
  font-size: 13px;
  color: var(--theme-basic-color-primary);
`;

export const SelectEmpty = memo(() => {
  const i18n_noData = fm('CapacityManage.noData');

  return (
    <StyledSelectEmpty>
      <StyledImage src={EmptyPng} />
      <StyledText>{i18n_noData}</StyledText>
    </StyledSelectEmpty>
  );
});
