import React, { memo } from 'react';

import { StyledObjectSelector } from './ObjectSelector.styled';
import type { SelectProps } from './Select';
import { Select } from './Select';
import type { SelectedProps } from './Selected';
import { Selected } from './Selected';

const DEFAULT_SELECTOR_HEIGHT = 460;

export interface ObjectSelectorProps<T extends { id: number | string }> {
  height?: number;
  select: SelectProps<T>;
  selected: SelectedProps<T>;
}

export const ObjectSelector = memo(
  <T extends { id: number | string; [key: string]: any }>(props: ObjectSelectorProps<T>) => {
    const { select, selected, height = DEFAULT_SELECTOR_HEIGHT } = props;
    return (
      <StyledObjectSelector $height={height}>
        <Select {...select} />
        <Selected {...selected} />
      </StyledObjectSelector>
    );
  },
);
