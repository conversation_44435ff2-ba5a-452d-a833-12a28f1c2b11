import styled from 'styled-components';

import { ReactComponent as CancelIcon } from '@/assets/icon/cancel-icon.svg';

export const StyledSelected = styled.div`
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  width: 50%;
  height: 100%;
  border-left: 1px solid var(--color-border-separator-lighter);
`;

export const StyledListContainer = styled.div`
  flex: 1;
  padding: 4px 0;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
`;

export const StyledItem = styled.div`
  width: 100%;
  cursor: default;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover,
  &:active {
    background-color: var(--theme-basic-color-bg-gray);
  }
`;

export const StyledSelectedItemContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 4px 12px;
`;

export const StyledSelectedItemInfo = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
`;

export const StyledSelectedItemInfoContent = styled.div`
  flex: 1;
  overflow: hidden;
`;

export const StyledSelectedItemTitle = styled.div`
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-basic-color-primary);
`;

export const StyledSelectedItemDescription = styled.div`
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-basic-color-primary);
`;

export const StyledClearIcon = styled(CancelIcon)`
  width: 16px;
  height: 16px;
  color: var(--theme-basic-color-primary);

  &:hover,
  &:active {
    cursor: pointer;
    color: var(--theme-basic-color-primary);
  }
`;
