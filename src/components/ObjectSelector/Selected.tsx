import type { ReactNode } from 'react';
import React, { memo } from 'react';

import { ZERO } from '../../configs/configs';
import { StyledItem, StyledListContainer, StyledSelected } from './Selected.styled';
import type { SelectedEmptyProps } from './SelectedEmpty';
import { SelectedEmpty } from './SelectedEmpty';
import type { SelectedHeaderProps } from './SelectedHeader';
import { SelectedHeader } from './SelectedHeader';
import { renderSelectedItem } from './utils/selector';

export interface SelectedProps<T extends { id: number | string }> {
  list: T[];
  header?: SelectedHeaderProps;
  itemRender?: (item: T) => ReactNode;
  empty?: SelectedEmptyProps;
}

export const Selected = memo((props: SelectedProps<any>) => {
  const { list, header, empty, itemRender } = props;

  return (
    <StyledSelected>
      {header && <SelectedHeader {...header} />}
      {list.length === ZERO ? (
        <SelectedEmpty {...empty} />
      ) : (
        <StyledListContainer>
          {list.map((item, index) => (
            <StyledItem key={index}>{itemRender ? itemRender(item) : renderSelectedItem(item)}</StyledItem>
          ))}
        </StyledListContainer>
      )}
    </StyledSelected>
  );
});
