import { useCallback, useMemo, useState } from 'react';

import { ZERO } from '../../../configs/configs';
import { convertSelectToSelected, updateSelectedHandler } from '../utils/seleted';
import type { SelectedItemBasicType, SelectedItemType, SelectItemBasicType } from './types';

interface ObjectSelectedType {
  list: SelectedItemType[];
  clearAction: {
    disabled: boolean;
    onPress: () => void;
  };
  checkSelected: (item: SelectItemBasicType) => boolean;
  updateSelected: (params: { selected?: SelectedItemBasicType; selectedList?: SelectedItemBasicType[] }) => void;
  updateChecked: (params: { checked: boolean; item: SelectItemBasicType }) => void;
  onClearSelected: (params: { id?: number | string; ids?: (number | string)[] }) => void;
}

export function useObjectSelected(): ObjectSelectedType {
  const [list, setList] = useState<SelectedItemBasicType[]>([]);

  const onClearAll = useCallback(() => {
    setList([]);
  }, []);

  const onClearSelected = useCallback((params: { id?: number | string; ids?: (number | string)[] }) => {
    const { id, ids } = params;
    if (id) {
      setList((list) => list.filter((item) => item.id !== id));
    }
    if (ids) {
      setList((list) => list.filter((item) => !ids.includes(item.id)));
    }
  }, []);

  const checkSelected = useCallback(
    (item: SelectItemBasicType) => {
      return list.some((selectedItem) => selectedItem.id === item.id);
    },
    [list],
  );

  const updateSelected = useCallback(
    (params: { selected?: SelectedItemBasicType; selectedList?: SelectedItemBasicType[] }) => {
      const { selected, selectedList } = params;
      setList((list) => {
        const basicList = [...list];
        if (selected) {
          basicList.push(selected);
        }
        if (selectedList) {
          basicList.push(...selectedList);
        }
        return basicList.reduce(updateSelectedHandler, [] as SelectedItemBasicType[]);
      });
    },
    [],
  );

  const updateChecked = useCallback(
    (params: { checked: boolean; item: SelectItemBasicType }) => {
      const { checked, item } = params;
      if (checked) {
        updateSelected({ selected: convertSelectToSelected(item) });
        return;
      }
      onClearSelected({ id: item.id });
    },
    [updateSelected, onClearSelected],
  );

  return useMemo(
    () => ({
      list: list.map((item) => ({
        ...item,
        onPress: () => {
          onClearSelected({ id: item.id });
        },
      })),
      checkSelected,
      onClearSelected,
      updateChecked,
      updateSelected,
      clearAction: {
        disabled: list.length <= ZERO,
        onPress: onClearAll,
      },
    }),
    [list, onClearAll, onClearSelected, updateChecked, checkSelected, updateSelected],
  );
}
