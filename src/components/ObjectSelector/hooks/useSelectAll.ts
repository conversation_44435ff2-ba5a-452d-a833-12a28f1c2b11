import { useCallback, useMemo } from 'react';

import { ZERO } from '../../../configs/configs';
import { formatterAllowCheck } from '../utils/convert';
import { convertSelectToSelected } from '../utils/seleted';
import type { SelectedItemBasicType, SelectItemBasicType } from './types';

interface SelectAllType {
  isCheckedAll: () => boolean;
  isIndeterminate: () => boolean;
  onPressSelectedAll: (checked: boolean) => void;
}

interface SelectAllProps {
  isSelectableCompany: boolean;
  list: SelectItemBasicType[];
  checkSelected: (item: SelectItemBasicType) => boolean;
  updateSelected: (params: { selected?: SelectedItemBasicType; selectedList?: SelectedItemBasicType[] }) => void;
  onClearSelected: (params: { id?: number | string; ids?: (number | string)[] }) => void;
}

export function useSelectAll(props: SelectAllProps): SelectAllType {
  const { list, checkSelected, updateSelected, onClearSelected, isSelectableCompany } = props;

  const getAllowCheckList = useCallback(() => {
    return formatterAllowCheck({ list, isSelectableCompany }).filter((item) => item.allowCheck);
  }, [list, isSelectableCompany]);

  const isCheckedAll = useCallback((): boolean => {
    const allowCheckList = getAllowCheckList();
    return allowCheckList.length > ZERO && allowCheckList.every(checkSelected);
  }, [getAllowCheckList, checkSelected]);

  const isIndeterminate = useCallback((): boolean => {
    const allowCheckList = getAllowCheckList();
    if (allowCheckList.length === 0) {
      return false;
    }

    const selectedCount = allowCheckList.filter(checkSelected).length;
    return selectedCount > 0 && selectedCount < allowCheckList.length;
  }, [getAllowCheckList, checkSelected]);

  const onPressSelectedAll = useCallback(
    (checked: boolean) => {
      const allowCheckList = getAllowCheckList();
      const selectedIds = allowCheckList.map((item) => item.id);

      if (checked) {
        const selectedList = allowCheckList.map(convertSelectToSelected);
        updateSelected({ selectedList });
      } else {
        onClearSelected({ ids: selectedIds });
      }
    },
    [getAllowCheckList, updateSelected, onClearSelected],
  );

  return useMemo(
    () => ({ isCheckedAll, isIndeterminate, onPressSelectedAll }),
    [isCheckedAll, isIndeterminate, onPressSelectedAll],
  );
}
