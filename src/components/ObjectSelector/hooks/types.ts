import type { IconTypes } from '../enums/enums';

export interface ObjectSelectorSearchConfigTypes {
  department?: object;
  user: {
    includeDisabledMember?: boolean;
  };
}

export interface ObjectSelectorParams {
  type?: 'org' | 'space';
  pageSize?: number;
  isAdmin?: boolean; // 是否不受组织架构可见性控制
  isSelectableCompany?: boolean; // 是否可以选择企业，默认为 true
  searchConfigs?: ObjectSelectorSearchConfigTypes;
}

export interface GetOrgListParams {
  current: number;
  departmentId: number;
}

export interface GetSpaceListParams {
  current: number;
}

export type GetListParams = GetOrgListParams | GetSpaceListParams;

export interface SelectItemBasicType {
  key: string;
  id: number | string;
  label: string;
  avatar?: string;
  email?: string;
  iconType?: IconTypes;
  creator?: {
    id: number;
    name: string;
    email?: string;
    avatar?: string;
  };
  [key: string]: unknown;
}

export interface SelectItemType extends SelectItemBasicType {
  checked?: boolean;
  allowCheck: boolean;
  onCheck?: (checked: boolean) => void;
  onPress?: () => void;
}

export interface CrumbType {
  id: number;
  label: string;
  onPress?: (id: number) => void;
}

export interface SelectedItemBasicType {
  key: string;
  id: number | string;
  label: string;
  avatar?: string;
  iconType?: IconTypes;
  description?: string;
  creator?: {
    id: number;
    name: string;
    email?: string;
    avatar?: string;
  };
  [key: string]: unknown;
}

export interface SelectedItemType extends SelectedItemBasicType {
  onPress?: () => void;
}
