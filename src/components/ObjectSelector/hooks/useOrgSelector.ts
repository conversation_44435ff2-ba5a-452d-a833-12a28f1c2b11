import type { Dispatch, MutableRefObject, SetStateAction } from 'react';
import { useCallback, useMemo } from 'react';

import { getOrgTree, getOrgUsers } from '../services/service';
import { convertBasicSelectList } from '../utils/convert';
import { scrollHandler } from '../utils/selector';
import type { GetOrgListParams, SelectItemBasicType } from './types';

interface OrgSelectorType {
  scrollUserHandler: (params: GetOrgListParams) => void;
  getOrgTreeHandler: (params: GetOrgListParams) => void;
}

interface OrgSelectorProps {
  pageSize: number;
  isAdmin: boolean;
  setList: Dispatch<SetStateAction<SelectItemBasicType[]>>;
  isEndRef: MutableRefObject<boolean>;
  isPendingRef: MutableRefObject<boolean>;
  pageCurrentRef: MutableRefObject<number>;
  setLoading: Dispatch<SetStateAction<boolean>>;
  updateCrumbs: (params: { id: number; name: string }) => void;
  onChangeCurrentCrumb: (params: { id: number; name: string }) => void;
}

export function useOrgSelector(props: OrgSelectorProps): OrgSelectorType {
  const {
    setList,
    isAdmin,
    isEndRef,
    pageSize,
    setLoading,
    isPendingRef,
    updateCrumbs,
    pageCurrentRef,
    onChangeCurrentCrumb,
  } = props;

  const getOrgTreeHandler = useCallback(
    (params: GetOrgListParams) => {
      const { current, departmentId } = params;
      getOrgTree({
        isAdmin,
        departmentId,
        page: current,
        perPage: pageSize,
      })
        .then(({ list, currentDepartment }) => {
          setList(convertBasicSelectList(list));
          onChangeCurrentCrumb(currentDepartment);
          updateCrumbs(currentDepartment);
          isEndRef.current = false;
        })
        .catch(console.error)
        .finally(() => {
          setLoading(false);
          isPendingRef.current = false;
        });
    },
    [isAdmin, pageSize, isEndRef, isPendingRef, setList, setLoading, updateCrumbs, onChangeCurrentCrumb],
  );

  const scrollUserHandler = useCallback(
    (params: GetOrgListParams) => {
      const { departmentId, current } = params;
      if (isPendingRef.current || isEndRef.current) {
        return;
      }
      pageCurrentRef.current = params.current;
      getOrgUsers({ isAdmin, page: current, perPage: pageSize, departmentId })
        .then(({ total, list }) => {
          scrollHandler({
            total,
            list,
            setList,
            current,
            isEndRef,
            pageSize,
            pageCurrentRef,
          });
        })
        .catch(console.error)
        .finally(() => {
          isPendingRef.current = false;
        });
    },
    [isAdmin, pageSize, isEndRef, isPendingRef, setList, pageCurrentRef],
  );

  return useMemo(
    () => ({
      getOrgTreeHandler,
      scrollUserHandler,
    }),
    [getOrgTreeHandler, scrollUserHandler],
  );
}
