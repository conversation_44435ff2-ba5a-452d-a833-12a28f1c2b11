import type { FormInstance } from 'antd';
import { Form } from 'antd';
import debounce from 'lodash/debounce';
import { useCallback, useMemo, useRef, useState } from 'react';

import { fm } from '@/modules/Locale';

import { DEBOUNCE_INPUT_WAIT, DEFAULT_PAGE_CURRENT, DEFAULT_PAGE_SIZE } from '../../../configs/configs';
import { getSpace, searchOrg } from '../services/service';
import { convertBasicSelectList } from '../utils/convert';
import type { ObjectSelectorSearchConfigTypes, SelectItemBasicType } from './types';

interface SelectorSearchFormType {
  searchValue: string;
}

interface ObjectSelectorSearchType {
  form: FormInstance<SelectorSearchFormType>;
  placeholder: string;
  searchLoading: boolean;
  searchList?: SelectItemBasicType[];
  onSearch: (value: string) => void;
  resetSearchList: () => void;
}

interface ObjectSelectorSearchProps {
  type: 'org' | 'space';
  isAdmin: boolean;
  searchConfigs?: ObjectSelectorSearchConfigTypes;
}

export function useObjectSelectorSearch(props: ObjectSelectorSearchProps): ObjectSelectorSearchType {
  const [searchList, setSearchList] = useState<SelectItemBasicType[]>();
  const [searchLoading, setSearchLoading] = useState(false);
  const [form] = Form.useForm<SelectorSearchFormType>();

  const i18n_search = fm('CapacityManage.search');

  const { type, ...searchConfigs } = props;

  const searchValueRef = useRef<string | undefined>();

  const searchOrgHandler = useCallback(
    (keyword: string) => {
      searchOrg({
        keyword,
        ...searchConfigs,
      })
        .then((list) => {
          if (searchValueRef.current === keyword) {
            setSearchList(convertBasicSelectList(list));
          }
        })
        .catch(console.error)
        .finally(() => {
          setSearchLoading(false);
        });
    },
    [searchConfigs],
  );

  const searchSpaceHandler = useCallback((keyword: string) => {
    getSpace({
      pageSize: DEFAULT_PAGE_SIZE,
      pageNum: DEFAULT_PAGE_CURRENT,
      spaceName: keyword,
    })
      .then(({ list }) => {
        if (searchValueRef.current === keyword) {
          setSearchList(convertBasicSelectList(list));
        }
      })
      .catch(console.error)
      .finally(() => {
        setSearchLoading(false);
      });
  }, []);

  const onSearch = useCallback(
    (value: string) => {
      searchValueRef.current = value;
      if (!value) {
        setSearchList(undefined);
        return;
      }
      setSearchLoading(true);
      switch (type) {
        case 'org':
          searchOrgHandler(value);
          break;
        case 'space':
          searchSpaceHandler(value);
          break;
      }
    },
    [searchOrgHandler, searchSpaceHandler],
  );

  const resetSearchList = useCallback(() => {
    setSearchList(undefined);
  }, []);

  return useMemo(
    () => ({
      form,
      searchList,
      searchLoading,
      resetSearchList,
      placeholder: i18n_search,
      onSearch: debounce(onSearch, DEBOUNCE_INPUT_WAIT),
    }),
    [form, searchList, searchLoading, resetSearchList, i18n_search, onSearch],
  );
}
