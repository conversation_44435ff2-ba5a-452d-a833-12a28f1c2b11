import type { Dispatch, MutableRefObject, SetStateAction } from 'react';
import { useCallback, useMemo } from 'react';

import { ZERO } from '@/configs/configs';
import { fm } from '@/modules/Locale';

import { getSpace } from '../services/service';
import { convertBasicSelectList } from '../utils/convert';
import { scrollHandler } from '../utils/selector';
import type { GetSpaceListParams, SelectItemBasicType } from './types';

interface SpaceSelectorType {
  scrollSpaceHandler: (params: GetSpaceListParams) => void;
  getSpaceListHandler: (params: GetSpaceListParams) => void;
}

interface SpaceSelectorProps {
  pageSize: number;
  setList: Dispatch<SetStateAction<SelectItemBasicType[]>>;
  isEndRef: MutableRefObject<boolean>;
  isPendingRef: MutableRefObject<boolean>;
  pageCurrentRef: MutableRefObject<number>;
  setLoading: Dispatch<SetStateAction<boolean>>;
  updateCrumbs: (params: { id: number; name: string }) => void;
  onChangeCurrentCrumb: (params: { id: number; name: string }) => void;
}

export function useSpaceSelector(props: SpaceSelectorProps): SpaceSelectorType {
  const { setList, isEndRef, pageSize, setLoading, isPendingRef, updateCrumbs, pageCurrentRef, onChangeCurrentCrumb } =
    props;

  const i18n_allSpace = fm('CapacityManage.allSpace');

  const getDefaultCrumb = () => {
    return {
      id: ZERO,
      name: i18n_allSpace,
    };
  };

  const getSpaceListHandler = useCallback(
    (params: GetSpaceListParams) => {
      const { current } = params;
      getSpace({
        pageNum: current,
        pageSize,
      })
        .then(({ list }) => {
          setList(convertBasicSelectList(list));
          onChangeCurrentCrumb(getDefaultCrumb());
          updateCrumbs(getDefaultCrumb());
          isEndRef.current = false;
        })
        .catch(console.error)
        .finally(() => {
          setLoading(false);
          isPendingRef.current = false;
        });
    },
    [pageSize, isEndRef, isPendingRef, setList, setLoading, updateCrumbs, onChangeCurrentCrumb],
  );

  const scrollSpaceHandler = useCallback(
    (params: GetSpaceListParams) => {
      const { current } = params;
      if (isPendingRef.current || isEndRef.current) {
        return;
      }
      pageCurrentRef.current = params.current;
      getSpace({ pageNum: current, pageSize: pageSize })
        .then(({ total, list }) => {
          scrollHandler({
            total,
            list,
            setList,
            current,
            isEndRef,
            pageSize,
            pageCurrentRef,
          });
        })
        .catch(console.error)
        .finally(() => {
          isPendingRef.current = false;
        });
    },
    [pageSize, isEndRef, isPendingRef, setList, pageCurrentRef],
  );

  return useMemo(
    () => ({
      getSpaceListHandler,
      scrollSpaceHandler,
    }),
    [getSpaceListHandler, scrollSpaceHandler],
  );
}
