import type { FormInstance } from 'antd';
import { useCallback, useMemo, useRef, useState } from 'react';

import { DEFAULT_PAGE_CURRENT, DEFAULT_PAGE_SIZE, ROOT_DEPARTMENT, ZERO } from '../../../configs/configs';
import type { SelectSearchFormType } from '../SelectSearch';
import { convertSelectList } from '../utils/convert';
import type {
  CrumbType,
  GetListParams,
  ObjectSelectorParams,
  SelectedItemBasicType,
  SelectedItemType,
  SelectItemBasicType,
  SelectItemType,
} from './types';
import { useObjectSelected } from './useObjectSelected';
import { useObjectSelectorCrumbs } from './useObjectSelectorCrumbs';
import { useObjectSelectorSearch } from './useObjectSelectorSearch';
import { useOrgSelector } from './useOrgSelector';
import { useSelectAll } from './useSelectAll';
import { useSpaceSelector } from './useSpaceSelector';

interface ObjectSelectorType {
  resetSelector: () => void;
  select: {
    crumbs: CrumbType[];
    loading: boolean;
    searchLoading: boolean;
    onScroll: () => void;
    list: SelectItemType[];
    searchList?: SelectItemType[];
    initSelect: (defaultSelected?: SelectedItemType[]) => void;
    search: {
      placeholder: string;
      disabled: boolean;
      onSearch: (value: string) => void;
      form: FormInstance<SelectSearchFormType>;
    };
    selectAll: {
      checked: boolean;
      disabled?: boolean;
      indeterminate: boolean;
      onPress: (checked: boolean) => void;
    };
  };
  selected: {
    list: SelectedItemType[];
    clearAction: {
      disabled: boolean;
      onPress: () => void;
    };
  };
}

export function useObjectSelector(params?: ObjectSelectorParams): ObjectSelectorType {
  const {
    type = 'org',
    isSelectableCompany = true,
    pageSize = DEFAULT_PAGE_SIZE,
    searchConfigs,
    isAdmin = false,
  } = params ?? {};

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<SelectItemBasicType[]>([]);
  const pageCurrentRef = useRef<number>(DEFAULT_PAGE_CURRENT);
  const isEndRef = useRef(false);
  const isPendingRef = useRef(false);

  const { searchList, resetSearchList, searchLoading, ...search } = useObjectSelectorSearch({
    type,
    isAdmin,
    searchConfigs,
  });
  const { clearAction, updateChecked, checkSelected, updateSelected, onClearSelected, ...selected } =
    useObjectSelected();

  const { crumbs, currentCrumb, resetCrumbs, onChangeCurrentCrumb, updateCrumbs } = useObjectSelectorCrumbs();

  const { isCheckedAll, isIndeterminate, onPressSelectedAll } = useSelectAll({
    list,
    checkSelected,
    updateSelected,
    onClearSelected,
    isSelectableCompany,
  });

  const { getOrgTreeHandler, scrollUserHandler } = useOrgSelector({
    setList,
    isAdmin,
    pageSize,
    setLoading,
    updateCrumbs,
    isEndRef,
    pageCurrentRef,
    isPendingRef,
    onChangeCurrentCrumb,
  });
  const { getSpaceListHandler, scrollSpaceHandler } = useSpaceSelector({
    setList,
    pageSize,
    setLoading,
    updateCrumbs,
    isEndRef,
    pageCurrentRef,
    isPendingRef,
    onChangeCurrentCrumb,
  });

  const getTreeHandler = useCallback(
    (params: GetListParams) => {
      if (isPendingRef.current) {
        return;
      }
      setLoading(true);
      isPendingRef.current = true;
      const { current } = params;
      pageCurrentRef.current = current;
      switch (type) {
        case 'org':
          if ('departmentId' in params) {
            getOrgTreeHandler(params);
          }
          break;
        case 'space':
          getSpaceListHandler(params);
          break;
      }
    },
    [type, isPendingRef, getOrgTreeHandler, getSpaceListHandler],
  );

  const initSelect = useCallback(
    (defaultSelected?: SelectedItemBasicType[]) => {
      getTreeHandler({
        departmentId: ROOT_DEPARTMENT,
        current: DEFAULT_PAGE_CURRENT,
      });
      updateSelected({ selectedList: defaultSelected });
    },
    [getTreeHandler, updateSelected],
  );

  const onPressCrumbs = useCallback(
    (id: number) => {
      const crumb = crumbs.find((item) => item.id === id);
      if (crumb) {
        const { id, label } = crumb;
        updateCrumbs({ id, name: label });
        getTreeHandler({ current: DEFAULT_PAGE_CURRENT, departmentId: id });
      }
    },
    [crumbs, updateCrumbs, getTreeHandler],
  );

  const onPressItem = useCallback(
    (departmentId: number) => {
      getTreeHandler({
        current: DEFAULT_PAGE_CURRENT,
        departmentId: departmentId,
      });
    },
    [getTreeHandler],
  );

  const onScroll = useCallback(() => {
    if (isEndRef.current || list.length === ZERO || isPendingRef.current || !currentCrumb) {
      return;
    }
    const current = pageCurrentRef.current + 1;
    switch (type) {
      case 'org':
        scrollUserHandler({
          current: pageCurrentRef.current + 1,
          departmentId: currentCrumb.id,
        });
        break;
      case 'space':
        scrollSpaceHandler({ current });
        break;
    }
  }, [type, currentCrumb, scrollUserHandler, scrollSpaceHandler]);

  const resetSelector = useCallback(() => {
    setList([]);
    resetSearchList();
    resetCrumbs();
    search.form.resetFields();
    clearAction.onPress();
  }, [search.form, resetSearchList, resetCrumbs, clearAction.onPress]);

  return useMemo(() => {
    const selectList =
      convertSelectList({
        list,
        onPressItem,
        isSelectableCompany,
        checkSelected,
        updateChecked,
      }) ?? [];
    return {
      resetSelector,
      select: {
        list: selectList,
        searchList: convertSelectList({
          list: searchList,
          isSelectableCompany,
          checkSelected,
          updateChecked,
        }),
        search: { disabled: loading, ...search },
        loading,
        onScroll,
        initSelect,
        searchLoading,
        crumbs: crumbs.map((crumb) => ({
          ...crumb,
          onPress: crumb.id === currentCrumb?.id ? undefined : onPressCrumbs,
        })),
        selectAll: {
          disabled: selectList.length > 0 ? !selectList.some((item) => item.allowCheck) : true,
          checked: isCheckedAll(),
          indeterminate: isIndeterminate(),
          onPress: onPressSelectedAll,
        },
      },
      selected: {
        clearAction,
        list: selected.list,
      },
    };
  }, [
    list,
    crumbs,
    search,
    loading,
    onScroll,
    searchList,
    initSelect,
    clearAction,
    onPressItem,
    currentCrumb,
    searchLoading,
    selected.list,
    updateChecked,
    checkSelected,
    isCheckedAll,
    isIndeterminate,
    onPressSelectedAll,
    isSelectableCompany,
  ]);
}
