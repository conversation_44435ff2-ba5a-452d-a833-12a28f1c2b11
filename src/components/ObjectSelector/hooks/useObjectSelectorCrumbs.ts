import { useCallback, useMemo, useState } from 'react';

import { NEGATIVE_ONE, ONE, ZERO } from '../../../configs/configs';
import type { CrumbType } from './types';

interface ObjectSelectorCrumbsType {
  crumbs: CrumbType[];
  currentCrumb?: CrumbType;
  updateCrumbs: (params: { id: number; name: string }) => void;
  resetCrumbs: () => void;
  onChangeCurrentCrumb: (params: { id: number; name: string }) => void;
}

export function useObjectSelectorCrumbs(): ObjectSelectorCrumbsType {
  const [currentCrumb, setCurrentCrumb] = useState<CrumbType>();
  const [crumbs, setCrumbs] = useState<CrumbType[]>([]);

  const onChangeCurrentCrumb = useCallback((params: { id: number; name: string }) => {
    const { id, name } = params;
    setCurrentCrumb({ id, label: name });
  }, []);

  const updateCrumbs = useCallback((params: { id: number; name: string }) => {
    const { id, name } = params;
    setCrumbs((old) => {
      const index = old.findIndex((item) => item.id === id);
      if (index !== NEGATIVE_ONE) {
        return old.slice(ZERO, index + ONE);
      }
      return [...old, { id, label: name }];
    });
  }, []);

  const resetCrumbs = useCallback(() => {
    setCrumbs([]);
  }, []);

  return useMemo(
    () => ({
      crumbs,
      currentCrumb,
      updateCrumbs,
      resetCrumbs,
      onChangeCurrentCrumb,
    }),
    [crumbs, currentCrumb, updateCrumbs, resetCrumbs, onChangeCurrentCrumb],
  );
}
