import type { ReactNode } from 'react';
import React, { memo, useCallback } from 'react';

import { ZERO } from '../../configs/configs';
import {
  StyledDivider,
  StyledSelect,
  StyledSelectContainer,
  StyledSelectTitle,
  StyledSelectTreeContainer,
} from './ObjectSelector.styled';
import type { SelectAllProps } from './SelectAll';
import { SelectAll } from './SelectAll';
import type { SelectCrumbProps } from './SelectCrumb';
import { SelectCrumb } from './SelectCrumb';
import { SelectEmpty } from './SelectEmpty';
import type { SelectListProps } from './SelectList';
import { SelectList } from './SelectList';
import type { SelectSearchProps } from './SelectSearch';
import { SelectSearch } from './SelectSearch';

export interface SelectProps<T extends { id: number | string }> {
  list: T[];
  title?: string;
  searchList?: T[];
  loading?: boolean;
  searchLoading?: boolean;
  crumbs?: SelectCrumbProps['crumbs'];
  search?: SelectSearchProps;
  selectAll?: SelectAllProps;
  listConfigs: SelectListProps<T>['configs'];
  renderEmpty?: () => ReactNode;
}

export const Select = memo((props: SelectProps<any>) => {
  const { list, title, crumbs, search, loading, selectAll, searchList, listConfigs, renderEmpty, searchLoading } =
    props;

  const renderEmptyFun = useCallback(() => {
    if (renderEmpty) {
      return renderEmpty();
    }
    return <SelectEmpty />;
  }, [renderEmpty]);

  return (
    <StyledSelect>
      {title && <StyledSelectTitle>{title}</StyledSelectTitle>}
      {search && <SelectSearch {...search} />}
      <StyledSelectContainer>
        {crumbs?.length === ZERO || searchLoading || searchList ? (
          <SelectList
            configs={listConfigs}
            data={searchList ?? []}
            loading={searchLoading || loading}
            renderEmpty={renderEmptyFun}
          />
        ) : (
          <>
            {crumbs && <SelectCrumb crumbs={crumbs} />}
            {(title || search || crumbs) && <StyledDivider />}
            <StyledSelectTreeContainer>
              {selectAll && <SelectAll {...selectAll} />}
              <SelectList configs={listConfigs} data={list} loading={loading} renderEmpty={renderEmptyFun} />
            </StyledSelectTreeContainer>
          </>
        )}
      </StyledSelectContainer>
    </StyledSelect>
  );
});
