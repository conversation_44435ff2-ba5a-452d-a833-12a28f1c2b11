import { Tooltip } from 'antd';
import classnames from 'classnames';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import { fm } from '@/modules/Locale';

import { ZERO } from '../../configs/configs';
import { isRtl } from '../../themes/RtlAdapterProvider';
import {
  StyledArrowIcon,
  StyledCrumbContainer,
  StyledCrumbContent,
  StyledCrumbIcon,
  StyledCrumbItem,
  StyledCrumbItemLabel,
  StyledCrumbReset,
  StyledResetIcon,
  StyledSelectCrumb,
} from './SelectCrumb.styled';

const CrumbItem = memo((props: CrumbItemProps) => {
  const { id, label, onPress } = props;

  const onPressHandler = useCallback(() => {
    onPress?.(id);
  }, [id, onPress]);

  return (
    <StyledCrumbItem>
      <StyledCrumbItemLabel className={classnames({ action: !!onPress })} onClick={onPressHandler}>
        {label}
      </StyledCrumbItemLabel>
      <StyledCrumbIcon>
        <StyledArrowIcon />
      </StyledCrumbIcon>
    </StyledCrumbItem>
  );
});

export interface SelectCrumbProps {
  crumbs: Omit<CrumbItemProps, 'isScroll'>[];
  className?: string;
}

export const SelectCrumb = memo((props: SelectCrumbProps) => {
  const { crumbs, className } = props;
  const [isScroll, setIsScroll] = useState(false);
  const ref = useRef<HTMLDivElement | null>(null);
  const tipRef = useRef<HTMLDivElement | null>(null);

  const i18n_goBack = fm('CapacityManage.goBack');

  const goBack = useCallback(() => {
    const { id, onPress } = crumbs?.[ZERO] ?? {};
    if (onPress) {
      onPress(id);
    }
  }, [crumbs]);

  useEffect(() => {
    if (ref.current && ref.current.clientWidth < ref.current.scrollWidth) {
      ref.current.scrollTo(isRtl() ? -ref.current.scrollWidth : ref.current.scrollWidth, ZERO);
      setIsScroll(true);
    } else {
      ref.current?.scrollTo?.(ZERO, ZERO);
      setIsScroll(false);
    }
  }, [crumbs]);

  return (
    <StyledSelectCrumb className={className}>
      <StyledCrumbContainer ref={ref}>
        {crumbs.map((crumb) => (
          <StyledCrumbContent key={crumb.id} $isScroll={isScroll}>
            <CrumbItem {...crumb} />
          </StyledCrumbContent>
        ))}
      </StyledCrumbContainer>
      {crumbs?.[ZERO]?.onPress && (
        <StyledCrumbReset ref={tipRef}>
          <Tooltip
            arrow={{ pointAtCenter: true }}
            getPopupContainer={() => tipRef.current ?? document.body}
            placement="top"
            title={i18n_goBack}
          >
            <StyledResetIcon as="div" onClick={goBack} />
          </Tooltip>
        </StyledCrumbReset>
      )}
    </StyledSelectCrumb>
  );
});

interface CrumbItemProps {
  id: number;
  label: string;
  onPress?: (id: number) => void;
}
