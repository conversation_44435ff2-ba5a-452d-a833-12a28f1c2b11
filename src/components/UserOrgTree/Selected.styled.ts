import styled from 'styled-components';

export const StyledSelected = styled.div`
  box-sizing: border-box;
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  width: 50%;
  height: 100%;
  border-left: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledSelectedList = styled.div`
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 4px 0;
`;
