import type { ChangeEventHandler, ReactNode } from 'react';
import React, { memo, useCallback, useState } from 'react';

import type { OrgSelectedProps } from '../OrgSelectCard/OrgSelect';
import { OrgSelect } from '../OrgSelectCard/OrgSelect';
import { StyledHeader, StyledSearch, StyledSearchOrSelect } from './SearchOrSelect.styled';

export interface SearchOrSelectProps<T = any> extends OrgSelectedProps<T> {
  placeholder?: string;
  title?: ReactNode;
  onSearch?: (value: string) => void;
}

export const SearchOrSelect = memo((props: SearchOrSelectProps) => {
  const { onSearch, title, placeholder, ...org } = props;
  const [value, setValue] = useState<string>();

  const searchChangeHandler: ChangeEventHandler<HTMLInputElement> = useCallback(
    (event) => {
      const { value } = event.target;
      setValue(value);
      onSearch?.(value);
    },
    [onSearch],
  );

  return (
    <StyledSearchOrSelect>
      {title && <StyledHeader>{title}</StyledHeader>}
      <StyledSearch
        allowClear
        placeholder={placeholder}
        value={value}
        onChange={searchChangeHandler}
        onSearch={onSearch}
      />
      <OrgSelect {...org} />
    </StyledSearchOrSelect>
  );
});
