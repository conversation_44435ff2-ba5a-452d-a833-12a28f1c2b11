import { Input } from 'antd';
import styled from 'styled-components';

export const StyledSearchOrSelect = styled.div`
  box-sizing: border-box;
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
`;

export const StyledHeader = styled.div`
  margin: 8px 0;
  padding: 0 12px;
  flex: 0 0 22px;
  height: 22px;
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const StyledSearch = styled(Input.Search)`
  margin-bottom: 8px;
  padding: 0 12px;
  flex: 0 0 32px;
  min-height: 0;

  &.ant-input-search > .ant-input-group {
    & .ant-input-affix-wrapper:not(:last-child) {
      border-radius: 2px 0 0 2px;
    }

    & > .ant-input-group-addon:last-child .ant-input-search-button {
      border-radius: 0 4px 4px 0;
    }
  }
`;
