import React, { memo } from 'react';

import type { SearchOrSelectProps } from './SearchOrSelect';
import { SearchOrSelect } from './SearchOrSelect';
import type { SelectedProps } from './Selected';
import { Selected } from './Selected';
import styles from './UserOrgSelector.less';

const DEFAULT_SELECTOR_HEIGHT = 460;

export interface UserOrgSelectorProps<T = any, U = any> {
  height?: number;
  select: SearchOrSelectProps<T>;
  selected: SelectedProps<U>;
}

export const UserOrgSelector = memo((props: UserOrgSelectorProps) => {
  const { height = DEFAULT_SELECTOR_HEIGHT, select, selected } = props;
  return (
    <div className={styles.styledUserOrgSelector} style={{ height: height }}>
      <SearchOrSelect {...select} />
      <Selected {...selected} />
    </div>
  );
});
