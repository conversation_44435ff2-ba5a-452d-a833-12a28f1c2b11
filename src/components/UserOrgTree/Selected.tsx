import type { ReactNode } from 'react';
import React, { memo } from 'react';

import { OrgSelectedEmpty } from '../OrgSelectCard/OrgSelectedEmpty';
import type { OrgSelectedHeaderProps } from '../OrgSelectCard/OrgSelectedHeader';
import { OrgSelectedHeader } from '../OrgSelectCard/OrgSelectedHeader';
import { StyledSelected, StyledSelectedList } from './Selected.styled';

export interface SelectedProps<T = any> {
  list: T[];
  itemRender: (item: T) => ReactNode;
  header: OrgSelectedHeaderProps;
}

export const Selected = memo((props: SelectedProps) => {
  const { list, header, itemRender } = props;

  return (
    <StyledSelected>
      <OrgSelectedHeader {...header} />
      {Array.isArray(list) && list.length > 0 ? (
        <StyledSelectedList>{list.map((item) => itemRender(item))}</StyledSelectedList>
      ) : (
        <OrgSelectedEmpty />
      )}
    </StyledSelected>
  );
});
