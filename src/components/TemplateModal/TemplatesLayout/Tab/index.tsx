import { memo, useCallback, useState } from 'react';

import { useTemplateStore } from '@/store/TemplateLib';

import style from './index.less';
type Props = {
  defaultVal?: string;
  changeTab?: (type: string) => void;
  list: {
    name: string;
    type: string;
  }[];
};
export const Tab = memo(({ list, defaultVal, changeTab }: Props) => {
  const { templateProp } = useTemplateStore((state) => state);
  const [activeTab, setActiveTab] = useState(defaultVal || list?.[0].type);
  const isAi = useCallback(() => {
    return templateProp.propType === 'ai';
  }, [templateProp.propType]);
  const goChangeTab = useCallback(
    (type: string) => {
      if (isAi()) {
        return;
      }
      setActiveTab(type);
      changeTab?.(type);
    },
    [changeTab],
  );
  return (
    <div className={style.list}>
      {list.map((item) => (
        <div
          key={item.type}
          className={`${style.listItem} ${activeTab === item.type && !isAi() ? style.listItemActive : null}`}
          onClick={() => goChangeTab(item.type)}
        >
          {item.name}
        </div>
      ))}
    </div>
  );
});
