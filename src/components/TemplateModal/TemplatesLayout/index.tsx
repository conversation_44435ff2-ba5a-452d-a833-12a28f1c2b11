import { Layout } from 'antd';
import { Button, Divider } from 'antd';
import { Content } from 'antd/es/layout/layout';
import Sider from 'antd/es/layout/Sider';
import { memo, useCallback, useEffect, useState } from 'react';

import MinifilesImg from '@/assets/images/template/minifiles.png';
import { ReactComponent as CloseSvg } from '@/assets/images/upload/close.svg';
import type { TabChildrenTypeModel } from '@/model/Template';
import { fm } from '@/modules/Locale';
import { useTemplateStore } from '@/store/TemplateLib';

import { TabContent } from '../../TabContent';
import type { TabsPropItems } from '..';
import { MyTempTabContent } from '../MyTempTabContent';
import style from './index.less';
import { Tab } from './Tab';
type Props = {
  parentGuid?: string;
  closeTemplate: () => void;
  children: ({ activeTab, operations, activeKey, ItemComponent, changeTab }: TabChildrenTypeModel) => React.ReactNode;
};
export const TemplateSLayout = memo(({ children, parentGuid, closeTemplate }: Props) => {
  const { templateProp } = useTemplateStore((state) => state);
  const titleText = fm('TempLatePicker.grapTempLibrary');
  const siderMenuCreateDocText = fm('SiderMenu.siderMenuCreateDocText');
  const siderMenuCreateMoDocText = fm('SiderMenu.siderMenuCreateMoDocText');
  const siderMenuCreateTableText = fm('SiderMenu.siderMenuCreateTableText');
  const siderMenuCreateMoTableText = fm('SiderMenu.siderMenuCreateMoTableText');
  const siderMenuCreatePptText = fm('SiderMenu.siderMenuCreatePptText');
  const siderMenuCreateFormText = fm('SiderMenu.siderMenuCreateFormText');
  const tabList = [
    {
      name: fm('TempLatePicker.enterpriseTemp'),
      type: 'publicTemplates',
    },
    {
      name: fm('TempLatePicker.myTemp'),
      type: 'customTemplates',
    },
  ];
  const [activeKey, setActiveKey] = useState('newdoc');
  const [tab, setTab] = useState('publicTemplates');

  const isAi = useCallback(() => {
    return templateProp.propType === 'ai';
  }, [templateProp.propType]);
  const operations = (
    <>
      <Divider type="vertical" /> <Button icon={<CloseSvg />} type="text" onClick={closeTemplate} />
    </>
  );
  const items: TabsPropItems[] = [
    {
      disabled: isAi(),
      key: 'newdoc',
      label: siderMenuCreateDocText,
      children: (
        <TabContent parentGuid={parentGuid} type="newdoc" visible={activeKey === 'newdoc'} onClose={closeTemplate} />
      ),
    },
    {
      disabled: isAi(),
      key: 'modoc',
      label: siderMenuCreateMoDocText,
      children: (
        <TabContent parentGuid={parentGuid} type="modoc" visible={activeKey === 'modoc'} onClose={closeTemplate} />
      ),
    },
    {
      disabled: isAi(),
      key: 'mosheet',
      label: siderMenuCreateTableText,
      children: (
        <TabContent parentGuid={parentGuid} type="mosheet" visible={activeKey === 'mosheet'} onClose={closeTemplate} />
      ),
    },
    {
      disabled: isAi(),
      key: 'table',
      label: siderMenuCreateMoTableText,
      children: (
        <TabContent parentGuid={parentGuid} type="table" visible={activeKey === 'table'} onClose={closeTemplate} />
      ),
    },
    {
      disabled: isAi(),
      key: 'presentation',
      label: siderMenuCreatePptText,
      children: (
        <TabContent
          parentGuid={parentGuid}
          type="presentation"
          visible={activeKey === 'presentation'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      disabled: isAi(),
      key: 'form',
      label: siderMenuCreateFormText,
      children: (
        <TabContent parentGuid={parentGuid} type="form" visible={activeKey === 'form'} onClose={closeTemplate} />
      ),
    },
    {
      disabled: !isAi(),
      key: 'ai',
      label: 'AI模版',
      children: <TabContent parentGuid={parentGuid} type="ai" visible={activeKey === 'ai'} onClose={closeTemplate} />,
    },
  ];

  const myTempItems: TabsPropItems[] = [
    {
      key: 'newdoc',
      label: siderMenuCreateDocText,
      children: (
        <MyTempTabContent
          parentGuid={parentGuid}
          type="newdoc"
          visible={activeKey === 'newdoc'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      key: 'modoc',
      label: siderMenuCreateMoDocText,
      children: (
        <MyTempTabContent
          parentGuid={parentGuid}
          type="modoc"
          visible={activeKey === 'modoc'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      key: 'mosheet',
      label: siderMenuCreateTableText,
      children: (
        <MyTempTabContent
          parentGuid={parentGuid}
          type="mosheet"
          visible={activeKey === 'mosheet'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      key: 'table',
      label: siderMenuCreateMoTableText,
      children: (
        <MyTempTabContent
          parentGuid={parentGuid}
          type="table"
          visible={activeKey === 'table'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      key: 'presentation',
      label: siderMenuCreatePptText,
      children: (
        <MyTempTabContent
          parentGuid={parentGuid}
          type="presentation"
          visible={activeKey === 'presentation'}
          onClose={closeTemplate}
        />
      ),
    },
    {
      key: 'form',
      label: siderMenuCreateFormText,
      children: (
        <MyTempTabContent parentGuid={parentGuid} type="form" visible={activeKey === 'form'} onClose={closeTemplate} />
      ),
    },
  ];
  const ItemComponent: Record<string, TabsPropItems[]> = {
    publicTemplates: items,
    customTemplates: myTempItems,
  };
  const layoutChangeTab = useCallback((tab: string) => {
    setTab(tab);
  }, []);
  const changeTab = useCallback((tab: string) => {
    setActiveKey(tab);
  }, []);
  useEffect(() => {
    if (isAi()) {
      setActiveKey('ai');
    } else {
      setActiveKey('newdoc');
    }
    if (templateProp.isShowTemplateLib) {
      setTab('publicTemplates');
    }
  }, [tab, templateProp.isShowTemplateLib]);

  return (
    <Layout>
      <Sider className={style.sider} width="196">
        <div className={style.title}>
          <img className={style.img} src={MinifilesImg} />
          <div>{titleText}</div>
        </div>
        <Tab changeTab={layoutChangeTab} list={tabList} />
      </Sider>
      <Content>
        {children({
          activeKey,
          operations,
          ItemComponent,
          changeTab,
          activeTab: tab,
        })}
      </Content>
    </Layout>
  );
});
