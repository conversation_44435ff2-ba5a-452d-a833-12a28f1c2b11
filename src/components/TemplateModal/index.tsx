import { Modal, Tabs } from 'antd';
import React, { useCallback } from 'react';

import type { FileType } from '@/constants/fileList.config';
import { CustomEventName } from '@/model/CustomEvent';
import type { TabChildrenTypeModel } from '@/model/Template';
import { useTemplateStore } from '@/store/TemplateLib';
import { emitCustomEvent } from '@/utils/customEvent';

import { TemplateSLayout } from './TemplatesLayout';
export interface TemplateModalProps {
  visible: boolean;
  closeTemplate: () => void;
  parentGuid?: string;
}

export interface TabsPropItems {
  key: FileType | 'ai';
  label: string;
  children: JSX.Element;
  disabled?: boolean;
}

export const TemplateModal = React.memo(function TemplateModal() {
  const { templateProp, setTemplateProp } = useTemplateStore((state) => state);
  const RenderContent = ({ activeKey, ItemComponent, operations, changeTab, activeTab }: TabChildrenTypeModel) => (
    <Tabs
      activeKey={activeKey}
      defaultActiveKey={activeKey}
      items={ItemComponent[activeTab]}
      tabBarExtraContent={operations}
      onChange={changeTab}
    />
  );

  const closeTemplate = useCallback(() => {
    setTemplateProp({ ...templateProp, isShowTemplateLib: false });
    emitCustomEvent(CustomEventName.aiTemplateCancel, {});
  }, [setTemplateProp, templateProp]);

  return (
    <Modal
      centered
      classNames={{ content: 'templateModel' }}
      closeIcon={false}
      footer={null}
      open={templateProp.isShowTemplateLib}
      width={960}
      z-index={2000}
    >
      <TemplateSLayout closeTemplate={closeTemplate} parentGuid={templateProp.parentGuid}>
        {({ activeTab, operations, activeKey, ItemComponent, changeTab }) => (
          <RenderContent
            activeKey={activeKey}
            activeTab={activeTab}
            changeTab={changeTab}
            ItemComponent={ItemComponent}
            operations={operations}
          />
        )}
      </TemplateSLayout>
    </Modal>
  );
});
