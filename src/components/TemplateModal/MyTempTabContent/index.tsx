import 'overlayscrollbars/overlayscrollbars.css';

import type { MenuProps } from 'antd';
import { Dropdown, message, Spin, Tooltip } from 'antd';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'umi';

import { deleteByTemplate, loadTemplateTypeData, updateByTemplate } from '@/api/Template';
import homeEmptySrc from '@/assets/images/template/home-empty.png';
import verMoreSrc from '@/assets/images/template/ver-more.png';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { useMyTemplate } from '@/hooks/useMyTemplate';
import type { FileCreateType } from '@/model/Common';
import type { FormTabConfig, FormTabItem, TabItemBase, TemplateDataItem } from '@/model/Template';
import { fm } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import { TemplateReview } from '../../TemplateReview';
import styles from './index.less';
import RenameModal from './RenameModal';
// 提取要排除的类型为单独的类型
interface TabContentProp {
  parentGuid?: string;
  type: FileCreateType;
  visible?: boolean;
  onClose: () => void;
}
// 联合所有可能的标签配置类型
type TabConfig = TabItemBase | FormTabConfig;
type TabTypeMap = Record<FileCreateType, TabConfig>;

const initialPage = 1;
const pageSize = 20;

export const MyTempTabContent = React.memo(({ type, visible, onClose, parentGuid }: TabContentProp) => {
  const noTemplateText = fm('TemplateContent.noTemplate');
  const upperTempFileAdd = fm('TemplateContent.upperTempFileAdd');
  const i18nText = {
    preview: fm('TabContent.preview'),
    use: fm('TabContent.use'),
    testFormSubTitle: fm('TabContent.testFormSubTitle'),
    tableFormSubTitle: fm('TabContent.tableFormSubTitle'),
    formSubTitle: fm('TabContent.formSubTitle'),
    pptSubTitle: fm('TabContent.pptSubTitle'),
    tableSubTitle: fm('TabContent.tableSubTitle'),
    sheetSubTitle: fm('TabContent.sheetSubTitle'),
    docxSubTitle: fm('TabContent.docxSubTitle'),
    docSubTitle: fm('TabContent.docSubTitle'),
    homeSubTitle: fm('TabContent.homeSubTitle'),
    empty: fm('TabContent.empty'),
    noSupport: fm('Editor.noSupport'),
    title: `${fm('deleteConfirm.title')}?`,
    okText: fm('deleteConfirm.title'),
    content: fm('TempLatePicker.deleteContent'),
    cancelText: fm('deleteConfirm.cancel'),
    successText: fm('ShareCollaboration.modifySuccess'),
    deleteSuccess: fm('deleteConfirm.success'),
    deleteError: fm('deleteConfirm.error'),
  };
  const { goDetails } = useMyTemplate();
  const { formatTime } = useFormatTime();
  const params = useParams<{ guid: string }>();
  const guid = parentGuid ? parentGuid : params?.guid || 'Desktop';
  const [renameVisible, setRenameVisible] = useState(false);

  const tabTypeMap: TabTypeMap = {
    newdoc: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -2,
    },
    modoc: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -6,
    },
    mosheet: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -4,
    },
    table: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -11,
    },
    presentation: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -10,
    },
    form: {
      icon: homeEmptySrc,
      smallIcon: '',
      title: `${noTemplateText}`,
      subTitle: upperTempFileAdd,
      typeValue: -8,
    },
  };
  const [emptyItem] = useState<TabConfig>(tabTypeMap[type]);

  const toUseTemp = useCallback(
    (item: TemplateDataItem) => {
      goDetails(
        {
          originGuid: item.guid,
          type: item.type,
        },
        guid,
      );
      onClose();
    },
    [goDetails, onClose, guid],
  );

  const emptyItemTemp = useCallback((item: FormTabItem) => {
    return (
      <div key={item.title} className={styles.emptyItem}>
        <div className={styles.icon}>
          <img src={item.icon} />
        </div>
        <div className={styles.title}>{item.title}</div>
        <div className={styles.subTitle}>{item.subTitle}</div>
      </div>
    );
  }, []);

  const [dataList, setDataList] = useState<TemplateDataItem[]>([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [itemData, setItemData] = useState<TemplateDataItem>();

  const emptyFile = useCallback(() => {
    return emptyItemTemp(emptyItem as FormTabItem);
  }, [emptyItem, emptyItemTemp]);

  const [isLoading, setIsLoading] = useState(true);

  // 加载数据的函数
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const {
        data: { customTemplates },
      } = await loadTemplateTypeData({ type: emptyItem.typeValue || undefined, page: initialPage, pageSize });
      setDataList(
        customTemplates.map((item: TemplateDataItem) => {
          item.isTipMore = false;
          return item;
        }) || [],
      );
    } finally {
      setIsLoading(false);
    }
  }, [emptyItem.typeValue]);

  useEffect(() => {
    if (type) {
      fetchData();
    }
  }, [type, fetchData]);

  const contentRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (visible === false) {
      setOpenDrawer(false); //关闭 drawer
    }
    contentRef.current?.scrollIntoView({
      block: 'start', // 对齐顶部
    });
  }, [visible]);
  const renameCallback = useCallback(
    async (values?: Record<string, string>) => {
      if (values) {
        if (itemData) {
          const res = await updateByTemplate({ ...itemData, ...values } as TemplateDataItem);
          if (res.status === 200 || res.status === 204) {
            message.success(i18nText.successText);
            setRenameVisible(false);
            setItemData(undefined);
            fetchData();
          } else {
            message.error(res.data.msg);
          }
        }
      } else {
        setRenameVisible(false);
      }
    },
    [fetchData, itemData, i18nText.successText],
  );
  const items: MenuProps['items'] = [
    {
      label: fm('File.reName'),
      key: 'rename',
      onClick: (event) => {
        event?.domEvent?.stopPropagation();
        setRenameVisible(true);
      },
    },
    {
      type: 'divider',
    },
    {
      label: fm('Space.rightClickDelete'),
      key: 'delete',
      danger: true,
      onClick: (event) => {
        event?.domEvent?.stopPropagation();
        if (itemData) {
          deleteConfirm({
            i18nText: {
              ...i18nText,
              error: i18nText.deleteError,
              success: i18nText.deleteSuccess,
            },
            data: itemData,
            api: deleteByTemplate,
            callback: fetchData,
          });
          setItemData(undefined);
        }
      },
    },
  ];
  const clickMoreItem = useCallback((event: React.MouseEvent<HTMLImageElement>, item: TemplateDataItem) => {
    event.stopPropagation();
    setItemData(item);
  }, []);

  const changeOpenDropdown = useCallback(
    (data: TemplateDataItem, bol: boolean) => {
      const newDataList = [...dataList];
      const index = newDataList.findIndex((item) => item.guid === data.guid);
      if (index !== -1) {
        const data = newDataList[index];
        newDataList.forEach((item) => {
          if (bol) {
            item.isTipMore = item.guid === data.guid ? bol : !bol;
          } else {
            item.isTipMore = bol;
          }
        });
        setDataList(newDataList);
      }
    },
    [dataList],
  );

  const dataFileList = (
    <OverlayScrollbarsComponent
      className={styles.listContent}
      options={{
        scrollbars: {
          autoHide: 'scroll', // 滚动时隐藏滚动条
          clickScroll: true, // 点击轨道滚动
        },
      }}
    >
      <div ref={contentRef} className={styles.scrollContent}>
        {dataList.map((item) => (
          <Tooltip key={item.guid} title={item.name}>
            <div
              className={styles.dataItem}
              onClick={() => toUseTemp(item)}
              onMouseEnter={() => changeOpenDropdown(item, true)}
              onMouseLeave={() => changeOpenDropdown(item, false)}
            >
              <div className={styles.more}>
                {item.isTipMore && (
                  <Dropdown
                    getPopupContainer={(node) => node.parentNode as HTMLElement}
                    menu={{ items }}
                    trigger={['click']}
                  >
                    <img className={styles.moreImg} src={verMoreSrc} onClick={(event) => clickMoreItem(event, item)} />
                  </Dropdown>
                )}
              </div>

              <div className={styles.img} style={{ background: `url(${item.img}) no-repeat center center/cover` }} />
              <div className={styles.name}>{item.name}</div>
              <div className={styles.updatedAt}>{formatTime(item.updated_at)}</div>
            </div>
          </Tooltip>
        ))}
      </div>
    </OverlayScrollbarsComponent>
  );
  return (
    <Spin spinning={isLoading}>
      <div className={styles.content}>
        {!isLoading && (dataList.length ? dataFileList : emptyFile())}
        <TemplateReview itemData={itemData} open={openDrawer} onClose={() => setOpenDrawer(false)} />
      </div>
      <RenameModal callback={renameCallback} params={itemData as TemplateDataItem} visible={renameVisible} />
    </Spin>
  );
});
