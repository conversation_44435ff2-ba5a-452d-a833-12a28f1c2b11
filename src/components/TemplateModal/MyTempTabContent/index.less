.content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 540px;
  position: relative;
  overflow: hidden;

  .formEmptyContent {
    display: flex;
  }

  .emptyItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    &:hover {
      box-shadow: var(--theme-box-shadow-color-level6);
    }

    .icon {
      width: 120px;
      height: 110px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .title {
      text-align: center;
      font-size: 14px;
      font-weight: 500;
    }

    .subTitle {
      text-align: center;
      font-size: 14px;
      line-height: 24px;
    }
  }

  .listContent {
    display: flex;
    width: 100%;
    height: 100%;
  }

  .listContent .scrollContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    gap: 16px;
  }

  .listContent .scrollContent .dataItem {
    width: 230px;
    padding: 20px 60px;
    box-sizing: border-box;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid var(--theme-card-info-bg-hover-border);
    cursor: pointer;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 4%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    content-visibility: auto;
    position: relative;
  }

  .dataItem .img {
    width: 60px;
    height: 60px;
  }

  .listContent .scrollContent .formEmptyContent {
    display: flex;
    height: 172px;
  }
}

.moreImg {
  width: 20px;
  cursor: pointer;
}

.name {
  font-size: 16px;
  font-weight: 500;
  margin-top: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 100%;
}

.updatedAt {
  font-size: 12px;
  color: var(--theme-text-color-secondary);
}

.emptyItem {
  width: 100%;
  padding: 20px;
  border-radius: 4px;
}

.emptyItem .icon {
  width: 72px;
  height: 72px;
}

.emptyItem .icon img {
  width: 100%;
  height: 100%;
}

.more {
  width: 100%;
  position: absolute;
  right: 5px;
  top: 5px;
  text-align: right;
  transform: 200ms;

  :global {
    .ant-dropdown-menu-item {
      text-align: left;
    }
  }
}

.emptyItem .title {
  text-align: center;
  margin-top: 8px;
  font-size: 16px;
  line-height: 24px;
}

.emptyItem .subTitle {
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  color: var(--theme-text-color-secondary);
}
