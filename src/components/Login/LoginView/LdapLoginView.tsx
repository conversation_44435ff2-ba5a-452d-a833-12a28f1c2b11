import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { Button, Form, Input, message } from 'antd';
import { useCallback, useState } from 'react';

import { loginByLdap } from '@/api/Me';
import { catchApiResult } from '@/api/Request';
import { useFormatMessage } from '@/modules/Locale';

import styles from './style.less';

interface LoginViewProps {
  onSuccess?: () => void;
}

export default function LdapLoginView(props: LoginViewProps) {
  const { onSuccess } = props;
  const [submitting, setSubmitting] = useState(false);

  const userNameInputPlaceholder = useFormatMessage('LoginView.ldapLoginUserNameInputPlaceholder');
  const passwordInputPlaceholder = useFormatMessage('LoginView.passwordInputPlaceholder');
  const loginButtonText = useFormatMessage('LoginView.loginButtonText');
  const userNameInputLabel = useFormatMessage('LoginView.ldapLoginUserNameInputLabel');
  const passwordInputLabel = useFormatMessage('LoginView.passwordInputLabel');
  const loginError = useFormatMessage('LoginView.loginError');

  const onFinishHandler = useCallback(
    async (values: { username: string; password: string }) => {
      setSubmitting(true);
      const [err] = await catchApiResult(loginByLdap(values.username, values.password));
      if (err) {
        message.error(loginError);
        setSubmitting(false);
      } else {
        onSuccess?.();
      }
    },
    [loginError, onSuccess],
  );

  return (
    <Form
      disabled={submitting}
      initialValues={{ remember: true }}
      name="login"
      style={{ width: 324 }}
      onFinish={onFinishHandler}
    >
      <div className={styles.label}>{userNameInputLabel}</div>
      <Form.Item name="username" rules={[]} validateFirst={true} validateTrigger={['onBlur', 'submit']}>
        <Input placeholder={userNameInputPlaceholder} size="large" />
      </Form.Item>
      <div className={styles.label}>{passwordInputLabel}</div>
      <Form.Item name="password" rules={[]}>
        <Input.Password
          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          placeholder={passwordInputPlaceholder}
          size="large"
          type="password"
        />
      </Form.Item>
      <Form.Item>
        <Button block htmlType="submit" loading={submitting} size="large" type="primary">
          {loginButtonText}
        </Button>
      </Form.Item>
    </Form>
  );
}
