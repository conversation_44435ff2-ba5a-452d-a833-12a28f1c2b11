import styled from 'styled-components';

export const StyledHeaderTab = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32px;
  cursor: pointer;
  white-space: nowrap;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    cursor: default;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      height: 2px;
      background: var(--theme-basic-color-bg-gray);
      width: 100%;
    }
  }
`;

export const StyledLabel = styled.div`
  position: absolute;
  font-size: 16px;
  color: var(--theme-basic-color-primary);

  &.active {
    font-weight: 500;
  }

  &.block {
    position: unset;
    visibility: hidden;
  }
`;
