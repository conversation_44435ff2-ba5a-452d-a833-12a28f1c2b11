import React, { memo } from 'react';

import { StyledHeaderTab, StyledLabel } from './HeaderTab.styled';

export interface HeaderTabProps<T = string | any> {
  id: T;
  label: string;
  isActive: boolean;
  onPress: () => void;
}

export const HeaderTab = memo((props: HeaderTabProps) => {
  const { id, label, isActive, onPress } = props;
  return (
    <StyledHeaderTab key={id} className={isActive ? 'active' : ''} onClick={isActive ? undefined : onPress}>
      <StyledLabel className="block">{label}</StyledLabel>
      <StyledLabel className={isActive ? 'active' : ''}>{label}</StyledLabel>
    </StyledHeaderTab>
  );
});
