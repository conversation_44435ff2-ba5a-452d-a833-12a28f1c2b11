import React, { memo } from 'react';

import type { HeaderTabProps } from './HeaderTab';
import { HeaderTab } from './HeaderTab';
import { StyledHeaderTabs } from './HeaderTabs.styled';

interface HeaderTabsProps<T = any> {
  tabs: HeaderTabProps<T>[];
}

export const HeaderTabs = memo((props: HeaderTabsProps) => {
  const { tabs } = props;
  return (
    <StyledHeaderTabs>
      {tabs.map((tab) => (
        <HeaderTab key={tab.id} {...tab} />
      ))}
    </StyledHeaderTabs>
  );
});
