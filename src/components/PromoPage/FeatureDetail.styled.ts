import styled from 'styled-components';

import { Image } from '../Image';

export const StyledFeatureDetail = styled.div`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 960px;
  height: 240px;
  margin-top: 100px;

  &:nth-child(2) > img,
  &:nth-child(4) > img {
    order: -1;
  }
`;

export const StyledFeatureDetailNotes = styled.div`
  box-sizing: border-box;
  width: 400px;
  min-height: 0;
`;

export const StyledFeatureDetailTitle = styled.div`
  box-sizing: border-box;
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  color: var(--theme-text-color-default);
`;

export const StyledFeatureDetailDescription = styled.div`
  box-sizing: border-box;
  font-size: 14px;
  line-height: 24px;
  word-break: break-word;
  color: var(--theme-text-color-medium);
`;

export const StyledFeatureDetailImage = styled(Image)`
  box-sizing: border-box;
  height: 100%;
  width: 520px;

  /* 暗色主题下调整透明度 */
  .shimo-dark & {
    opacity: 0.85;
  }
`;
