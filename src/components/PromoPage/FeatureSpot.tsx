import s18n from '@shimo/simple-i18n';
import React, { memo } from 'react';

import {
  StyledFeatureSpot,
  StyledSpotContainer,
  StyledSpotHeader,
  StyledSpotImage,
  StyledSpotOrder,
  StyledSpotTitle,
} from './FeatureSpot.styled';
import type { FeatureSpotType } from './type';

interface FeatureSpotProps extends FeatureSpotType {
  order: number;
}
export const FeatureSpot = memo((spot: FeatureSpotProps) => {
  const { title, imgSrc, order } = spot;
  return (
    <StyledFeatureSpot>
      <StyledSpotContainer>
        <StyledSpotHeader>
          {s18n('功能亮点')}
          <StyledSpotOrder>{order}</StyledSpotOrder>
        </StyledSpotHeader>
        <StyledSpotTitle>{title}</StyledSpotTitle>
      </StyledSpotContainer>
      <StyledSpotImage src={imgSrc} />
    </StyledFeatureSpot>
  );
});
