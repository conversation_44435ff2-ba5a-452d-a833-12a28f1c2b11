import React, { memo } from 'react';

import {
  StyledFeatureDetail,
  StyledFeatureDetailDescription,
  StyledFeatureDetailImage,
  StyledFeatureDetailNotes,
  StyledFeatureDetailTitle,
} from './FeatureDetail.styled';
import type { FeatureDetailType } from './type';

export const FeatureDetail = memo((detail: FeatureDetailType) => {
  const { title, description, imgSrc } = detail;
  return (
    <StyledFeatureDetail>
      <StyledFeatureDetailNotes>
        <StyledFeatureDetailTitle>{title}</StyledFeatureDetailTitle>
        <StyledFeatureDetailDescription>{description}</StyledFeatureDetailDescription>
      </StyledFeatureDetailNotes>
      <StyledFeatureDetailImage src={imgSrc} />
    </StyledFeatureDetail>
  );
});
