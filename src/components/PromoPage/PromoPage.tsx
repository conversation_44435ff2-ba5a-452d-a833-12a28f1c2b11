import { Button } from 'antd';
import React, { memo } from 'react';

import { ONE } from '../../configs/configs';
// import { ModuleHeader } from '../ModuleHeader';
import { FeatureDetail } from './FeatureDetail';
import { FeatureSpot } from './FeatureSpot';
import {
  StyledBanner,
  StyledBannerDescription,
  StyledBannerHeader,
  StyledBannerIcon,
  StyledBannerImage,
  StyledBannerNotes,
  StyledBannerNotesContainer,
  StyledBannerTitle,
  StyledCard,
  StyledContainer,
  StyledFeatureDetail,
  StyledFeatureSpots,
  StyledPromoPage,
} from './PromoPage.styled';
import type { PromoPageProps } from './type';

export const PromoPage = memo((props: PromoPageProps) => {
  const { containerRef, action, banner, featureSpots, featureDetails } = props;

  return (
    <StyledPromoPage ref={containerRef}>
      <StyledContainer>
        <StyledCard>
          <StyledBanner $backgroundImgSrc={banner.backgroundImgSrc}>
            <StyledBannerNotesContainer>
              <StyledBannerNotes>
                <StyledBannerHeader>
                  <StyledBannerTitle>{banner.title}</StyledBannerTitle>
                  <StyledBannerIcon src={banner.icon} />
                </StyledBannerHeader>
                <StyledBannerDescription>{banner.description}</StyledBannerDescription>
              </StyledBannerNotes>
              <div>
                <Button href={action.href} target={action.target} type={action.type}>
                  {action.text}
                </Button>
              </div>
            </StyledBannerNotesContainer>
            <StyledBannerImage src={banner.bannerImgSrc} />
          </StyledBanner>
          <StyledFeatureSpots>
            {featureSpots.map((spot, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <FeatureSpot key={index} {...spot} order={index + ONE} />
            ))}
          </StyledFeatureSpots>
          <StyledFeatureDetail>
            {featureDetails.map((detail, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <FeatureDetail key={index} {...detail} />
            ))}
          </StyledFeatureDetail>
        </StyledCard>
      </StyledContainer>
      {/* {props.header && <ModuleHeader {...header} action={action} />} */}
    </StyledPromoPage>
  );
});
