import type { ButtonType } from 'antd/lib/button';
import type { HTMLAttributeAnchorTarget, MutableRefObject } from 'react';

export interface FeatureSpotType {
  title: string;
  imgSrc: string;
}

export interface FeatureDetailType {
  title: string;
  imgSrc: string;
  description: string | string[];
}

export interface BannerType {
  icon?: string;
  title: string;
  description: string | string[];
  bannerImgSrc: string;
  backgroundImgSrc: string;
}

export interface PromoPageProps {
  containerRef: MutableRefObject<HTMLDivElement | null>;
  header: {
    title: string;
    theme: string;
    teamId?: number;
    teamName?: string;
  } | null;
  action: {
    text: string;
    href: string;
    type: ButtonType;
    target: HTMLAttributeAnchorTarget;
  };
  banner: BannerType;
  featureSpots: FeatureSpotType[];
  featureDetails: FeatureDetailType[];
}
