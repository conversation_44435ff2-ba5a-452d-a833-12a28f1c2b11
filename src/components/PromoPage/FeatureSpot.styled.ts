import styled from 'styled-components';

import { Image } from '../Image';

export const StyledFeatureSpot = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 222px;
  height: 240px;
  margin-right: 24px;
  border-radius: 8px;
  background: var(--theme-layout-color-bg-new-page);
  border: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    margin-right: unset;
  }
`;

export const StyledSpotContainer = styled.div`
  box-sizing: border-box;
  padding: 16px 16px 0;
`;

export const StyledSpotHeader = styled.div`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  line-height: 24px;
  color: var(--theme-text-color-secondary);
`;

export const StyledSpotOrder = styled.div`
  box-sizing: border-box;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: 15px;
  background: var(--theme-layout-color-bg-white);
`;

export const StyledSpotTitle = styled.div`
  box-sizing: border-box;
  margin-top: 4px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: var(--theme-text-color-default);
`;

export const StyledSpotImage = styled(Image)`
  display: block;
  width: 100%;
  height: 146px;
  border-radius: 0 0 8px 8px;

  /* 暗色主题下调整透明度 */
  .shimo-dark & {
    opacity: 0.85;
  }
`;
