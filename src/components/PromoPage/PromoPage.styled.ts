import styled from 'styled-components';

import { Image } from '../Image';

export const StyledPromoPage = styled.div`
  box-sizing: border-box;
  width: 100%;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
`;

export const StyledContainer = styled.div`
  box-sizing: border-box;
  width: 100%;
  padding: 16px;
  min-height: 0;
`;

export const StyledCard = styled.div`
  box-sizing: border-box;
  border-radius: 8px;
  overflow: hidden;
  background: var(--theme-layout-color-bg-white);
  border: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledBanner = styled.div<{ $backgroundImgSrc: string }>`
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  width: 100%;
  height: 380px;
  padding: 60px 0 80px;
  background-size: 100%;
  background-repeat: no-repeat;
  background-image: url(${({ $backgroundImgSrc }) => $backgroundImgSrc});
`;

export const StyledBannerNotesContainer = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 460px;
  margin-right: 40px;
`;

export const StyledBannerNotes = styled.div`
  box-sizing: border-box;
  width: 100%;
`;

export const StyledBannerHeader = styled.div`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 34px;
  margin-bottom: 8px;
`;

export const StyledBannerTitle = styled.div`
  box-sizing: border-box;
  font-size: 24px;
  font-weight: 500;
  color: var(--theme-text-color-default);
`;

export const StyledBannerIcon = styled(Image)`
  box-sizing: border-box;
  height: 28px;
  width: 28px;
  margin-left: 12px;
`;

export const StyledBannerDescription = styled.div`
  box-sizing: border-box;
  font-size: 14px;
  line-height: 24px;
  word-break: break-word;
  color: var(--theme-text-color-medium);
`;

export const StyledBannerImage = styled(Image)`
  box-sizing: border-box;
  width: 460px;
  border-radius: 8px;

  /* 暗色主题下调整透明度 */
  .shimo-dark & {
    opacity: 0.85;
  }
`;

export const StyledFeatureSpots = styled.div`
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  margin-top: 20px;
  width: 100%;
  height: 240px;
`;

export const StyledFeatureDetail = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;
`;
