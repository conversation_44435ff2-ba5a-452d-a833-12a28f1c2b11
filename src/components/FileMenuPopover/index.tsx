import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';

import arrowRight from '@/assets/images/common/<EMAIL>';
import checkOutlined from '@/assets/images/common/<EMAIL>';
import { fm } from '@/modules/Locale';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';

import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; exportType?: string; fileGuid?: string; noSupport?: boolean }) => void;
  onOpenInNewTab?: (key: string) => void;
  fileType?: string;
  isFavorite?: boolean;
  isAdmin?: boolean;
  role?: string;
  quickAccessAdded?: boolean;
  subscribed?: boolean;
  showComment?: boolean;
  showMenu?: boolean;
  showWriter?: boolean;
}

export interface MenuItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  subItems?: MenuItem[];
  showDivider?: boolean;
  exportType?: string;
  disabled?: boolean;
  noSupport?: boolean;
  hide?: boolean;
  disabledTip?: string;
  active?: boolean;
}

export function FileMenuPopover({
  onItemClick,
  fileType = '',
  isFavorite = false,
  isAdmin,
  role,
  quickAccessAdded = false,
  subscribed = false,
  showComment = false,
  showMenu = false,
  showWriter = false,
}: PopoverProps) {
  // 新增子菜单弹出状态管理
  const [subMenuVisible, setSubMenuVisible] = useState<Record<string, boolean>>({});

  // 当组件重新渲染时，重置所有子菜单的显示状态
  useEffect(() => {
    setSubMenuVisible({});
  }, [fileType, isFavorite]);

  const i18nText = {
    newdoc: fm('File.newdoc'),
    mosheet: fm('File.mosheet'),
    board: fm('File.board'),
    mindmap: fm('File.mindmap'),
    subscribeUpdate: fm('FileMenuPopover.subscribeUpdate'),
    subscribeUpdatePpt: fm('FileMenuPopover.subscribeUpdatePpt'),
    lockFile: fm('FileMenuPopover.lockFile'),
    saveTemplate: fm('FileMenuPopover.saveTemplate'),
    viewHistory: fm('FileMenuPopover.viewHistory'),
    saveVersion: fm('FileMenuPopover.saveVersion'),
    addComment: fm('FileMenuPopover.addComment'),
    viewComment: fm('FileMenuPopover.viewComment'),
    guide: fm('FileMenuPopover.guide'),
    shortcut: fm('FileMenuPopover.shortcut'),
    helpCenter: fm('FileMenuPopover.helpCenter'),
  };

  const i18n = {
    modoc: fm('useFileDetail.modocTitle'),
    newdoc: fm('useFileDetail.modocTitle'),
    mosheet: fm('useFileDetail.mosheetTitle'),
    table: fm('useFileDetail.tableTitle'),
    presentation: fm('useFileDetail.pptTitle'),
    form: fm('useFileDetail.formTitle'),
    mindmap: fm('useFileDetail.mindmapTitle'),
  };

  const SUPPORT_VIEW_CONFIG = [
    {
      key: 'move', // 移动
      title: fm('FileMenuPopover.move'),
    },
    {
      key: 'createCopy', // 创建副本
      title: fm('FileMenuPopover.createCopy'),
      showDivider: true,
    },
    {
      key: 'fileInfo', // 文档信息
      title: fm('FileMenuPopover.documentInfo'),
      showDivider: true,
    },
    {
      key: 'delete', // 删除
      title: fm('FileMenuPopover.delete'),
    },
  ] satisfies MenuItem[];

  function getItems(type: string): MenuItem[] {
    const helpItemsMap = {
      newdoc: [
        { key: 'guide', title: `${i18nText.newdoc + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.newdoc + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.newdoc + i18nText.helpCenter}` }, // 帮助中心
      ],
      mosheet: [
        { key: 'guide', title: `${i18nText.mosheet + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.mosheet + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.mosheet + i18nText.helpCenter}` }, // 帮助中心
      ],
      board: [
        { key: 'guide', title: `${i18nText.board + i18nText.guide}`, noSupport: true }, // 使用指南
        { key: 'shortcut', title: `${i18nText.board + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.board + i18nText.helpCenter}` }, // 帮助中心
      ],
      mindmap: [
        { key: 'guide', title: `${i18nText.mindmap + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.mindmap + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.mindmap + i18nText.helpCenter}` }, // 帮助中心
      ],
    };

    const commonItems: MenuItem[] = [
      {
        key: 'addToQuickAccess',
        title: fm('QuickAccess.add'),
        icon: quickAccessAdded ? <img src={checkOutlined} width={12} /> : null,
        active: quickAccessAdded,
      },
      ...(type !== FileIconType.Table
        ? [
            {
              key: 'subscribeUpdate',
              title: type !== FileIconType.Presentation ? i18nText.subscribeUpdate : i18nText.subscribeUpdatePpt,
              icon: subscribed ? <img src={checkOutlined} width={12} /> : null,
              active: subscribed,
              hide: true,
            },
          ]
        : []),
      {
        key: 'favorite',
        title: fm('FileMenuPopover.favorite'),
        icon: isFavorite ? <img src={checkOutlined} width={12} /> : null,
        showDivider: true,
        active: isFavorite,
      },
      {
        key: 'move',
        title: fm('FileMenuPopover.move'),
        disabled: !isAdmin,
        disabledTip: fm('FileMenuPopover.noMovePermissionTip'),
      },
      {
        key: 'shortcut',
        title: fm('RightclickMouse.createShortcut'),
      },
      {
        key: 'lockFile',
        title: i18nText.lockFile,
        noSupport: true,
      },
      {
        key: 'createCopy',
        title: fm('FileMenuPopover.createCopy'),
        showDivider: type === 'table' || type === 'form',
        disabled: !role,
        disabledTip: fm('FileMenuPopover.noCreateCopyPermissionTip'),
      },
    ];

    // 剔除'锁定文件'
    const commonitems_board = commonItems.filter((item) => item.key !== 'lockFile');

    switch (type) {
      case 'newdoc':
      case 'modoc':
      case 'mosheet': {
        const downItemsMap = {
          newdoc: [
            { key: 'downImage', exportType: 'jpg', title: fm('FileMenuPopover.downImage') },
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
            { key: 'downMarkdown', exportType: 'md', title: fm('FileMenuPopover.downMarkdown') },
          ],
          modoc: [
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downWPS', exportType: 'wps', title: fm('FileMenuPopover.downWPS') },
            { key: 'downImagePDF', exportType: 'imagePdf', title: fm('FileMenuPopover.downImagePDF') },
            { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
          ],
          mosheet: [
            { key: 'downExcel', exportType: 'xlsx', title: fm('FileMenuPopover.downExcel') },
            {
              key: 'downPDF',
              exportType: 'pdf',
              title: fm('FileMenuPopover.downPDF'),
            },
            {
              key: 'downImage',
              exportType: 'image',
              title: fm('FileMenuPopover.downImage'),
            },
          ],
        };

        const downItems = downItemsMap[type];

        return [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: downItems,
          },
          { key: 'print', title: fm('FileMenuPopover.print'), showDivider: true },
          { key: 'saveTemplate', title: i18nText.saveTemplate }, //保存模板
          { key: 'saveVersion', title: i18nText.saveVersion, showDivider: true },
          { key: 'viewHistory', title: i18nText.viewHistory },
          ...(type === FileIconType.Doc
            ? [
                {
                  key: 'show',
                  title: fm('FileMenuPopover.show'),
                  subItems: [
                    {
                      key: 'comment',
                      exportType: 'comment',
                      title: fm('FileMenuPopover.showCommentCards'),
                      active: showComment,
                      icon: showComment ? <img src={checkOutlined} width={12} /> : null,
                    },
                    {
                      key: 'menu',
                      exportType: 'menu',
                      title: fm('FileMenuPopover.showMenu'),
                      active: showMenu,
                      icon: showMenu ? <img src={checkOutlined} width={12} /> : null,
                    },
                    {
                      key: 'writer',
                      exportType: 'writer',
                      title: fm('FileMenuPopover.showWriter'),
                      active: showWriter,
                      icon: showWriter ? <img src={checkOutlined} width={12} /> : null,
                    },
                  ],
                  showDivider: true,
                },
              ]
            : []), // 显示/隐藏
          ...(type !== 'modoc' ? [{ key: 'viewCommentList', title: fm('FileMenuPopover.viewCommentList') }] : []),
          ...(type === FileIconType.Mosheet
            ? [{ key: 'viewLockMosheet', title: fm('FileMenuPopover.viewLockMosheet') }]
            : []), //查看锁定工作表
          { key: 'fileInfo', title: i18n[type], showDivider: type === 'modoc' },

          // 表格、轻文档、白板、思维导图
          ['mosheet', 'newdoc', 'board', 'mindmap'].includes(type)
            ? {
                key: 'help',
                title: `${fm('FileMenuPopover.help')}`,
                subItems: helpItemsMap[type as keyof typeof helpItemsMap],
              }
            : { key: 'help', title: fm('FileMenuPopover.help'), hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'table': {
        return [
          ...commonItems,
          { key: 'convertToMoSheet', title: fm('FileMenuPopover.convertToMoSheet'), noSupport: true },
          { key: 'downToExcel', title: fm('FileMenuPopover.downToExcel'), showDivider: true },
          { key: 'saveTemplate', title: i18nText.saveTemplate }, //保存模板
          { key: 'fileInfo', title: i18n[type] },
          { key: 'tableHelp', title: fm('FileMenuPopover.tableHelp'), noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'presentation': {
        return [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: [
              { key: 'downPPTX', exportType: 'pptx', title: fm('FileMenuPopover.downPPTX') },
              { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
              {
                key: 'downImagePDF',
                exportType: 'imagePdf',
                title: fm('FileMenuPopover.downImagePDF'),
              },
              { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
            ],
          },
          { key: 'print', title: fm('FileMenuPopover.print'), showDivider: true },
          { key: 'saveVersion', title: i18nText.saveVersion },
          { key: 'saveTemplate', title: i18nText.saveTemplate, showDivider: true }, //保存模板
          { key: 'viewHistory', title: fm('FileMenuPopover.viewHistory') },
          { key: 'addComment', title: i18nText.addComment },
          {
            key: 'viewComment',
            title: i18nText.viewComment,
            showDivider: true,
            active: showComment,
            icon: showComment ? <img src={checkOutlined} width={12} /> : null,
          },
          { key: 'fileInfo', title: i18n[type], showDivider: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'form': {
        return [
          ...commonItems,
          { key: 'saveTemplate', title: i18nText.saveTemplate, showDivider: true }, //保存模板
          { key: 'formHelp', title: fm('FileMenuPopover.formHelp'), showDivider: true, noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'board': {
        return [
          ...commonitems_board,
          {
            key: 'fileInfo', // 文档信息
            title: fm('FileMenuPopover.whiteboardInfo'),
          },
          {
            key: 'help',
            title: `${fm('FileMenuPopover.help')}`,
            subItems: helpItemsMap[type as keyof typeof helpItemsMap],
            showDivider: true,
          },
          {
            key: 'delete', // 删除
            title: fm('FileMenuPopover.delete'),
          },
        ];
      }
      case 'mindmap': {
        return [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: [
              { key: 'xmind', exportType: 'xmind', title: fm('FileMenuPopover.xmind') },
              { key: 'downImage', exportType: 'jpg', title: fm('FileMenuPopover.downImage') },
            ],
            showDivider: true,
          },
          { key: 'viewHistory', title: fm('FileMenuPopover.viewHistory'), showDivider: true },
          { key: 'fileInfo', title: i18n[type], showDivider: type === 'mindmap' },
          {
            key: 'help',
            title: `${fm('FileMenuPopover.help')}`,
            subItems: helpItemsMap[type as keyof typeof helpItemsMap],
          },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }

      default:
        return SUPPORT_VIEW_CONFIG;
    }
  }

  const items = getItems(fileType);

  function handleItemClick(item: MenuItem) {
    // 点击触发时同步关闭所有子菜单弹框
    setSubMenuVisible({});
    onItemClick?.({ key: item.key, exportType: item.exportType || '', noSupport: item.noSupport });
  }

  // 控制子菜单显示状态
  function subMenuVisibleChange(key: string, visible: boolean) {
    setSubMenuVisible((prev) => ({
      ...prev,
      [key]: visible,
    }));
  }

  // 通用菜单项渲染函数
  function renderMenuItem(item: MenuItem) {
    // 判断是否应该禁用该菜单项
    const isDisabled = item.disabled;

    // 创建菜单项内容
    const menuItemContent = (
      <Tooltip placement="left" title={isDisabled ? item.disabledTip : ''}>
        <div
          key={item.key}
          className={classNames(css.item, {
            [css.danger]: item.key === 'delete',
            [css.disabled]: isDisabled,
            [css.active]: item.active,
          })}
          onClick={() => !isDisabled && handleItemClick(item)}
        >
          <div className={css.itemContent}>
            <span>{item.title}</span>
            {item.icon && <span className={css.itemIcon}>{item.icon}</span>}
          </div>
          {item.subItems && <img src={arrowRight} width={5.5} />}
        </div>
      </Tooltip>
    );

    // 如果有子菜单，则渲染弹出菜单
    if (item.subItems && item.subItems.length) {
      return (
        <React.Fragment key={item.key}>
          <Popover
            key={item.key}
            arrow={false}
            content={
              <div className={css.submenuContent}>
                {item.subItems.map((subItem) => (
                  <React.Fragment key={subItem.key}>
                    {renderMenuItem({ ...subItem, key: `${item.key}-${subItem.key}` })}
                  </React.Fragment>
                ))}
              </div>
            }
            open={subMenuVisible[item.key]}
            placement="rightTop"
            trigger={isDisabled ? [] : ['hover']}
            onOpenChange={(visible) => subMenuVisibleChange(item.key, visible)}
          >
            {!item.hide && menuItemContent}
          </Popover>
          {!item.hide && item.showDivider && <div className={css.divider} />}
        </React.Fragment>
      );
    }

    // 无子菜单，直接返回菜单项
    return (
      <React.Fragment key={item.key}>
        {!item.hide && menuItemContent}
        {!item.hide && item.showDivider && <div className={css.divider} />}
      </React.Fragment>
    );
  }

  return (
    <div className={css.container}>
      <div className={css.section}>{items.map(renderMenuItem)}</div>
    </div>
  );
}
