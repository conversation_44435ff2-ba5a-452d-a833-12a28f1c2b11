.resultsItem {
  display: flex;
  align-items: flex-start;
  transition: all 0.3s;
  cursor: pointer;
  padding: 8px 16px;
  max-height: 46px;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.img {
  width: 24px;
  height: 24px;
  color: var(--theme-text-color-default);
  margin-top: 2px;
}

.itemIcon {
  display: flex;
  align-items: flex-start;
}

.itemContent {
  margin-left: 8px;
  flex: 1;
  overflow: hidden;
}

.nameTitle {
  font-size: 13px;
  line-height: 1.5;
  color: var(--theme-text-color-default);
  width: 156px;
  font-weight: 500;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  margin-top: 5px;
}

.contentText {
  width: 100%;
  font-size: 12px;
  line-height: 1.5;
  font-weight: 400;
  padding-right: 2px;
  display: inline-block;
  color: var(--theme-text-color-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  margin-top: 2px;
}

.noData {
  font-size: 12px;
  color: var(--theme-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
}
