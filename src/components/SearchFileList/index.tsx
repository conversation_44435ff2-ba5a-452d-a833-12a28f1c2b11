import { history } from 'umi';

import { getFileIcon } from '@/hooks/FileIcon';
import type { SearchListItem } from '@/model/QuickAccess';
import { fm } from '@/modules/Locale';

import styles from './index.less';

interface Props {
  data: SearchListItem[];
}

export const renderHighlightedText = (text: string | undefined) => {
  if (!text) return null;

  // 将文本按照<em>标签分割
  const parts = text.split(/(<em>.*?<\/em>)/g);

  return parts.map((part) => {
    if (part.startsWith('<em>') && part.endsWith('</em>')) {
      // 提取<em>标签中的内容
      const content = part.replace(/<\/?em>/g, '');
      return (
        <span key={part} style={{ color: 'var(--theme-text-color-guidance)', margin: '0 2px', padding: '0 2px' }}>
          {content}
        </span>
      );
    }
    return <span key={part}>{part}</span>;
  });
};

function SearchList(props: Props) {
  const { data = [] } = props;

  return (
    <div>
      {data.map((item) => {
        return (
          <div
            key={item.url}
            className={styles.resultsItem}
            onClick={() => history.push(`${location.origin}${item?.url}`)}
          >
            <div className={styles.itemIcon}>
              <img className={styles.img} src={getFileIcon(item?.source?.type)} />
            </div>
            <div className={styles.itemContent}>
              <div className={styles.nameTitle}>{renderHighlightedText(item.highlight.name) || '-'}</div>
              {item.highlight.content ? (
                <div className={styles.contentText}>{renderHighlightedText(item.highlight.content) || '-'}</div>
              ) : null}
            </div>
          </div>
        );
      })}
      {Array.isArray(data) && data.length === 0 && <span className={styles.noData}> {fm('SearchCenter.noData')} </span>}
    </div>
  );
}

export default SearchList;
