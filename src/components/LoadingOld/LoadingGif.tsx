import React, { memo } from 'react';
import styled from 'styled-components';

import LoadingGifIcon from '../../assets/components/loading/loading.gif';
import { LOADING_ICON_SIZE_MINE } from '../../configs/configs';
import { Image } from '../Image';

const StyledLoadingGifImage = styled(Image)<{ $size: number }>`
  /* stylelint-disable-next-line function-no-unknown */
  width: ${({ $size }) => `${$size}px`};
  /* stylelint-disable-next-line function-no-unknown */
  height: ${({ $size }) => `${$size}px`};
`;

const StyledLoadingGifContainer = styled.div`
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

interface LoadingGifProps {
  size?: number;
}

const DEFAULT_ICON_SIZE = 32;

export const LoadingGif = memo((props?: LoadingGifProps) => {
  const { size = DEFAULT_ICON_SIZE } = props ?? {};
  return <StyledLoadingGifImage $size={size} loading="eager" src={LoadingGifIcon} />;
});

export const TableLoadingGif = memo(() => {
  return (
    <StyledLoadingGifContainer>
      <LoadingGif size={LOADING_ICON_SIZE_MINE} />
    </StyledLoadingGifContainer>
  );
});
