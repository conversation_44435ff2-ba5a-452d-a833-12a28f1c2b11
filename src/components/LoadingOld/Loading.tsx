import React from 'react';

import { StyledLoading, StyledLoadingContainer } from './Loading.style';
import { LoadingGif } from './LoadingGif';

export interface LoadingProps {
  prefix?: string;
  className?: string;
  text?: string;
  hasMarginBottom?: boolean;
}

export const Loading: React.FC<LoadingProps> = (props) => {
  const { prefix = 'sm-loading', className = '', text = '', hasMarginBottom = true } = props;
  return (
    <StyledLoading $hasMarginBottom={hasMarginBottom} className={`${prefix} ${className}`}>
      <span className="loading" />
      {text}
    </StyledLoading>
  );
};

export const LoadingContainer: React.FC = () => {
  return (
    <StyledLoadingContainer>
      <LoadingGif />
    </StyledLoadingContainer>
  );
};
