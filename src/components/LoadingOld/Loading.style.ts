import styled, { keyframes } from 'styled-components';

import IconLoading from '../../assets/components/loading/<EMAIL>';
import { concatImageUrl } from '../../utils/image';

const animationName = keyframes`
  0% {
    transform: rotate(0);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
`;

export const StyledLoading = styled.div<{ $hasMarginBottom: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--theme-basic-color-primary);

  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(${concatImageUrl(IconLoading)}) no-repeat;
    background-size: 20px 20px;
    /* stylelint-disable-next-line function-no-unknown */
    margin-bottom: ${({ $hasMarginBottom }) => ($hasMarginBottom ? '10px' : 'unset')};
    animation: ${animationName} 0.8s infinite linear;
  }
`;

export const StyledLoadingContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
