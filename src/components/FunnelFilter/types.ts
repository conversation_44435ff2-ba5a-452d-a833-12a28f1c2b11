import type { ItemType } from 'antd/es/menu/interface';

import type { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';

export type FilterMenuItem = Omit<ItemType, 'children'> & {
  key?: string;
  groupId?: number | undefined;
  label?: string | React.ReactNode;
  children?: FilterMenuItem[];
  icon?: React.ReactNode | string;
  isTrigger?: boolean;
};

export interface GroupMap {
  set: (key: number, value: FilterMenuItem) => void;
  values: () => IterableIterator<FilterMenuItem>;
  size: number;
  get: (key: number) => FilterMenuItem | undefined;
  delete: (key: number) => boolean;
  clear: () => void;
}

export interface OnChangeParams {
  selectKeys: string[];
  selectRows?: FilterMenuItem[];
}

export interface FilterProps {
  filterConfig: FilterMenuItem[];
  onChange?: (params: OnChangeParams) => void;
  filterIcon?: React.ReactNode;
  defaultValue?: string[];
  showResult?: boolean;
  tip?: string | React.ReactNode;
  selectKeys?: string[];
  defaultValues?: string[];
}

export interface FilterMenuState {
  selectedKeys: string[];
  selectInfo: FilterMenuItem[];
  open: boolean;
}

export type FilterKey = string | FileIconType | undefined;
export interface FilterItem {
  key: FilterKey;
  label: string;
  groupId?: number;
  icon?: React.ReactNode;
  isTrigger?: boolean;
}

export type SortKey = string;
export interface SortItem {
  key: SortKey;
  label: string;
  groupId?: number;
  isTrigger?: boolean;
}

export interface TableItem {
  guid: string;
  name: string;
  type: FileIconType | string;
  size?: number;
  createdAt: string | number;
  updatedAt: string | number;
  sharedAt: number;
  fileSize: number;
  shareMode?: string;
  role?: string;
  user: {
    name: string;
    id?: number;
    avatar?: string;
  };
  lastUsedAt: string;
  isFolder?: boolean;
  starred?: boolean;
  lastAction?: string;
  isSpace?: boolean;
  isShortcut?: boolean;
  shortcutSource?: TableItem;
  isCloudFile?: boolean;
}

export interface UseTableFilterSortReturn {
  tableData: TableItem[];
}

export interface TableFilterSortParams {
  data: TableItem[];
  filterKeys?: FilterKey[];
  sortKeys?: SortKey[];
  isSort?: boolean;
}
