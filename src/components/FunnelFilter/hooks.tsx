import { UndoOutlined } from '@ant-design/icons';
import { useCallback, useMemo } from 'react';
import { useLocation } from 'umi';

import { fm } from '@/modules/Locale';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
import { useMeStore } from '@/store/Me';
import { getFileType } from '@/utils/file';

import type { FilterKey, SortKey, TableFilterSortParams, TableItem, UseTableFilterSortReturn } from './types';

export const useGetDefaultConfig = () => {
  const location = useLocation();
  const pathName = location.pathname;
  const path = pathName.split('/')[1];
  const {
    clearFilter,
    all,
    document,
    modoc,
    mosheet,
    presentation,
    table,
    mindMap,
    form,
    board,
    folder,
    upload,
    sortByNameAZ,
    sortByNameZA,
    sortByNewest,
    sortByOldest,
    sortByUpdateTime,
    sortByLargest,
    sortBySmallest,
    sortFolderTop,
    space,
    recentlyEdit,
    recentlyOpened,
    anyCreator,
    myCreated,
  } = {
    clearFilter: fm('File.clearFilter'),
    all: fm('File.all'),
    document: fm('File.document'),
    modoc: fm('File.modoc'),
    mosheet: fm('File.mosheet'),
    presentation: fm('File.presentation'),
    table: fm('File.table'),
    mindMap: fm('File.mindMap'),
    form: fm('File.form'),
    board: fm('File.board'),
    folder: fm('File.folder'),
    upload: fm('File.upload'),
    sortByNameAZ: fm('File.sortByNameAZ'),
    sortByNameZA: fm('File.sortByNameZA'),
    sortByNewest: fm('File.sortByNewest'),
    sortByOldest: fm('File.sortByOldest'),
    sortByUpdateTime: fm('File.sortByUpdateTime'),
    sortByLargest: fm('File.sortByLargest'),
    sortBySmallest: fm('File.sortBySmallest'),
    sortFolderTop: fm('File.sortFolderTop'),
    space: fm('FilePathPicker.space'),
    recentlyOpened: fm('File.recentlyOpened'),
    recentlyEdit: fm('File.recentlyEdit'),
    anyCreator: fm('File.anyCreator'),
    myCreated: fm('File.myCreated'),
  };

  const DEFAULT_FILTER_CONFIG_RECENT = [
    { type: 'divider' },
    { label: anyCreator, key: 'anyCreator', groupId: 2 },
    { label: myCreated, key: 'myCreated', groupId: 2 },
    { type: 'divider' },
    { label: recentlyOpened, key: 'open', groupId: 3 },
    { label: recentlyEdit, key: 'edit', groupId: 3 },
  ];

  const DEFAULT_FILTER_CONFIG_SHARE = [{ label: space, key: FileIconType.Space, groupId: 1 }];

  const DEFAULT_FILTER_CONFIG = [
    { label: clearFilter, key: 'clear', icon: <UndoOutlined /> },
    { type: 'divider' },
    { label: all, key: 'all', groupId: 1 },
    { label: document, key: FileIconType.Doc, groupId: 1 },
    { label: modoc, key: FileIconType.Modoc, groupId: 1 },
    { label: mosheet, key: FileIconType.Mosheet, groupId: 1 },
    { label: presentation, key: FileIconType.Presentation, groupId: 1 },
    { label: table, key: FileIconType.Table, groupId: 1 },
    { label: mindMap, key: FileIconType.Mindmap, groupId: 1 },
    { label: form, key: FileIconType.Form, groupId: 1 },
    { label: board, key: FileIconType.Board, groupId: 1 },
    ...(path !== 'recent' ? [{ label: folder, key: FileIconType.Folder, groupId: 1 }] : []),
    { label: upload, key: 'isCloudFile', groupId: 1 },
    ...(path === 'recent' ? DEFAULT_FILTER_CONFIG_RECENT : []),
    ...(path === 'share' ? DEFAULT_FILTER_CONFIG_SHARE : []),
  ];

  const DEFAULT_FILTER_SORTER_CONFIG = [
    { label: sortByNameAZ, key: 'sortByNameAZ', groupId: 1 },
    { label: sortByNameZA, key: 'sortByNameZA', groupId: 1 },
    ...(!['favorites'].includes(path)
      ? [
          { label: sortByNewest, key: 'sortByNewest', groupId: 1 },
          { label: sortByOldest, key: 'sortByOldest', groupId: 1 },
          { label: sortByLargest, key: 'sortByLargest', groupId: 1 },
          { label: sortBySmallest, key: 'sortBySmallest', groupId: 1 },
        ]
      : []),

    { label: sortByUpdateTime, key: 'sortByUpdateTime', groupId: 1 },
    { type: 'divider' },
    { label: sortFolderTop, key: 'sortFolderTop', groupId: 2, isTrigger: true },
  ];
  return {
    DEFAULT_FILTER_CONFIG,
    DEFAULT_FILTER_SORTER_CONFIG,
  };
};

/**
 * 表格筛选排序通用 Hook
 */
export const useTableFilterSort = ({
  data,
  filterKeys = [],
  sortKeys = [],
  isSort = true, // 是否可以排序
}: TableFilterSortParams): UseTableFilterSortReturn => {
  const location = useLocation();
  const pathName = location.pathname;
  const path = pathName.split('/')[1];
  const userInfo = useMeStore((state) => state.me);

  const filterFn = useCallback(
    (item: TableItem) => {
      // 提取通用判断函数，减少重复代码
      const isInFilter = (key: string) => filterKeys.includes(key);

      // 语义化命名：判断是否为云文件
      const isCloudFile = item?.isCloudFile;

      // 计算文件类型（非云文件时）
      const fileType = !isCloudFile ? getFileType({ type: item.type, isSpace: item?.isSpace }) : '';

      // 最终判断逻辑：云文件直接匹配'isCloudFile'，非云文件匹配类型或'all'
      const isType = isInFilter('isCloudFile') ? isCloudFile : isInFilter(fileType) || isInFilter('all');
      const isCreator = filterKeys.includes('anyCreator') || item?.user?.name === userInfo?.name;
      const isOpen = filterKeys.includes('open') || item.lastAction === 'edit';
      switch (path) {
        case 'recent':
          return isType && isCreator && isOpen;
        default:
          return isType;
      }
    },
    [filterKeys, path, userInfo?.name],
  );

  const filterTableData = useCallback(
    (data: TableItem[], filterKeys: FilterKey[]): TableItem[] => {
      if (filterKeys.length === 0 || (filterKeys.includes('all') && !['recent'].includes(path))) {
        return data;
      }
      return data.filter(filterFn);
    },
    [filterFn, path],
  );

  const sortTableData = useCallback(
    (data: TableItem[], sortKeys: SortKey[] = []): TableItem[] => {
      if (!isSort || !data.length || !sortKeys.length) {
        return [...data];
      }

      const validSortKeys = [...new Set(sortKeys)].filter((key) => key !== 'sortFolderTop') as Exclude<
        SortKey,
        'sortFolderTop'
      >[];

      const shouldSortFolderTop = sortKeys.includes('sortFolderTop');

      const isFolder = (item: TableItem): boolean => {
        return item.isSpace || (item.isShortcut ? item.shortcutSource?.type === 'folder' : item.type === 'folder');
      };

      // 字符类型判断（严格固定四大类顺序：符号0→数字1→字母2→汉字3）
      const getCharType = (char: string): 0 | 1 | 2 | 3 => {
        if (!char) return 0;
        const code = char.charCodeAt(0);

        // 全角转半角（避免全角字符被误判）
        const isFullWidth = code >= 0xff01 && code <= 0xff5e;
        const halfWidthCode = isFullWidth ? code - 0xfee0 : code;

        // 1. 符号类型（非数字、字母、汉字的字符）
        const isSymbol =
          (halfWidthCode >= 33 && halfWidthCode <= 47) ||
          (halfWidthCode >= 58 && halfWidthCode <= 64) ||
          (halfWidthCode >= 91 && halfWidthCode <= 96) ||
          (halfWidthCode >= 123 && halfWidthCode <= 126) ||
          !(
            (halfWidthCode >= 48 && halfWidthCode <= 57) || // 数字
            (halfWidthCode >= 65 && halfWidthCode <= 90) || // 大写字母
            (halfWidthCode >= 97 && halfWidthCode <= 122) || // 小写字母
            (code >= 0x4e00 && code <= 0x9fff) || // 基本汉字
            (code >= 0x3400 && code <= 0x4dbf) // 扩展汉字
          );
        if (isSymbol) return 0;

        // 2. 数字类型（半角0-9 或 全角０-９）
        if ((halfWidthCode >= 48 && halfWidthCode <= 57) || (code >= 0xff10 && code <= 0xff19)) return 1;

        // 3. 字母类型（半角A-Z/a-z 或 全角Ａ-Ｚ/ａ-ｚ）
        if (
          (halfWidthCode >= 65 && halfWidthCode <= 90) ||
          (halfWidthCode >= 97 && halfWidthCode <= 122) ||
          (code >= 0xff21 && code <= 0xff3a) ||
          (code >= 0xff41 && code <= 0xff5a)
        )
          return 2;

        // 4. 汉字类型
        if ((code >= 0x4e00 && code <= 0x9fff) || (code >= 0x3400 && code <= 0x4dbf)) return 3;

        return 0; // 其他字符归为符号
      };

      // 提取连续数字序列（支持全角数字）
      const extractNumberSequence = (str: string, start: number): string => {
        let result = '';
        for (let i = start; i < str.length; i++) {
          const char = str[i];
          if (getCharType(char) === 1) {
            // 仅数字类型
            const code = char.charCodeAt(0);
            // 全角数字转半角
            result += code >= 0xff10 && code <= 0xff19 ? String.fromCharCode(code - 0xfee0) : char;
          } else {
            break;
          }
        }
        return result;
      };

      /**
       * 文件名比较函数
       * @param a 文件名a
       * @param b 文件名b
       * @param isDesc 是否内部降序（不影响大类顺序）
       */
      const compareFileName = (a: string, b: string, isDesc: boolean): number => {
        let i = 0;
        const lenA = a.length;
        const lenB = b.length;

        while (i < lenA && i < lenB) {
          const charA = a[i];
          const charB = b[i];
          const typeA = getCharType(charA);
          const typeB = getCharType(charB);

          // 1. 大类比较：始终保持 符号→数字→字母→汉字（固定不变）
          if (typeA !== typeB) {
            return typeA - typeB; // 0<1<2<3，确保大类顺序不变
          }

          // 2. 同类型内部比较（根据isDesc决定升序/降序）
          let sameTypeResult = 0;
          switch (typeA) {
            case 0: // 符号：按编码
              sameTypeResult = charA.charCodeAt(0) - charB.charCodeAt(0);
              break;
            case 1: {
              // 数字：按位比较
              const numStrA = extractNumberSequence(a, i);
              const numStrB = extractNumberSequence(b, i);
              const minLen = Math.min(numStrA.length, numStrB.length);

              // 逐位比较
              for (let j = 0; j < minLen; j++) {
                const digitA = parseInt(numStrA[j], 10);
                const digitB = parseInt(numStrB[j], 10);
                if (digitA !== digitB) {
                  sameTypeResult = digitA - digitB;
                  break;
                }
              }

              // 长度不同时，短的在前（正序）
              if (sameTypeResult === 0) {
                sameTypeResult = numStrA.length - numStrB.length;
              }

              // 跳过已比较的数字位数
              i += Math.max(numStrA.length, numStrB.length) - 1;
              break;
            }
            case 2: // 字母：不区分大小写
              sameTypeResult = charA.toLowerCase().localeCompare(charB.toLowerCase());
              break;
            case 3: // 汉字：按拼音
              sameTypeResult = charA.localeCompare(charB, 'zh-CN', {
                sensitivity: 'accent',
                numeric: true,
              });
              break;
          }

          // 根据是否降序，反转同类型内部的比较结果
          if (sameTypeResult !== 0) {
            return isDesc ? -sameTypeResult : sameTypeResult;
          }

          i++;
        }

        // 长度不同时：短的在前（正序），长的在前（倒序）
        const lenResult = lenA - lenB;
        return isDesc ? -lenResult : lenResult;
      };

      const compareItems = (a: TableItem, b: TableItem): number => {
        for (const key of validSortKeys) {
          switch (key) {
            case 'sortByUpdateTime':
              return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
            case 'sortByNameAZ': // 正序：大类顺序不变，类内升序
              return compareFileName(a.name, b.name, false);
            case 'sortByNameZA': // 倒序：大类顺序不变，类内降序
              return compareFileName(a.name, b.name, true);
            case 'sortByNewest':
              return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            case 'sortByOldest':
              return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
            case 'sortByLargest':
              return a.fileSize - b.fileSize;
            case 'sortBySmallest':
              return b.fileSize - a.fileSize;
            default:
              return 0;
          }
        }
        return 0;
      };

      if (shouldSortFolderTop) {
        const folders = data.filter(isFolder).sort(compareItems);
        const files = data.filter((item) => !isFolder(item)).sort(compareItems);
        return [...folders, ...files];
      } else {
        return [...data].sort(compareItems);
      }
    },
    [isSort],
  );

  const filteredData = useMemo(() => filterTableData(data, filterKeys), [data, filterKeys, filterTableData]);

  const sortedData = useMemo(() => sortTableData(filteredData, sortKeys), [filteredData, sortKeys, sortTableData]);

  return {
    tableData: sortedData,
  };
};
export default useTableFilterSort;
