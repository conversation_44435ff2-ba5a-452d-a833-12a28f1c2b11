import { Input, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';

import { searchFile } from '@/api/File';
import { ReactComponent as ClearIcon } from '@/assets/images/svg/close-outline.svg';
import { ReactComponent as DesktopIcon } from '@/assets/images/svg/desktop.svg';
import { ReactComponent as SearchIcon } from '@/assets/images/svg/find-outline.svg';
import { ReactComponent as WorkbenchIcon } from '@/assets/images/svg/workbench.svg';
import { getFileIcon } from '@/hooks/FileIcon';
import type { QuickAccessItem, SearchListItem } from '@/model/QuickAccess';
import { fm } from '@/modules/Locale';
import type { FileDetail } from '@/types/api';

import SearchFileList from '../SearchFileList';
import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; fileType?: string; fileGuid?: string }) => void;
  recentFiles?: FileDetail[];
  quickAccessList?: QuickAccessItem[];
}

function ShowChildren({ children, visible }: { children: React.ReactNode; visible: boolean }) {
  return visible ? children : null;
}

export function BackToPopover({ onItemClick, recentFiles = [], quickAccessList = [] }: PopoverProps) {
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState<SearchListItem[]>([]);

  const resetSearch = () => {
    setSearchValue('');
    setSearchResults([]);
  };

  function handleItemClick(item: { key: string; fileType?: string; fileGuid?: string }) {
    onItemClick?.(item);
    resetSearch();
  }

  function renderRecentFile(file: FileDetail) {
    return (
      <div
        key={file.guid}
        className={css.item}
        onClick={() => handleItemClick({ key: 'recent', fileType: file.type, fileGuid: file.guid })}
      >
        <img className={css.icon} src={getFileIcon(file.subType || file.type)} />
        <Tooltip placement="top" title={file.name}>
          <span>{file.name}</span>
        </Tooltip>
      </div>
    );
  }

  function renderQuickAccessItem(item: QuickAccessItem) {
    return (
      <div
        key={item.guid}
        className={css.item}
        onClick={() => handleItemClick({ key: 'quickAccess', fileType: item.type, fileGuid: item.guid })}
      >
        <img className={css.icon} src={getFileIcon(item.type)} />
        <Tooltip placement="top" title={item.label}>
          <span>{item.label}</span>
        </Tooltip>
      </div>
    );
  }

  const debouncedSearch = useRef(
    debounce(async (value: string) => {
      if (value) {
        try {
          const result = await searchFile({ keyword: value, size: 12 });
          setSearchResults(result?.data?.results || []);
        } catch (error) {
          setSearchResults([]);
        }
      } else {
        setSearchResults([]);
      }
    }, 300),
  ).current;

  // 当搜索值变化时，请求文件列表
  useEffect(() => {
    debouncedSearch(searchValue);

    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  return (
    <div className={css.container}>
      <div className={css.section}>
        <Input
          allowClear={{ clearIcon: <ClearIcon /> }}
          placeholder={fm('BackToPopover.searchFiles')}
          prefix={<SearchIcon />}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
        />
      </div>
      <div className={css.divider} />
      <div style={{ overflowY: 'auto', maxHeight: 475 }}>
        {/* 搜索组件添加  */}
        <ShowChildren visible={!!searchValue}>
          <SearchFileList data={searchResults} />
        </ShowChildren>

        <ShowChildren visible={!searchValue}>
          <div className={css.section}>
            <div className={css.sectionTitle}>{fm('BackToPopover.backTo')}</div>
            <div className={css.item} onClick={() => handleItemClick({ key: 'myDesktop' })}>
              <DesktopIcon className={css.icon} />
              <span>{fm('BackToPopover.myDesktop')}</span>
            </div>
            <div className={css.item} onClick={() => handleItemClick({ key: 'workbench' })}>
              <WorkbenchIcon className={css.icon} />
              <span>{fm('BackToPopover.workbench')}</span>
            </div>
          </div>

          <div className={css.section}>
            <div className={css.sectionTitle}>{fm('BackToPopover.recentlyUsed')}</div>
            {recentFiles.map((file) => renderRecentFile(file))}
          </div>

          {/* 快速访问 */}
          <div className={css.section}>
            <div className={css.sectionTitle}>{fm('BackToPopover.quickAccess')}</div>
          </div>
          <div className={css.section}>{quickAccessList.map((item) => renderQuickAccessItem(item))}</div>
        </ShowChildren>
      </div>
    </div>
  );
}
