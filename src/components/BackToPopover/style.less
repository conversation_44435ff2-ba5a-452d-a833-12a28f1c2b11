.container {
  padding: 8px 0;
  background-color: var(--theme-menu-color-bg-default);
  width: 220px;
  max-height: 526px;
  box-sizing: border-box;

  .section {
    &:first-child {
      :global(.ant-input-affix-wrapper) {
        border: none;
        border-radius: 4px;

        &:hover,
        &:focus,
        &-focused {
          box-shadow: none;
          border: none;
        }
      }
    }

    .sectionTitle {
      margin-top: 9px;
      padding: 4px 16px;
      color: var(--theme-text-color-secondary);
      font-size: 10px;
      font-weight: 400;
    }

    .item {
      display: flex;
      align-items: center;
      transition: all 0.3s;
      cursor: pointer;
      border-radius: 4px;
      padding: 0 16px;
      height: 36px;

      &:hover {
        background-color: var(--theme-menu-color-bg-hover) !important;
      }

      .icon {
        width: 18px;
        color: var(--theme-text-color-default);
      }

      span {
        margin-left: 8px;
        color: var(--theme-text-color-default);
        font-size: 12px;
        line-height: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 126px;
      }
    }
  }

  .divider {
    margin: 4px 15px;
    border-top: 1px solid var(--theme-separator-color-lighter);
    background: transparent;
    height: 1px;
  }
}

.resultsItem {
  min-height: unset;
  padding: 5px 20px 6px 25px;
}
