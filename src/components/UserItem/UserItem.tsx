import { Tooltip } from 'antd';
import React from 'react';

import { StyledAvatar, StyledName, StyledUserItem } from './UserItem.styled';

interface UserItemProps {
  name: string;
  avatar: string;
}

export const UserItem = (props: UserItemProps) => {
  const { name, avatar } = props;

  return (
    <StyledUserItem>
      <StyledAvatar size={28} src={avatar} />
      <Tooltip
        // overlayStyle={{ fontSize: 13 }}
        placement="topLeft"
        title={name}
      >
        <StyledName>{name}</StyledName>
      </Tooltip>
    </StyledUserItem>
  );
};
