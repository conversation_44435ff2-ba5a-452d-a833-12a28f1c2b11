import type { AxiosResponse } from 'axios';
import type { PropsWithChildren } from 'react';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { history, useLocation, useParams } from 'umi';

import { fileDetail } from '@/api/File';
import { to } from '@/api/Request';
import type { EditorHeaderRef } from '@/components/EditorHeader';
import { EditorHeader } from '@/components/EditorHeader';
import ErrorPageRenderer from '@/components/FileErrorPage';
import PasswordInput from '@/components/PasswordInput/PasswordInput';
import H5ErrorPageRenderer from '@/components-mobile/H5FileErrorPage/H5ErrorPageRenderer';
import { MobileEditorHeader } from '@/components-mobile/MobileEditorHeader';
import { FileSettingContext } from '@/contexts/FileSetting';
import useShortcutKeys from '@/hooks/useShortcutKeys';
import { editorStore } from '@/store/Editor';
import { useMeStore } from '@/store/Me';
import type { ErrorResponse, FileDetail } from '@/types/api';
import { isMac } from '@/utils/browser';
import { getSMEditor } from '@/utils/ShimoSDK';
import { isFillForm, isFormResponseSharePath } from '@/utils/url';

import styles from './index.less';

type DetailState = { ok: true; data?: FileDetail } | { ok: false; data?: ErrorResponse };

export default function EditLayoutContent(props: PropsWithChildren<{ style?: React.CSSProperties }>) {
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || '';
  const [detail, setDetail] = useState<DetailState>();
  const [loading, setLoading] = useState(true);
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [shareUserName, setShareUserName] = useState<string>('');
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter((seg) => seg);
  const fileGuid = pathSegments[1] || '';
  const fileType = pathSegments[0] || '';

  const meId = useMeStore((state) => state.me.id);
  const isMobilePlatform = process.env.PLATFORM === 'mobile';
  const { refreshFileList, mainDivRef } = useContext(FileSettingContext);
  const isOnMac = isMac();

  const editorHeaderRef = useRef<EditorHeaderRef>(null);

  // 查看文件详情
  const viewFileDetail = useCallback(async () => {
    setLoading(true);
    const [err, res] = await to<AxiosResponse<FileDetail>, AxiosResponse<ErrorResponse>>(fileDetail(guid));
    setLoading(false);
    if (res?.status === 200) {
      const fileData = res.data;
      if (fileData.passwordProtected && !fileData.permissions.readable) {
        setShareUserName(fileData.user.name);
        setShowPasswordInput(true);
      } else {
        if (pathSegments[2] && pathSegments[2] !== 'fill-form') {
          history.replace(`/${fileType}/${fileGuid}`);
          window.location.reload();
        }
      }

      editorStore.getState().update({ fileType: res.data.type });

      setDetail({
        ok: true,
        data: fileData,
      });
    } else if (isFillForm() || isFormResponseSharePath()) {
      // 表单填写页和表单分享回复页不对文件权限判断
      setDetail({
        ok: true,
      });
    } else {
      setDetail({
        ok: false,
        data: err?.data,
      });
    }
  }, [guid, refreshFileList]);

  // 初始化
  useEffect(() => {
    viewFileDetail();
  }, [viewFileDetail, refreshFileList]);

  const handleDemoMode = () => {
    getSMEditor()?.startDemonstration?.();
  };
  const handleOpenDesktopInNewTab = () => {
    window.open('/desktop', '_blank');
  };
  const handleToggleShortcutPanel = () => {
    editorHeaderRef.current?.toggleShortcutPanel();
  };

  useShortcutKeys([
    // 演示模式
    ...(isOnMac
      ? [{ keys: ['command', 'shift', 'p'], handler: handleDemoMode }]
      : [{ keys: ['ctrl', 'shift', 'p'], handler: handleDemoMode }]),
    // 新标签打开石墨桌面
    ...(isOnMac
      ? [{ keys: ['command', 'shift', 'e'], handler: handleOpenDesktopInNewTab }]
      : [{ keys: ['ctrl', 'shift', 'e'], handler: handleOpenDesktopInNewTab }]),
    // 打开/关闭 快捷键面板
    ...(isOnMac
      ? [{ keys: ['command', '/'], handler: handleToggleShortcutPanel }]
      : [{ keys: ['ctrl', '/'], handler: handleToggleShortcutPanel }]),
  ]);

  return (
    <div ref={mainDivRef} className={styles.myLayout}>
      {loading ? null : detail?.ok ? (
        showPasswordInput ? (
          <PasswordInput
            autoFillPassword={false}
            fileGuid={fileGuid}
            fileType={fileType}
            pathname={location.pathname}
            shareUserName={shareUserName}
          />
        ) : isMobilePlatform ? (
          <MobileEditorHeader data={detail.data} />
        ) : (
          <EditorHeader data={detail.data} />
        )
      ) : isMobilePlatform ? (
        <H5ErrorPageRenderer errorCode={detail?.data?.code} fileGuid={fileGuid} isLogin={!!(meId && meId > 0)} />
      ) : (
        <ErrorPageRenderer errorCode={detail?.data?.code} fileGuid={fileGuid} isLogin={!!(meId && meId > 0)} />
      )}
      {(!detail || detail?.ok) && (
        <div className={styles.content} id="editor-content">
          {props.children}
        </div>
      )}
    </div>
  );
}
