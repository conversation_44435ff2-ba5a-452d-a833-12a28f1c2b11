/**
 * 文件夹管理系统 - 提供文件和文件夹的管理功能
 * 负责处理文件夹浏览、导航和状态管理的核心逻辑
 */

/**
 * 文件项接口 - 定义基本文件项属性
 */
export interface FileItem {
  /** 文件唯一标识符 */
  id: string;
  /** 文件名称 */
  name: string;
  /** 文件类型：doc, ppt, word等 */
  type: string;
  isPin?: boolean;
}

/**
 * 文件夹项接口 - 继承自FileItem，增加了children属性
 */
export interface FolderItem extends FileItem {
  /** 子文件和文件夹列表 */
  children?: (FolderItem | FileItem)[];
  /** 来源菜单ID */
  sourceMenuId: string;
  /** 协作权限 */
  role?: string;
  /** 是否是子文件夹 */
  isChild?: boolean;
}

/**
 * 位置选项接口 - 用于下拉选择框
 */
export interface LocationOption {
  /** 选项值 */
  value: string;
  /** 显示文本 */
  label: string;
  /** 来源菜单ID */
  sourceMenuId?: string;
}

/**
 * 视图模式类型 - list(列表视图) 或 grid(网格视图)
 */
export type ViewMode = 'list' | 'grid';

/**
 * 文件夹管理系统状态接口 - 定义系统所有状态
 */
export interface FolderManagerState {
  viewMode: ViewMode; // 视图模式 列表或网格
  expandedFolders: Record<string, boolean>; // 已展开的文件夹ID记录
  curGridFolder: string | null; // 当前在网格视图中选中的文件夹ID
  browseHistory: string[]; // 浏览历史记录，存储访问过的文件夹ID
  historyIndex: number; // 当前在历史记录中的位置索引
  folders: FolderItem[]; // 所有文件夹列表
  files: FileItem[]; // 所有文件列表
  folderMap?: Record<string, FolderMapItem>; // 文件夹映射表
  currentSideMenuId: string; // 当前侧边栏菜单的ID
  curFolder: FolderItem | null; // 当前选中的文件夹
}

/**
 * 文件夹事件处理器接口 - 定义与UI交互的回调函数
 */
export interface FolderEventHandlers {
  /** 位置变更时的回调函数 */
  onLocationChange: (locationGuid: string) => void;
}

/**
 * 文件夹映射项接口 - 用于folderMap结构
 */
export interface FolderMapItem {
  folders: FolderItem[];
  files: FileItem[];
}

interface FolderManagerProps {
  eventHandlers: FolderEventHandlers;
  folders?: FolderItem[];
  files?: FileItem[];
  currentSideMenuId?: string; // 添加可选的当前侧边栏菜单ID
}

/**
 * 历史记录项接口 - 定义历史记录的结构
 */
interface HistoryItem {
  folderId: string | 'root';
  folderName: string;
  sideMenuId: string;
}

/**
 * 文件夹管理器类 - 负责管理文件夹系统的所有状态和操作
 */
export class FolderManager {
  /** 内部状态 */
  private state: FolderManagerState;
  /** 事件处理器 */
  private eventHandlers: FolderEventHandlers;

  private folderMap: Record<string, FolderMapItem | null> = {
    desktop: null,
    space: null,
    used: null,
    shared: null,
    favorites: null,
  };

  /**
   * 初始化文件夹管理器
   * @param eventHandlers UI交互的事件处理器
   */
  constructor({ eventHandlers, folders = [], files = [], currentSideMenuId = 'desktop' }: FolderManagerProps) {
    this.eventHandlers = eventHandlers;
    // 初始化状态对象，设置默认值
    this.state = {
      viewMode: 'list', // 默认为列表视图
      expandedFolders: {}, // 默认没有展开的文件夹
      curGridFolder: null, // 默认没有选中的网格文件夹
      curFolder: null, // 默认选中的文件夹
      browseHistory: ['root'], // 历史从根目录开始 - 将在下面替换为新格式
      historyIndex: 0, // 初始历史索引
      folders, // 使用模拟数据
      files, // 使用模拟数据
      currentSideMenuId, // 使用传入的侧边栏菜单ID或默认值
    };

    // 使用新的历史记录格式
    this.state.browseHistory = [JSON.stringify({ folderId: 'root', sideMenuId: currentSideMenuId })];
  }

  /**
   * 获取完整状态 - 返回当前状态的副本
   * @returns 当前状态的副本
   */
  getState(): FolderManagerState {
    // 返回状态副本，避免外部直接修改内部状态
    return { ...this.state };
  }

  /**
   * 更新视图模式 - 设置列表或网格视图
   * @param mode 新的视图模式
   */
  setViewMode(mode: ViewMode): void {
    this.state.viewMode = mode;
    // 切换视图时重置当前选中的文件夹
    if (mode === 'grid') {
      this.state.curGridFolder = null;
    }
  }

  setState(folders: FolderItem[], files: FileItem[]): void {
    this.state.curGridFolder = null;
    this.state.curFolder = null;
    this.state.expandedFolders = {};
    this.state.folders = [...folders];
    this.state.files = [...files];
  }

  setFolders(folders: FolderItem[]): void {
    this.state.folders = [...folders];
  }

  setFolderMapByKey(key: string, folderMap: FolderMapItem): void {
    this.folderMap[key] = folderMap;
  }

  getFolderMapByKey(key: string): FolderMapItem | null {
    return this.folderMap[key] || null;
  }

  /**
   * 查找特定ID的文件夹 - 递归搜索文件夹树
   * @param id 要查找的文件夹ID
   * @param folderList 要搜索的文件夹列表，默认为所有文件夹
   * @returns 找到的文件夹对象，未找到则返回null
   */
  findFolderById(id: string, folderList = this.state.folders): FolderItem | null {
    for (const folder of folderList) {
      // 如果找到匹配的ID，直接返回该文件夹
      if (folder.id === id) {
        return folder;
      }
      // 如果有子文件夹，递归搜索
      if (folder.children && folder.children.length > 0) {
        const childFolders = folder.children.filter((child): child is FolderItem => child.type === 'folder');
        const found = this.findFolderById(id, childFolders);
        if (found) return found;
      }
    }
    return null; // 未找到
  }

  /**
   * 查找文件夹的父文件夹 - 递归搜索文件夹的父级
   * @param folderId 要查找父级的文件夹ID
   * @param folderList 要搜索的文件夹列表，默认为所有文件夹
   * @param parentId 当前搜索层级的父ID
   * @returns 父文件夹ID，未找到则返回null
   */
  findParentFolder(folderId: string, folderList = this.state.folders, parentId: string | null = null): string | null {
    for (const folder of folderList) {
      // 如果在当前层级找到了目标文件夹，返回传入的父ID
      if (folder.id === folderId) {
        return parentId; // 找到了文件夹，返回父ID
      }
      // 如果有子文件夹，进一步检查
      if (folder.children && folder.children.length > 0) {
        const childFolders = folder.children.filter((child): child is FolderItem => child.type === 'folder');
        // 检查直接子文件夹
        for (const childFolder of childFolders) {
          if (childFolder.id === folderId) {
            return folder.id; // 在子文件夹中找到了，返回当前文件夹ID
          }
          // 递归搜索更深层级
          const found = this.findParentFolder(folderId, [childFolder], folder.id);
          if (found !== null) {
            return found;
          }
        }
      }
    }
    return null; // 未找到
  }

  /**
   * 添加文件夹到浏览历史 - 记录浏览记录并处理历史截断
   * @param folderId 要添加的文件夹ID，null表示根目录
   */
  addToHistory(folder: FolderItem | null): void {
    const state = this.state;
    // 将文件夹ID或'root'添加到历史
    const historyItem: HistoryItem = {
      folderId: folder?.id || 'root',
      folderName: folder?.name || 'root',
      sideMenuId: state.currentSideMenuId,
    };

    const historyItemStr = JSON.stringify(historyItem);

    // 如果在历史中间点添加，需要截断后面的历史记录
    if (state.historyIndex < state.browseHistory.length - 1) {
      // 保留当前位置及之前的历史，加上新项目
      const newHistory = [...state.browseHistory.slice(0, state.historyIndex + 1), historyItemStr];
      state.browseHistory = newHistory;
      state.historyIndex = newHistory.length - 1;
    } else {
      // 在历史末尾添加新项目
      state.browseHistory = [...state.browseHistory, historyItemStr];
      state.historyIndex = state.browseHistory.length - 1;
    }
  }

  /**
   * 切换文件夹展开/收起状态 - 点击三角图标时触发
   * @param folderId 要切换的文件夹ID
   */
  toggleFolder(folderId: string): void {
    // 更新展开状态，使用新对象保证状态更新
    this.state.expandedFolders = {
      ...this.state.expandedFolders,
      [folderId]: !this.state.expandedFolders[folderId], // 取反当前状态
    };
  }

  /**
   * 进入文件夹 - 切换到指定文件夹并更新相关状态
   * @param folder 要进入的文件夹
   */
  enterFolder(folder: FolderItem): void {
    // 更新location
    this.eventHandlers.onLocationChange(folder.id);
    // 设置当前浏览的文件夹
    this.state.curFolder = folder;

    // 添加到浏览历史
    this.addToHistory(folder);
  }

  /**
   * 处理网格视图中项目的点击 - 专用于网格视图交互
   * @param item 被点击的文件或文件夹
   */
  handleGridItemClick(item: FileItem | FolderItem): void {
    // 只处理文件夹类型的点击
    if (item.type === 'folder' && 'children' in item) {
      // 更新网格视图中选中的文件夹
      this.state.curGridFolder = item.id;
      // 设置当前浏览的文件夹
      this.state.curFolder = item;
      // 通知UI更新位置显示
      this.eventHandlers.onLocationChange(item.id);

      // 添加到浏览历史
      this.addToHistory(item);
    }
  }

  /**
   * 处理位置选择变更 - 响应Select组件选择变化
   * @param value 选择的位置值
   * @param curLocationOptions 当前可用的位置选项
   */
  handleLocationChange(value: string, curLocationOptions: LocationOption[]): void {
    // 检查是否选择的是当前已选中的文件夹
    if (curLocationOptions.find((opt) => opt.value === value)?.value === this.state.curGridFolder) {
      // 如果选择的是当前文件夹，不需要操作
      return;
    }

    // 如果选择的是其他位置，更新location
    this.eventHandlers.onLocationChange(value);

    // 并且清除当前选中的文件夹
    this.state.curGridFolder = null;
    this.state.curFolder = null;

    // 更新浏览历史
    this.addToHistory(null); // 添加根目录到历史

    // 如果是已知的文件夹ID，更新当前文件夹ID
    const folder = this.findFolderById(value);
    if (folder) {
      this.state.curFolder = folder;

      // 添加到浏览历史
      this.addToHistory(folder);
    }
  }

  /**
   * 导航到历史记录中的指定位置
   * @param targetIndex 目标历史索引
   * @param locationChange 位置变更的回调函数
   * @private
   */
  private navToHistoryIndex(targetIndex: number, locationChange?: (menuId: string) => void): void {
    const targetItemStr = this.state.browseHistory[targetIndex];
    this.state.historyIndex = targetIndex;

    // 解析历史记录项
    const targetItem = JSON.parse(targetItemStr) as HistoryItem;
    const folderId = targetItem.folderId;

    // 更新当前浏览文件夹
    if (folderId === 'root') {
      // 返回到根目录
      this.state.curGridFolder = null;
      this.state.curFolder = null;
      this.eventHandlers.onLocationChange(this.state.currentSideMenuId); // 默认位置
    } else {
      // 更新location显示
      const folder = this.findFolderById(folderId);
      if (folder) {
        // 通知UI更新位置显示
        this.eventHandlers.onLocationChange(folder.id);

        if (this.state.viewMode === 'grid') {
          // 网格视图下，更新选中的文件夹
          this.state.curGridFolder = folderId;
          return;
        }
        this.state.curFolder = folder;
        // 列表视图下，找到父文件夹并展开，以显示当前文件夹
        const parentId = this.findParentFolder(folderId);
        if (parentId) {
          this.state.expandedFolders = {
            ...this.state.expandedFolders,
            [parentId]: true,
          };
        }
      }
    }

    this.state.currentSideMenuId = targetItem.sideMenuId;
    // 通知UI更新位置
    locationChange?.(targetItem.sideMenuId);
  }

  /**
   * 后退按钮操作 - 在浏览历史中后退一步
   * 根据历史记录恢复之前访问的文件夹状态
   * @param locationChange 位置变更的回调函数
   */
  goBack(locationChange?: (menuId: string) => void): void {
    // 检查是否可以后退
    if (this.canGoBack()) {
      const targetIndex = this.state.historyIndex - 1;
      this.navToHistoryIndex(targetIndex, locationChange);
    }
  }

  /**
   * 前进按钮操作 - 在浏览历史中前进一步
   * 导航到历史记录中的下一个文件夹
   * @param locationChange 位置变更的回调函数
   */
  goForward(locationChange?: (menuId: string) => void): void {
    // 检查是否可以前进
    if (this.canGoForward()) {
      const targetIndex = this.state.historyIndex + 1;
      this.navToHistoryIndex(targetIndex, locationChange);
    }
  }

  /**
   * 是否可以后退 - 用于控制后退按钮状态
   * @returns 是否可以执行后退操作
   */
  canGoBack(): boolean {
    return this.state.historyIndex > 0;
  }

  /**
   * 是否可以前进 - 用于控制前进按钮状态
   * @returns 是否可以执行前进操作
   */
  canGoForward(): boolean {
    return this.state.historyIndex < this.state.browseHistory.length - 1;
  }

  /**
   * 更新文件夹的子项 - 用于异步加载子文件夹和文件
   * @param folderId 要更新子项的文件夹ID
   * @param children 新的子项列表
   */
  updateFolderChildren(folder: FolderItem, children: (FolderItem | FileItem)[]): void {
    if (folder) {
      // 更新子项
      folder.children = children;
    } else {
      console.error('文件夹不存在，无法更新children:', folder);
    }
  }

  /**
   * 更新当前侧边栏菜单ID
   * @param menuId 新的侧边栏菜单ID
   */
  setCurrentSideMenuId(menuId: string): void {
    this.state.currentSideMenuId = menuId;
  }

  /**
   * 获取当前侧边栏菜单ID
   * @returns 当前侧边栏菜单ID
   */
  getCurrentSideMenuId(): string {
    return this.state.currentSideMenuId;
  }
}
