import { message, Modal } from 'antd';
import path from 'path';
import { useState } from 'react';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import { getPositioning } from '@/components/fileList';
import { fm2 } from '@/modules/Locale';
import { getBaseName } from '@/utils/file';

import { Content } from './Content';
import { SideMenu } from './SideMenu/index';
import css from './style.less';

export interface Options {
  /** 目标位置guid */
  locationGuid?: string;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  type?: 'create' | 'move' | 'shortcut';
  source: {
    /** 待操作文件 name */
    name: string;
    /** 待操作文件 guid */
    fileGuid: string;
    /** 待操作文件父级guid */
    parentGuid: string;
    isAdmin?: boolean;
    role?: string;
  }[];
}

// 过滤文件名，最后拼接上后缀
function filterFileName(name: string) {
  const ext = path.extname(name);
  const baseName = path.basename(name, ext);
  return `${baseName} ${fm2('FilePathPicker.copy')}${ext}`;
}

export const FilePathPicker = (options: Options) => {
  const { locationGuid = 'desktop', onOk, type = 'create', source } = options;
  if (source.length === 0) throw new Error('原文件不能为空');

  const _filename =
    source.length > 1
      ? `${fm2('FilePathPicker.originalName')} ${fm2('FilePathPicker.copy')}`
      : `${getBaseName(source[0].name)} ${fm2('FilePathPicker.copy')}`;

  let modalInstance: any;

  function ModalContent() {
    const [curLocationGuid, setCurLocationGuid] = useState(locationGuid);
    const [menuId, setMenuId] = useState<string>('used');
    const [isManual, setIsManual] = useState(false);

    // 创建副本
    async function createDuplicate(locationGuid: string, folderName: string) {
      const params = {
        files: source.map((item) => {
          return {
            guid: item.fileGuid,
            name: filterFileName(item.name),
          };
        }),
        folder: locationGuid,
      };
      const [err, res] = await to(fileApi.duplicateBatch(params));
      if (res?.status !== 200) return message.error(err?.data?.msg);

      message.success(fm2('FilePathPicker.createSuccess', { folderName }));
      onOk?.();
    }

    // 移动到
    async function moveFile(locationGuid: string, folderName: string) {
      const params = {
        entries: source.map((item) => {
          return {
            to: locationGuid,
            fileGuid: item.fileGuid,
          };
        }),
      };
      const [err, res] = await to(fileApi.move(params));
      if (res?.status !== 204) return message.error(err?.data?.msg);

      message.success(fm2('FilePathPicker.moveSuccess', { folderName }));
      onOk?.();
    }
    //创建快捷方式
    async function shortcutTo(folder: string, folderName: string) {
      const sourceGuid = source[0].fileGuid;
      const [err, res] = await to(fileApi.createdShortcut({ folder, sourceGuid }));
      if (res?.status !== 200) return message.error(err?.data?.msg);
      message.success({
        content: (
          <div className={'msgBox'}>
            <span className={'msgTitle'}>{source[0].name}</span> {fm2('RightclickMouse.createdTo')}
            <span className={'msgTitle'}>「{folderName}」</span>
            <span className={'msgBtn'} onClick={() => getPositioning(res.data)}>
              {fm2('RightclickMouse.openImmediately')}
            </span>
          </div>
        ),
        duration: 4,
      });
      onOk?.();
    }

    return (
      <div className={css.modalContent}>
        <SideMenu
          curMenuId={menuId}
          type={type}
          onSideMenuChange={(menuId, isManual) => {
            setMenuId(menuId);
            setIsManual(isManual);
          }}
        />
        <Content
          filename={_filename}
          isAdmin={source[0].isAdmin}
          isManual={isManual}
          locationGuid={locationGuid}
          role={source[0].role}
          sideMenuId={menuId}
          sourceFileParentGuid={source[0].parentGuid === 'Desktop' ? 'desktop' : source[0].parentGuid}
          type={type}
          onCancel={() => {
            modalInstance.destroy();
          }}
          onLocationChange={(locationGuid) => {
            setCurLocationGuid(locationGuid);
          }}
          onOk={(folderName) => {
            const _locationGuid = curLocationGuid === 'desktop' ? 'Desktop' : curLocationGuid;
            const _folderName = curLocationGuid === 'desktop' ? fm2('FilePathPicker.desktop') : folderName;
            if (type === 'create') {
              createDuplicate(_locationGuid, _folderName);
            } else if (type === 'shortcut') {
              shortcutTo(_locationGuid, _folderName);
            } else {
              moveFile(_locationGuid, _folderName);
            }
            modalInstance.destroy();
          }}
          onSideMenuChange={(menuId, isManual = false) => {
            setMenuId(menuId);
            setIsManual(isManual);
          }}
        />
      </div>
    );
  }

  modalInstance = Modal.info({
    width: 893,
    icon: null,
    footer: null,
    centered: true,
    closable: false,
    className: css.filePathPicker,
    content: <ModalContent />,
  });
};
