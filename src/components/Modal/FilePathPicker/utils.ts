import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import * as spaceApi from '@/api/Space';
import type { FileDetail } from '@/types/api';

import type { FileItem, FolderItem } from './FolderManager';

function getSourceMenuId(item: FileDetail) {
  if (item.isSpace || String(item.subType) === '2' || item.space_guid) return 'space';
  if (item.starred) return 'favorites';

  return 'desktop';
}

// 辅助函数，过滤文件
function mapFileItem(item: FileDetail): FileItem {
  return {
    id: item.guid,
    name: item.name,
    type: item.type,
  };
}

// 辅助函数，过滤文件夹
function mapFolderItem(item: any): FolderItem {
  return {
    id: item.guid,
    name: item.name,
    type: 'folder',
    role: item.role,
    sourceMenuId: getSourceMenuId(item),
  };
}

// 将文件列表分离为文件和文件夹
export function processFileList(list: FileDetail[]) {
  const fileItems = list
    .filter((item: FileDetail) => !item.isFolder && !item.isSpace)
    .sort((a, b) => b.updatedAt - a.updatedAt)
    .map(mapFileItem);

  const folderItems = list.filter((item: FileDetail) => item.isFolder || item.isSpace).map(mapFolderItem);

  return { files: fileItems, folders: folderItems };
}

export async function getFilesByGuid({ type, guid = 'Desktop' }: { type?: 'used' | 'shared'; guid?: string }) {
  let params = {};
  if (type) {
    params = { type };
  } else {
    params = { folder: guid };
  }
  const [, res] = await to(fileApi.files(params));
  if (res?.status === 200) {
    const list = res.data.list || [];
    const { files: fileItems, folders: folderItems } = processFileList(list);
    // 返回处理后的数据，以便在enterFolder中使用
    return {
      files: fileItems,
      folders: folderItems,
    };
  }
  return null;
}

export async function getSpaceList() {
  const [, res] = await to(spaceApi.getSpaceList({}));
  const [, res1] = await to(spaceApi.getSpacePinList());

  if (res?.status === 200 || res1?.status === 200) {
    const pinSpaces = (res1?.data?.spaces || []).map((item: FileDetail) => ({ ...item, isPin: true }));
    const spaces = res?.data?.spaces || [];
    const allSpaces = [...pinSpaces, ...spaces];
    const folderItems = allSpaces.map((item: FileDetail) => ({
      id: item.guid,
      name: item.name,
      type: 'folder',
      sourceMenuId: 'space',
      role: item.role,
      isPin: item?.isPin,
    }));
    return {
      files: [],
      folders: folderItems,
    };
  }
}

// 获取最近位置
export async function getRecentList() {
  const [, res] = await to(fileApi.recentLocation());
  if (res?.status === 200) {
    const list = res.data.data?.items || [];
    const folderItems = list
      .sort((a: FileDetail, b: FileDetail) => b.updatedAt - a.updatedAt)
      .map((item: FileDetail) => ({
        id: item.guid,
        name: item.name,
        type: 'folder',
        sourceMenuId: getSourceMenuId(item),
        role: item.role,
      }));
    return {
      files: [],
      folders: folderItems,
    };
  }
}

export async function getStarredList() {
  const [, res] = await to(fileApi.getStarredFileList({ orderBy: 'updatedAt' }));
  if (res?.status === 200) {
    const list = res.data || [];
    const { files: fileItems, folders: folderItems } = processFileList(list);
    return {
      files: fileItems,
      folders: folderItems,
    };
  }
}

export async function getResultByMenuId(menuId: string) {
  let result = null;
  // 根据不同的菜单类型调用相应的API
  switch (menuId) {
    case 'used':
      result = await getRecentList();
      break;
    case 'shared':
    case 'desktop': {
      const params = menuId === 'shared' ? { type: menuId } : {};
      result = await getFilesByGuid(params);
      break;
    }
    case 'favorites': {
      result = await getStarredList();
      break;
    }
    case 'space': {
      result = await getSpaceList();
      break;
    }
  }
  return result;
}
