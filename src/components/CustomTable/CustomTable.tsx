import type { TableProps } from 'antd';
import { ConfigProvider, Table } from 'antd';
import type { ColumnGroupType, ColumnType } from 'antd/lib/table';
import React, { memo } from 'react';

import { TableLoadingGif } from '@/components/LoadingOld/LoadingGif';

import { StyledCustomTable } from './CustomTable.styled';

interface CustomTableProps<T = any> extends Omit<TableProps, 'columns' | 'loading'> {
  loading?: boolean;
  columns: (ColumnGroupType<T> | ColumnType<T>)[];
  renderEmpty?: (componentName?: string) => React.ReactNode;
}

export const CustomTable = memo(({ renderEmpty, loading, ...tableProps }: CustomTableProps) => {
  return (
    <StyledCustomTable>
      <ConfigProvider renderEmpty={renderEmpty}>
        <Table
          bordered
          loading={{
            spinning: !!loading,
            indicator: <TableLoadingGif />,
          }}
          {...tableProps}
        />
      </ConfigProvider>
    </StyledCustomTable>
  );
});
