import styled from 'styled-components';

export const StyledCustomTable = styled.div`
  & > .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
    border-left: unset;

    & > .ant-table-content > table > thead > tr > th,
    & > .ant-table-header > table > thead > tr > th {
      border-right: unset;
    }

    & table thead,
    & .ant-table-header .ant-table-thead {
      &,
      & > tr {
        height: 50px;
      }
    }

    table {
      border-radius: 4px;

      & > thead > tr > th:first-child {
        display: flex;
        height: inherit;
        align-items: center;
        justify-content: center;
      }

      & > thead > tr > th {
        font-weight: 500;
        font-size: 13px;
        padding: 0 16px;
        border-right: unset;
      }

      & .ant-table-tbody > tr {
        & > .ant-table-selection-column,
        & > td:last-child {
          border-inline-end: unset;
        }

        & > .ant-table-cell {
          height: 44px;
          padding: 0 16px;
          font-size: 13px;
          line-height: 20px;
          color: var(--theme-text-color-default);
        }
      }
    }
  }

  .ant-table-virtual {
    table {
      colgroup {
      }
    }

    .ant-table-tbody-virtual {
      .ant-table-row {
        height: 44px;

        .ant-table-cell:first-child {
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ant-table-cell {
          padding: 10px 16px 6px 10px;
        }

        .ant-table-selection-column {
          border-right: none;
        }

        .ant-table-cell:last-child {
          border-right: none;
        }
      }
    }
  }
`;
