.userCardContainer {
  display: flex;
  width: 260px;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  box-sizing: border-box;
  background:
    linear-gradient(181deg, rgba(255, 255, 255, 30%), var(--theme-basic-color-bg-default)),
    url('@/assets/images/common/userCardBackground.png') 0 0 / 35px auto repeat,
    var(--theme-basic-color-bg-default);
  box-shadow: 0 8px 18px 0 var(--theme-basic-color-lighter);

  .userCardHeader {
    width: 100%;
    display: flex;
    align-items: center;

    .userInfo {
      display: flex;
      flex-direction: column;
      margin-left: 20px;

      .userName {
        font-size: 14px;
        color: var(--theme-text-color-default);
      }

      .userEmail {
        font-size: 12px;
        color: var(--theme-text-color-secondary);
      }
    }
  }

  .userDescription {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-top: 1px solid var(--theme-basic-color-lighter);
    margin-top: 20px;

    .userDescriptionItem {
      display: flex;
      flex-direction: row;
      margin-top: 8px;
      font-size: 12px;
      padding: 1px 0;

      .userDescriptionLabel {
        width: 60px;
        color: var(--theme-text-color-secondary);
      }

      .userDescriptionValue {
        color: var(--theme-text-color-default);
        margin-left: 8px;
      }
    }
  }
}
