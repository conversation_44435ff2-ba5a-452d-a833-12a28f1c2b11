import { Popover } from 'antd';
import type { PopoverProps } from 'antd/es/popover';
import React from 'react';

import UserCard from '@/components/UserCardPopover/components/UserCard';

import styles from './index.less';

interface UserCardPopoverProps extends PopoverProps {
  userId: number | string;
  children: React.ReactNode | string;
  trigger?: PopoverProps['trigger'];
  cardType?: 'sdk' | 'normal';
}

const UserCardPopover: React.FC<UserCardPopoverProps> = ({
  userId,
  children,
  placement = 'leftTop',
  trigger = 'click',
  cardType,
  ...rest
}) => {
  return (
    <div className={styles.userCardPopover}>
      <Popover
        key={userId}
        arrow={false}
        content={<UserCard cardType={cardType} userId={userId} />}
        mouseEnterDelay={0.5}
        placement={placement}
        trigger={trigger}
        {...rest}
      >
        <span className={styles.userChildren} onClick={(e) => e.stopPropagation()}>
          {children}
        </span>
      </Popover>
    </div>
  );
};

export default UserCardPopover;
