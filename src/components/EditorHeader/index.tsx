import { CaretDownOutlined, LeftOutlined, PlusOutlined } from '@ant-design/icons';
import { message, Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { history, useParams } from 'umi';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import starIcon from '@/assets/images/editor/<EMAIL>';
import { ReactComponent as CancelStarIcon } from '@/assets/images/svg/cancelStar.svg';
import { ReactComponent as DemoIcon } from '@/assets/images/svg/demo.svg';
import { ReactComponent as FollowIcon } from '@/assets/images/svg/follow.svg';
import { ReactComponent as HistoryIcon } from '@/assets/images/svg/history.svg';
import { ReactComponent as MoreIcon } from '@/assets/images/svg/more.svg';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { CreateFolder } from '@/components/Modal/CreateFolder';
import { isSupportedImportExt } from '@/constants/fileTypes';
import { useQuickAccess } from '@/hooks/QuickAccess';
import { useCollaborators } from '@/hooks/useCollaborators';
import { useCreateFile } from '@/hooks/useCreateFile';
import { useFileDetail } from '@/hooks/useFileDetail';
import { useFileEdit } from '@/hooks/useFileEdit';
import useFileUpload from '@/hooks/useFileUpload';
import { useFollow } from '@/hooks/useFollow';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm, fm2 } from '@/modules/Locale';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
import { editorStore } from '@/store/Editor';
import { useQuickAccessStore } from '@/store/QuickAccess';
import { useTemplateStore } from '@/store/TemplateLib';
import type { FileDetail } from '@/types/api';
import { setBrowserTabTitle } from '@/utils/browser';
import { DEFAULT_PATH } from '@/utils/constants';
import { downloadFile, getOpenFilePermissionTip, getOpenFileUrl } from '@/utils/file';
import { getSMEditor } from '@/utils/ShimoSDK';

import { useShowOrHide } from '../../hooks/useShowOrHide';
import { AddNewPopover } from '../AddNewPopover';
import { AvatarGroup } from '../AvatarGroup';
import { BackToPopover } from '../BackToPopover';
import CollaborationShare from '../Collaboration';
import { FileMenuPopover } from '../FileMenuPopover';
import type { ShowGuideRef } from '../HelpGuide';
import { HelpGuide } from '../HelpGuide';
import { FilePathPicker } from '../Modal/FilePathPicker';
import useIsBoard from './hooks/useIsBoard';
import MindMapHistoryDrawer from './MindMapHistoryDrawer';
import { SaveStatus } from './SaveStatus';
import { SaveTemplate, useSaveTemplate } from './SaveTemplate';
import css from './style.less';
import { TitleInput } from './TitleInput/index';

interface Props {
  data?: FileDetail;
}

export type EditorHeaderRef = {
  toggleShortcutPanel: () => void;
};

export const EditorHeader = forwardRef<EditorHeaderRef, Props>(({ data }, ref) => {
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || '';
  const { isBoard } = useIsBoard();

  // 控制帮助文档是否展示
  const helpGuideRef = useRef<ShowGuideRef>(null);
  // 收藏按钮
  const [isStar, setIsStar] = useState(false);
  // 存储文件详情数据
  const [fileDetail, setFileDetail] = useState({} as FileDetail);
  // 最近使用文件列表
  const [recentFiles, setRecentFiles] = useState<FileDetail[]>([]);
  // 父级guid
  const [parentGuid, setParentGuid] = useState<string | undefined>(undefined);
  const { createFile: createFileForPc } = useCreateFile(parentGuid || '');
  // 控制文件菜单弹框显示
  const [fileMenuVisible, setFileMenuVisible] = useState(false);
  // 正在收藏中的状态
  const [isStaring, setIsStaring] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  const [subscribed, setSubscribed] = useState(false);

  const isOnline = useNetworkStatus();
  // 添加到快速访问状态
  const [quickAdding, setQuickAdding] = useState(false);
  const [historyOpen, setHistoryOpen] = useState(false);
  const quickAccess = useQuickAccess();

  // 可导入编辑的文件类型
  const isShowEditBtn = useMemo(() => {
    return isSupportedImportExt(fileDetail?.subType || '');
  }, [fileDetail?.subType]);
  const hasEditPermission = useMemo(
    () => fileDetail?.permissionsAndReasons?.canEdit.value,
    [fileDetail?.permissionsAndReasons?.canEdit.value],
  );
  const { debouncedHandleEdit } = useFileEdit(fileDetail, guid);

  const { isFollowing } = useFollow({ type: fileDetail.type });

  useEffect(() => {
    if (!isOnline) {
      messageApi.info(
        <span>
          {fm2('Editor.saveStatus.offline')}。
          <a
            className="know"
            href="javascript:void(0)"
            onClick={() => {
              messageApi.destroy();
            }}
          >
            {fm2('Editor.ok')}
          </a>
        </span>,
        0,
      );
    }
  }, [isOnline]);

  // 实现标题输入框
  const [title, setTitle] = useState('');
  const [shareVisible, setShareVisible] = useState(false);

  const { openFileDetail } = useFileDetail();

  const [showShortcutPanel, setShowShortcutPanel] = useState(false);
  const typeText = useMemo<string>(() => {
    if (!fileDetail.type) return '';
    return fm2(`File.${fileDetail.type}`);
  }, [fileDetail.type]);

  // 国际化文案
  const inputPlaceholder = fm('Header.inputPlaceholder');
  const backButtonTipText = fm('Header.backButtonTipText');
  const backToButtonTipText = fm('Header.backToButtonTipText');
  const createButtonTipText = fm('Header.createButtonTipText');
  const teamButtonText = fm('Header.teamButtonText');
  const shareButtonText = fm('Header.shareButtonText');
  const fileMenuButtonTipText = fm('Header.fileMenuButtonTipText');
  const unfavorited = fm('Header.unfavorited');
  const favorited = fm('Header.favorited');
  const favorite = fm('Header.favorite');
  const unfavorite = fm('Header.unfavorite');
  const loadingText = fm('Common.loadingText');
  const historyButtonText = fm('Header.historyButtonText');
  const demoButtonText = fm('Header.demoButtonText');
  const followingModeText = fm('Header.followingModeText');
  const i18n_editButtonText = fm('Header.editButtonText');
  const i18n_fileDownloadErr = fm('File.downloadErr');
  const i18n_fileStartDownload = fm('File.startDownload');
  const i18n_downloadButtonText = fm('Header.downloadButtonText');
  const i18n_noAuthEdit = fm('Header.noAuthEdit');

  const i18nText = {
    delete: fm('File.delete'),
    title: fm('deleteConfirm.title'),
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    action: fm('File.delete'),
    success: fm('deleteConfirm.success'),
    error: fm('deleteConfirm.error'),
    noSupport: fm('Editor.noSupport'),
    copy: fm('FilePathPicker.copy'),
    unSubscribed: fm('Header.unSubscribed', { type: typeText }),
    subscribed: fm('Header.subscribed', { type: typeText }),
  };

  const { refreshList } = useQuickAccess();

  const { setControlStatus, showComment, showMenu, showWriter, setShowComment } = useShowOrHide();

  const { saveTemplate } = useSaveTemplate({ reload: () => {} });

  useEffect(() => {
    refreshList();
  }, [refreshList]);
  const { list: quickList } = useQuickAccessStore();

  // 使用协作者 hook
  const { collaborators } = useCollaborators();

  // 获取最近使用的文件
  async function getRecentFiles() {
    const [, res] = await to(fileApi.files({ type: 'used', limit: 20 }));
    if (res?.status === 200) {
      const fileData = res.data.list as FileDetail[];
      setRecentFiles(fileData.slice(0, 5));
    }
  }

  function showDownTip() {
    messageApi.open({
      type: 'loading',
      content: loadingText,
      duration: 0,
      style: {
        marginTop: '50px',
      },
    });
  }

  // 导出表格文件
  async function exportTableFile() {
    showDownTip();
    const [, res] = await to(fileApi.exportTable(guid));
    if (res?.status === 200) {
      downloadFile(res.data.downloadUrl, `${fileDetail.name}`);
    }
    messageApi.destroy();
  }

  // 轮训获取导出进度
  async function getExportProgress(taskId: string) {
    const [, res] = await to(fileApi.exportProgress({ taskId }));
    if (res?.status !== 200) return messageApi.destroy();

    const { progress, downloadUrl } = res.data.data;
    if (progress < 100) {
      setTimeout(() => {
        getExportProgress(taskId);
      }, 1500);
    } else if (progress === 100) {
      downloadFile(downloadUrl, `${fileDetail.name}`);
      messageApi.destroy();
    }
  }

  // 导出不同类型文件
  async function exportFile(exportType: string) {
    if (fileDetail.type === 'modoc' && ['imagePdf', 'image'].includes(exportType)) {
      getSMEditor()?.docsApi.pluginManager.getInstance('Paint').exporter.export(exportType);
      return;
    }
    if (fileDetail.type === 'mosheet' && ['pdf', 'image'].includes(exportType)) {
      if (exportType === 'pdf') {
        getSMEditor()?.exportPdf?.();
      } else {
        getSMEditor()?.exportImage?.();
      }
      return;
    }
    if (fileDetail.type === 'presentation' && ['imagePdf', 'image'].includes(exportType)) {
      getSMEditor()?.export?.(exportType);
      return;
    }
    showDownTip();
    const [err, res] = await to(fileApi.exportFile(guid, { type: exportType }));
    if (res?.status !== 200) {
      messageApi.destroy();
      message.error(err?.data?.msg);
      return;
    }

    const { taskId } = res.data.data;
    getExportProgress(taskId);
  }

  // 删除文件
  function deleteFile() {
    deleteConfirm({
      i18nText,
      data: guid,
      api: fileApi.deleteFile,
      callback: () => {
        history.push('/desktop');
      },
    });
  }

  function getUpdateStatus() {
    fileApi.getUpdateSubscription(guid).then((res) => {
      if (res.status === 200) {
        const { data } = res;
        setSubscribed(data.subscribed);
      }
    });
  }

  // 使用文件上传 hook
  const { triggerUpload } = useFileUpload({
    parentGuid,
  });

  useEffect(() => {
    if (data) {
      // 查看文件详情
      const viewFileDetail = (fileDetail: FileDetail) => {
        setFileDetail(fileDetail);
        setIsStar(fileDetail.starred);
        setTitle(fileDetail.name);
        setParentGuid(fileDetail.parent_guid);
        editorStore.getState().update({ fileType: fileDetail.type });
      };
      viewFileDetail(data);
    }
  }, [data]);

  useEffect(() => {
    // 用户打开文件动作
    const userAction = async () => {
      await to(fileApi.userAction(guid, { trackOpen: 1 }));
      getRecentFiles();
    };
    userAction();
    getUpdateStatus();
  }, [guid]);

  useEffect(() => {
    setBrowserTabTitle(title);
  }, [title]);
  useEffect(() => {
    const permissionsAndReasons = fileDetail?.permissionsAndReasons;
    if (permissionsAndReasons) {
      const tipMessage = getOpenFilePermissionTip(permissionsAndReasons);
      setTimeout(() => {
        message.warning(tipMessage);
      }, 1500);
    }
  }, [fileDetail]);

  // 重命名文件
  async function rename(newTitle: string) {
    const [err, res] = await to(fileApi.rename(guid, newTitle));
    if (res?.status !== 200) return message.error(err?.data?.msg);

    setFileDetail({
      ...fileDetail,
      name: newTitle,
    });
    getRecentFiles();
    setTitle(newTitle);
    getSMEditor()?.setTitle?.(newTitle);
  }

  function handleTitleChange(newTitle: string) {
    rename(newTitle);
  }

  const quickAdded = quickList.some((item) => item.guid === guid);

  // 切换快速访问添加、移除
  async function toggleQuickAccess() {
    if (quickAdding) return;
    setQuickAdding(true);
    if (quickAdded) {
      await quickAccess.remove(guid, fileDetail.name);
    } else {
      await quickAccess.add(guid, fileDetail.name);
    }
    setQuickAdding(false);
  }

  // 切换收藏
  async function toggleStar() {
    if (isStaring) return;
    setIsStaring(true);

    if (isStar) {
      // 取消收藏
      const [, res] = await to(fileApi.cancelStar(guid));
      if (res?.status === 204) {
        setIsStar(false);
        message.info(`「${fileDetail?.name}」${unfavorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: false,
        });
      }
    } else {
      // 收藏
      const [, res] = await to(fileApi.star(guid));
      if (res?.status === 204) {
        setIsStar(true);
        message.success(`「${fileDetail?.name}」${favorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: true,
        });
      }
    }
    // 处理完成，重置状态
    setIsStaring(false);
  }

  // 打开创建文件夹弹窗
  function createFolder() {
    CreateFolder({
      onOk: (name) => {
        window.open(`/api/v1/files/create/folder?parentGuid=${parentGuid}&name=${name}`, '_self');
      },
    });
  }

  // 保存版本
  function saveVersion() {
    CreateFolder({
      type: 'saveVersion',
      onOk: (name) => {
        getSMEditor()?.createRevision?.({ name });
      },
    });
  }

  function openFilePathPicker(type: string) {
    if (type === 'create') {
      FilePathPicker({
        locationGuid: 'desktop',
        source: [
          {
            name: fileDetail.name,
            fileGuid: guid,
            parentGuid: fileDetail.parent_guid,
            isAdmin: fileDetail.isAdmin || fileDetail.isFileAdmin,
            role: fileDetail.role,
          },
        ],
        onOk: () => {},
      });
    } else if (type === 'shortcut') {
      FilePathPicker({
        type: 'shortcut',
        locationGuid: 'desktop',
        source: [
          {
            name: fileDetail.name,
            fileGuid: guid,
            parentGuid: fileDetail.parent_guid,
            isAdmin: fileDetail.isAdmin || fileDetail.isFileAdmin,
            role: fileDetail.role,
          },
        ],
        onOk: () => {},
      });
    } else {
      FilePathPicker({
        type: 'move',
        locationGuid: 'desktop',
        source: [
          {
            name: fileDetail.name,
            fileGuid: guid,
            parentGuid: fileDetail.parent_guid,
            isAdmin: fileDetail.isAdmin || fileDetail.isFileAdmin,
            role: fileDetail.role,
          },
        ],
        onOk: async () => {
          // 移动成功后，源文件的属性会发生变更，故需要更新整个文件详情
          const [, res] = await to(fileApi.fileDetail(guid));
          if (res?.status === 200) {
            setFileDetail(res.data);
          }
        },
      });
    }
  }

  function createFile(fileType: string, formType?: string) {
    if (fileType === 'whiteboard') {
      createFileForPc('whiteboard');
      return;
    }

    let url = `/api/v1/files/create/${fileType}?parentGuid=${parentGuid}&name=${inputPlaceholder}`;
    if (formType) {
      url += `&formType=${formType}`;
    }
    window.open(url, '_self');
  }

  function openFile({ fileType, fileGuid }: { fileType?: string; fileGuid?: string }) {
    if (fileGuid) {
      const url = getOpenFileUrl(fileGuid, fileType);
      if (fileType === 'folder') {
        fileApi.userAction(fileGuid, { trackOpen: 1 });
      }
      window.open(url, '_self');
    }
  }

  function back() {
    // 确定目标路径：对上级目录没有权限时或者上级目录是桌面时，返回桌面，否则进入上级文件夹
    const targetPath = !parentGuid || parentGuid === 'Desktop' ? '/desktop' : `/folder/${parentGuid}`;
    // 使用 location.href，解决返回上一级后，sdk右侧弹框没有关闭的问题
    location.href = targetPath;
  }

  function subscribeUpdate(guid: string) {
    if (!guid) return;
    if (subscribed) {
      fileApi.deleteUpdateSubscription(guid).then(() => {
        message.open({ content: i18nText.unSubscribed });
        getUpdateStatus();
      });
    } else {
      fileApi.updateSubscription(guid).then(() => {
        message.open({ content: i18nText.subscribed });
        getUpdateStatus();
      });
    }
  }
  interface MenuItem {
    key: string;
    exportType?: string;
    fileType?: string;
    fileGuid?: string;
    noSupport?: boolean;
  }

  const { setTemplateProp } = useTemplateStore((state) => state);
  function showTemplateLibrary() {
    setTemplateProp({ parentGuid: parentGuid || '', isShowTemplateLib: true, propType: 'editor' });
  }

  // 处理导航项点击
  function handleMenuItemClick({ key, exportType, fileType, fileGuid, noSupport }: MenuItem) {
    setFileMenuVisible(false);

    if (noSupport) {
      message.warning(i18nText.noSupport);

      return;
    }
    if (key.includes('down') && exportType) {
      exportFile(exportType);
      return;
    }
    // 处理创建文件类型
    const createFileTypes = [
      'newdoc',
      'modoc',
      'mosheet',
      'presentation',
      'table',
      'normalForm',
      'tableViewForm',
      'quizForm',
      'whiteboard',
      'mindmap',
    ];

    if (['help-guide', 'help-shortcut'].includes(key)) {
      helpGuideRef.current?.show({ fileType: fileDetail.type, key: key });
      return;
    }

    if (key === 'help-helpCenter') {
      const origin = 'https://shimo.im';
      let path = '';
      switch (fileDetail.type) {
        case 'newdoc':
        case 'modoc':
          path = '/help/docs';
          break;
        case 'mosheet':
        case 'table':
          path = '/help/sheets';
          break;
        case 'board':
          path = '/help/boards';
          break;
        case 'presentation':
          path = '/help/slides';
          break;
        case 'mindmap':
          path = '/help/mindmaps';
          break;
        default:
          path = '/help/docs';
          break;
      }
      window.open(`${origin}${path}`, '_blank');

      return;
    }

    if (createFileTypes.includes(key)) {
      if (['normalForm', 'tableViewForm', 'quizForm'].includes(key)) {
        const formType = key === 'normalForm' ? '' : key === 'tableViewForm' ? 'table' : 'quiz';
        return createFile('form', formType);
      }
      return createFile(key);
    }
    if (key === 'upload') {
      return triggerUpload();
    }
    if (key === 'uploadFolder') return;
    if (key === 'templateLibrary') {
      // 显示模板库
      return showTemplateLibrary();
    }

    // 处理导航
    switch (key) {
      case 'back':
        back();
        break;
      case 'folder':
        createFolder();
        break;
      case 'myDesktop':
        history.push('/desktop');
        break;
      case 'workbench':
        history.push(DEFAULT_PATH);
        break;
      case 'quickAccess':
      case 'recent':
        openFile({ fileType, fileGuid });
        break;
      case 'favorite':
        toggleStar();
        break;
      case 'downToExcel':
        exportTableFile();
        break;
      case 'delete':
        deleteFile();
        break;
      case 'viewCommentList':
      case 'viewComment':
        if (fileDetail.type === FileIconType.Presentation) {
          setControlStatus('comment');
          return;
        }
        getSMEditor()?.showComments?.();
        break;
      case 'addComment':
        getSMEditor()?.addComment?.();
        if (fileDetail.type === FileIconType.Presentation) {
          // 需要勾选【查看评论】
          setShowComment(true);
          return;
        }
        break;
      case 'fileInfo':
        openFileDetail({ guid, type: fileDetail.type });
        break;
      case 'print':
        if (fileDetail.type === 'modoc') {
          getSMEditor()?.printAll?.();
          return;
        }
        getSMEditor()?.print?.();
        break;
      case 'saveVersion':
        if (['modoc'].includes(fileDetail.type)) {
          saveVersion();
          return;
        }
        if (fileDetail.type === FileIconType.Presentation) {
          getSMEditor()?.saveRevision?.();
          return;
        }
        getSMEditor()?.createRevision?.();
        break;
      case 'viewHistory':
        if (fileDetail?.type === 'mindmap') {
          setHistoryOpen(true);
        }
        if (fileDetail.type === 'table') {
          getSMEditor()?.showRevision?.();
        } else if (getSMEditor()?.showHistory) {
          getSMEditor()?.showHistory?.();
        }
        break;
      case 'startDemo':
        getSMEditor()?.startDemonstration?.();
        break;
      case 'shortcut':
        openFilePathPicker('shortcut');
        break;
      case 'createCopy':
        openFilePathPicker('create');
        break;
      case 'move':
        openFilePathPicker('move');
        break;
      case 'addToQuickAccess':
        toggleQuickAccess();
        break;
      case 'subscribeUpdate':
        subscribeUpdate(fileDetail.guid);
        break;
      case 'lockFile':
        break;
      case 'saveTemplate':
        saveTemplate.setVisible(true);
        break;
      case 'show-comment':
      case 'show-menu':
      case 'show-writer':
        setControlStatus(exportType);
        break;
      case 'viewLockMosheet':
        break;
      case 'follow':
        if (isFollowing) return;
        if (fileDetail.type === 'newdoc') {
          getSMEditor()?.startFollowMode();
        }
        if (fileDetail.type === 'mosheet') {
          getSMEditor()?.getFollowMode().start();
        }
        break;
    }

    setFileMenuVisible(false);
  }

  function handleMenuItemOpenInNewTab() {}

  // 文件菜单弹框显示状态变化
  function fileMenuOpenChange(visible: boolean) {
    setFileMenuVisible(visible);
  }

  // 表单 form 不显示演示、历史、分享按钮
  const isForm = fileDetail.type === 'form';
  // 应用表格，传统文档，表单，无演示按钮
  const showDemoBtn = !['table', 'modoc', 'form', 'mindmap'].includes(fileDetail.type);

  /** 文档，表格展示跟随模式 */
  const showFollowBtn = ['newdoc', 'mosheet'].includes(fileDetail.type);

  const debouncedHandleDownload = useMemo(() => {
    return debounce(
      async () => {
        try {
          if (!fileDetail) return;
          const downloadUrl = `/api/v1/files/${fileDetail.guid}/download`;

          // 预检查
          const res = await fetch(downloadUrl, { method: 'HEAD' });
          if (!res.ok) {
            throw new Error(i18n_fileDownloadErr);
          }

          downloadFile(downloadUrl, fileDetail.name);
          message.open({
            type: 'success',
            content: i18n_fileStartDownload,
          });
        } catch (error) {
          message.open({
            type: 'error',
            content: i18n_fileDownloadErr,
          });
        }
      },
      1000,
      { leading: true, trailing: false },
    );
  }, [fileDetail, i18n_fileDownloadErr, i18n_fileStartDownload]);

  useEffect(() => {
    return () => {
      debouncedHandleDownload.cancel();
    };
  }, [debouncedHandleDownload]);

  const renderEditBtn = () => {
    const editBtn = (
      <div
        className={classNames(css.btn, css.textBtn, css.btnLayout, {
          [css.disabled]: !hasEditPermission,
        })}
        onClick={!hasEditPermission ? undefined : () => debouncedHandleEdit()}
      >
        {i18n_editButtonText}
      </div>
    );
    return hasEditPermission ? (
      editBtn
    ) : (
      <Tooltip placement="bottom" title={i18n_noAuthEdit}>
        {editBtn}
      </Tooltip>
    );
  };

  useImperativeHandle(ref, () => ({
    toggleShortcutPanel: () => {
      if (!showShortcutPanel) {
        handleMenuItemClick({ key: 'help-docShortcut' });
        setShowShortcutPanel(true);
        return;
      }
      helpGuideRef.current?.hide();
      setShowShortcutPanel(false);
    },
  }));

  const onClose = () => {
    setHistoryOpen(false);
  };

  return (
    <div className={classNames(css.header, 'editor-header', 'file-page-header')}>
      <div className={css.leftSection}>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={backButtonTipText}>
          <div className={classNames(css.btn, css.arrowRightBtn)} onClick={() => handleMenuItemClick({ key: 'back' })}>
            <LeftOutlined />
          </div>
        </Tooltip>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={backToButtonTipText}>
          <Popover
            destroyTooltipOnHide
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={
              <BackToPopover quickAccessList={quickList} recentFiles={recentFiles} onItemClick={handleMenuItemClick} />
            }
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
          >
            <div className={classNames(css.btn, css.arrowDownBtn)}>
              <CaretDownOutlined />
            </div>
          </Popover>
        </Tooltip>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={createButtonTipText}>
          <Popover
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={<AddNewPopover onItemClick={handleMenuItemClick} onOpenInNewTab={handleMenuItemOpenInNewTab} />}
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
          >
            <div className={classNames(css.btn, css.plusBtn)}>
              <PlusOutlined />
            </div>
          </Popover>
        </Tooltip>
        {/* 标题输入框 */}
        <TitleInput oldTitle={title} readOnly={isShowEditBtn} onTitleChange={handleTitleChange} />

        {!isShowEditBtn && (
          <>
            <Tooltip
              classNames={{ root: 'tooltip-offset-y' }}
              placement="bottom"
              title={isStar ? unfavorite : favorite}
            >
              <div className={css.favoriteBtn} onClick={toggleStar}>
                <CancelStarIcon className={classNames(isStar ? css.hidden : css.show, css.cancelStarIcon)} />
                <img className={isStar ? css.show : css.hidden} src={starIcon} width={16} />
              </div>
            </Tooltip>

            {/* 保存状态 */}
            <SaveStatus fileType={fileDetail.type} />
          </>
        )}
      </div>

      <div className={css.rightSection}>
        {!isShowEditBtn && (
          <>
            <AvatarGroup avatars={collaborators} />
            {/* 历史 */}
            {!isForm && !isBoard && (
              <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={historyButtonText}>
                <div
                  className={classNames(css.btn, css.textBtn, css.historyBtn)}
                  onClick={() => handleMenuItemClick({ key: 'viewHistory' })}
                >
                  <HistoryIcon />
                </div>
              </Tooltip>
            )}

            {/* 演示 */}
            {showDemoBtn && !isBoard && (
              <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={demoButtonText}>
                <div
                  className={classNames(css.btn, css.textBtn, css.demoBtn)}
                  onClick={() => handleMenuItemClick({ key: 'startDemo' })}
                >
                  <DemoIcon />
                </div>
              </Tooltip>
            )}
            {/* 跟随模式 */}
            {showFollowBtn && (
              <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={followingModeText}>
                <div
                  className={classNames(css.btn, css.textBtn, { [css.followBtn]: isFollowing })}
                  onClick={() => handleMenuItemClick({ key: 'follow' })}
                >
                  <FollowIcon />
                </div>
              </Tooltip>
            )}
          </>
        )}

        {isShowEditBtn && (
          <>
            {/* 编辑按钮 */}
            {renderEditBtn()}
            {/* 下载按钮 */}
            <div
              className={classNames(css.btn, css.textBtn, {
                [css.btnLayout]: !isShowEditBtn,
              })}
              onClick={() => debouncedHandleDownload()}
            >
              {i18n_downloadButtonText}
            </div>
          </>
        )}

        {/* 分享 协作*/}
        {
          <Tooltip
            classNames={{ root: 'tooltip-offset-y' }}
            placement="bottom"
            title={shareButtonText + teamButtonText}
          >
            <div className={classNames(css.btn, css.textBtn, css.shareBtn)} onClick={() => setShareVisible(true)}>
              {shareButtonText + teamButtonText}
            </div>
          </Tooltip>
        }

        {/* 文件菜单 */}
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={fileMenuButtonTipText}>
          <Popover
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={
              <FileMenuPopover
                fileType={fileDetail.type}
                isAdmin={fileDetail.isAdmin || fileDetail.isFileAdmin}
                isFavorite={isStar}
                quickAccessAdded={quickAdded}
                role={fileDetail.role}
                showComment={showComment}
                showMenu={showMenu}
                showWriter={showWriter}
                subscribed={subscribed}
                onItemClick={handleMenuItemClick}
                onOpenInNewTab={handleMenuItemOpenInNewTab}
              />
            }
            destroyTooltipOnHide={true}
            open={fileMenuVisible}
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
            onOpenChange={fileMenuOpenChange}
          >
            <div className={classNames(css.btn, css.moreBtn)}>
              <MoreIcon height={14.5} width={14.5} />
            </div>
          </Popover>
        </Tooltip>
      </div>
      <CollaborationShare
        guid={fileDetail?.isShortcut ? fileDetail?.shortcutSource?.guid : (fileDetail?.guid ?? '')}
        visible={shareVisible}
        onCancel={() => setShareVisible(false)}
      />
      <HelpGuide ref={helpGuideRef} />
      {contextHolder}
      <MindMapHistoryDrawer open={historyOpen} onClose={onClose} />
      <SaveTemplate
        callback={saveTemplate.callback}
        params={{ name: fileDetail.name, guid: guid }}
        visible={saveTemplate.visible}
      />
    </div>
  );
});
