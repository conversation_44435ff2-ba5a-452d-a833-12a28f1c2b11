import { Button, Form, Input, message, Modal } from 'antd';
import React, { useState } from 'react';

import { saveTemplates } from '@/api/File';
import { fm } from '@/modules/Locale';
import { trimInput } from '@/utils/trim';
import { validateUnlawful } from '@/utils/validate';

interface FormModalProps {
  visible: boolean;
  params: { name?: string; guid: string };
  callback: ({ visible, refresh }: { visible: boolean; refresh?: boolean }) => void;
}

export const useSaveTemplate = ({ reload }: { reload: () => void }) => {
  const [visible, setVisible] = useState(false);

  const callback = ({ visible, refresh }: { visible: boolean; refresh?: boolean }) => {
    setVisible(visible);
    if (refresh) reload();
  };
  return {
    saveTemplate: {
      callback,
      setVisible,
      visible,
    },
  };
};

export const SaveTemplate: React.FC<FormModalProps> = ({ visible, callback, params }) => {
  const i18nText = {
    title: fm('SaveTemplate.title'),
    success: fm('SaveTemplate.success'),
    inputPlaceholder: fm('SaveTemplate.InputPlaceholder'),
    ok: fm('Space.sure'),
    cancel: fm('Space.cancel'),
    message: `${fm('RenameModal.validatorMessage')}: .\\/:*?"<>`,
    maxlength: fm('SaveTemplate.maxlength'),
  };
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState<string>();

  const handleOk = () => {
    form.validateFields().then(() => {
      setLoading(true);
      saveTemplates({ originGuid: params.guid, name: inputValue })
        .then((res) => {
          if (res.status === 200 || res.status === 204) {
            message.open({ content: i18nText.success });
            callback({ visible: false, refresh: true });
          }
        })
        .catch((err) => {
          message.error(err.data?.msg);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onCancel = () => {
    callback({ visible: false, refresh: false });
  };

  const afterOpenChange = (open: boolean) => {
    if (open) {
      setInputValue(params.name);

      form.setFieldValue('name', params.name);
    }
  };

  const inputHandleChange = (e: { target: { value: string } }) => {
    const value = trimInput(e.target.value);

    form.setFieldValue('name', value);

    setInputValue(value);
  };

  return (
    <Modal
      centered
      destroyOnClose
      afterOpenChange={afterOpenChange}
      footer={
        <div className="sm-footer">
          <Button
            className="sm-btn sm-btn-normal-primary sm-btn-pd15"
            disabled={!inputValue}
            loading={loading}
            onClick={handleOk}
          >
            {i18nText.ok}
          </Button>
          <Button className="sm-btn sm-btn-normal-secondary sm-btn-ml12 sm-btn-pd15" onClick={onCancel}>
            {i18nText.cancel}
          </Button>
        </div>
      }
      open={visible}
      title={i18nText.title}
      onCancel={onCancel}
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label=""
          name="name"
          rules={[
            { required: true, message: i18nText.inputPlaceholder },
            {
              max: 10,
              message: i18nText.maxlength,
            },
            {
              validator(rule, value) {
                if (validateUnlawful(value)) {
                  return Promise.reject(new Error(i18nText.message));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
        >
          <Input maxLength={10} value={inputValue} onChange={inputHandleChange} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
