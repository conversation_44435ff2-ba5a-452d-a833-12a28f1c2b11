import { useDndContext } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { Typography } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { history } from 'umi';

import { userAction } from '@/api/File';
import { ReactComponent as DeleteIcon } from '@/assets/images/svg/close-24.svg';
import { FileIcon } from '@/components/FileIcon';
import { useQuickAccess } from '@/hooks/QuickAccess';
import { useDragPosition } from '@/hooks/useDragContext';
import type { QuickAccessItem as QuickAccessItemType } from '@/model/QuickAccess';

import css from './style.less';

interface Props {
  item: QuickAccessItemType;
}
export function QuickAccessItem({ item }: Props) {
  const { remove } = useQuickAccess();
  const itemRef = useRef<HTMLDivElement>(null);
  const [dropPosition, setDropPosition] = useState<'top' | 'bottom' | null>(null);

  // 从全局DndContext获取拖拽状态
  const { active, over } = useDndContext();
  // 获取拖拽位置
  const dragPosition = useDragPosition();

  function handleDelete(e: React.MouseEvent) {
    e.stopPropagation();
    remove(item.guid, item.label);
  }

  function handleClick(e: React.MouseEvent) {
    e.stopPropagation();
    const url = item.url;
    if (item.type === 'folder') {
      userAction(item.guid, { trackOpen: 1 });
      history.push(url);
    } else {
      window.open(url, '_blank');
    }
  }

  // 检查是否是外部文件拖拽
  const isExternal = active?.data.current?.type === 'desktop-file';

  // 根据拖拽类型决定是否使用 sortable
  const sortableResult = useSortable({
    id: item.guid,
    data: {
      type: 'quick-access-file',
      fileCount: 1,
      file: {
        guid: item.guid,
        name: item.label,
        type: item.type,
      },
      dropPosition,
    },
  });

  // 如果是外部文件拖拽，不使用 sortable 的属性和监听器
  // 这样这个元素就不会响应拖拽事件，事件会穿透到父级 droppable
  const { attributes, listeners, setNodeRef } = isExternal
    ? { attributes: {}, listeners: {}, setNodeRef: (_node: HTMLElement | null) => {} }
    : sortableResult;

  // 检查当前项是否是悬停目标
  const isOver = over && over.id === item.guid;

  // 使用 dnd-kit 的拖拽位置来判断投放位置
  useEffect(() => {
    // 如果是外部文件拖拽，不显示排序指示器
    if (isExternal) {
      setDropPosition(null);
      return;
    }

    if (!itemRef.current || !active || active.id === item.guid || !isOver) {
      setDropPosition(null);
      return;
    }

    const rect = itemRef.current.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;

    if (dragPosition.y < midY) {
      setDropPosition('top');
    } else {
      setDropPosition('bottom');
    }
  }, [active, isOver, item.guid, dragPosition, isExternal]);

  // 设置ref引用
  function setRefs(element: HTMLDivElement | null) {
    setNodeRef(element);
    if (element) {
      (itemRef as React.MutableRefObject<HTMLDivElement | null>).current = element;
    }
  }

  return (
    <div
      ref={setRefs}
      className={classNames(css.item, {
        [css.isOver]: over,
      })}
      {...listeners}
      {...attributes}
    >
      {/* 上方投放指示器 */}
      {dropPosition === 'top' && <div className={css.dropIndicatorTop} />}
      <div className={css.dragHandle} onClick={handleClick}>
        <FileIcon isShortcut={item.isShortcut} type={item.type} />

        {active ? (
          <span className={css.label}> {item.label} </span>
        ) : (
          <span className={css.label}>
            <Typography.Text ellipsis={{ tooltip: { title: item.label } }}>{item.label}</Typography.Text>
          </span>
        )}
      </div>

      <DeleteIcon className={css.deleteBtn} onClick={handleDelete} />

      {/* 下方投放指示器 */}
      {dropPosition === 'bottom' && <div className={css.dropIndicatorBottom} />}
    </div>
  );
}
