/**
 * 登录表单功能，为登录页面或登录弹框提供登录的业务功能
 */
import { PlusOutlined } from '@ant-design/icons';
import type { MenuProps, SubMenuProps } from 'antd';
import { Button, Divider, Flex, Menu, Popover, Tooltip, Typography } from 'antd';
import Sider from 'antd/es/layout/Sider';
import classNames from 'classnames';
// import { debounce } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
// import ReactDOM from 'react-dom';
import { history, useLocation, useParams } from 'umi';

import { ReactComponent as WorkActivitySvg } from '@/assets/images/sidepanel/activity.svg';
import { ReactComponent as DesktopSvg } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as FavoritesSvg } from '@/assets/images/sidepanel/favorites.svg';
import { ReactComponent as RecentSvg } from '@/assets/images/sidepanel/recent.svg';
import { ReactComponent as ShareSvg } from '@/assets/images/sidepanel/share.svg';
// import { ReactComponent as SilderLfSvg } from '@/assets/images/sidepanel/silderLf.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as TrashSvg } from '@/assets/images/sidepanel/trash.svg';
import { ReactComponent as UnionSvg } from '@/assets/images/sidepanel/Union.svg';
import { ReactComponent as ArrowDownIcon } from '@/assets/images/svg/arrowDown-18.svg';
import { ReactComponent as ArrowRightIcon } from '@/assets/images/svg/arrowRight-18.svg';
import CreateFileMenu from '@/components/Desktop/CreateFileMenu/index';
import type { FileItem } from '@/constants/fileList.config';
import { FILE_LIST } from '@/constants/fileList.config';
import { useCreateFile } from '@/hooks/useCreateFile';
import { useCurrentPage } from '@/hooks/useCurrentPage';
import { RootType } from '@/model/File';
import type { SiderMenuItem } from '@/model/SiderMenuItem';
import type { DraggableItem } from '@/model/Space';
import { fm } from '@/modules/Locale';
import { useDragSpaceMenu } from '@/pages/pc/Space/components/dragTop/hooks/useDragSpaceMenu';
import { useUserCheckPoint } from '@/service/Me';
import { useCurrentFolderSpaceInfo } from '@/store/CurrentFolderSpaceInfo';
import { useFileStore } from '@/store/File';
import { useSelectedMenuItemStore } from '@/store/Sider';
import { useTemplateStore } from '@/store/TemplateLib';

import { QuickAccessList } from '../QuickAccess';
import styles from './index.less';

export type MenuItem = Required<MenuProps>['items'][number] & {
  draggable?: boolean;
  children?: MenuItem[];
  icon?: React.ReactNode | string;
  extra?: React.ReactNode;
};

export const getFirstPath = (pathname: string, rootType?: RootType) => {
  const parts = pathname.split('/');
  if (pathname.includes('space') && parts.length > 2) {
    return [`${parts[1]}`, `${parts[1]}/${parts[2]}`];
  }
  if (parts.length > 1) {
    const result = parts[1];
    if (result === 'folder') {
      if (rootType === RootType.Space) {
        return 'space';
      } else {
        return 'desktop';
      }
    }
    return result;
  } else {
    return '';
  }
};

function iskeyandlabel(value: MenuItem): value is SiderMenuItem {
  return (value as SiderMenuItem).label !== undefined && (value as SiderMenuItem).key !== undefined;
}

const SiderMenu: React.FC = () => {
  //国际化
  const siderMenuCreactText = fm('SiderMenu.siderMenuCreactText');
  const siderMenuRecentText = fm('SiderMenu.siderMenuRecentText');
  const siderMenuShareText = fm('SiderMenu.siderMenuShareText');
  const siderMenuFavoritesText = fm('SiderMenu.siderMenuFavoritesText');
  const siderMenuDesktopText = fm('SiderMenu.siderMenuDesktopText');
  const siderMenuSpaceText = fm('SiderMenu.siderMenuSpaceText');
  const siderMenuWorkActivity = fm('SiderMenu.siderMenuWorkActivity');
  const siderMenuTrashText = fm('SiderMenu.siderMenuTrashText');
  const siderMenuBusinessText = fm('SiderMenu.siderMenuBusinessText');

  const rootType = useFileStore((state) => state.rootType);

  // 获取当前的Guid
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || 'Desktop';

  const items: MenuItem[] = useMemo(() => {
    return [
      {
        key: 'recent',
        icon: <RecentSvg />,
        label: siderMenuRecentText,
      },
      {
        key: 'share',
        icon: <ShareSvg />,
        label: siderMenuShareText,
      },
      {
        key: 'favorites',
        icon: <FavoritesSvg />,
        label: siderMenuFavoritesText,
      },
      {
        key: 'workActivity',
        icon: <WorkActivitySvg />,
        label: siderMenuWorkActivity,
      },
      { type: 'divider', key: 'divider1' },
      { key: 'desktop', icon: <DesktopSvg />, label: siderMenuDesktopText },
      { key: 'space', icon: <SpaceSvg />, label: siderMenuSpaceText },
      { type: 'divider', key: 'divider2' },
      {
        key: 'quickAccess',
        extra: <QuickAccessList />,
      },
      { type: 'divider', key: 'divider3' },
      { key: 'trash', icon: <TrashSvg />, label: siderMenuTrashText },
    ];
  }, [
    siderMenuDesktopText,
    siderMenuFavoritesText,
    siderMenuRecentText,
    siderMenuShareText,
    siderMenuSpaceText,
    siderMenuTrashText,
    siderMenuWorkActivity,
  ]);
  const [collapsed] = useState(false);
  const { setTemplateProp } = useTemplateStore((state) => state);

  const setMenus = useSelectedMenuItemStore((state) => state.setMenus);
  const menus = useSelectedMenuItemStore((state) => state.menus);
  const getSpacePinListFetch = useSelectedMenuItemStore((state) => state.getSpacePinListFetch);
  const sideMenuDataList: SiderMenuItem[] = menus.filter(iskeyandlabel);
  const [isOpenTypePop, setIsOpenTypePop] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const { createFile } = useCreateFile(guid);
  const { dragState, handleDragStart, handleDragOver, handleDrop, handleDragEnd } = useDragSpaceMenu<
    MenuItem & DraggableItem,
    HTMLLIElement
  >();

  // 获取当前地址
  const location = useLocation();
  const pathName = location.pathname;
  const selectedKeys = getFirstPath(pathName, rootType);
  const setSelectedMenuItem = useSelectedMenuItemStore((state) => state.setSelectedMenuItem);
  const silderRef = useRef<HTMLDivElement>(null);
  // const [showButton, setShowButton] = useState(false);

  const showTemplateModel = () => {
    setIsOpenTypePop(false);
    setTemplateProp({ parentGuid: '', isShowTemplateLib: true, propType: 'list' });
  };

  const { isNoFeaturePoint } = useUserCheckPoint();

  const getFileMenuList = useMemo(() => {
    const tempArr = FILE_LIST[0].map((item: FileItem) => {
      item.hidden = item.supportType && isNoFeaturePoint(item.supportType || '');
      return item;
    });
    const fileMenuList: FileItem[][] = FILE_LIST;
    fileMenuList[0] = tempArr; //数组第一个参数做权限处理
    return fileMenuList;
  }, [FILE_LIST, isNoFeaturePoint]);

  const content = (
    <CreateFileMenu
      fileMenuList={getFileMenuList}
      showTemplateModel={showTemplateModel}
      onClose={(item: FileItem) => {
        if (item?.disabled || item?.children?.length) {
          return;
        }
        setIsOpenTypePop(false);
        //开始创建文件
        createFile(item?.value || 'newdoc');
      }}
    />
  );

  const disableContent = (
    <div className={'disableContent'}>
      <div className={'disableContentTitle'}>{fm('SiderMenu.createDisablePopText')}</div>
    </div>
  );

  // // 鼠标移动事件处理函数
  // const handleMouseMove = (event: MouseEvent) => {
  //   const topHeaderHeight: number = 52;
  //   if (silderRef.current) {
  //     silderRef.current.style.setProperty('--silder--y', `${event.clientY - topHeaderHeight}px`);
  //   }
  // };

  /**
   * 控制收缩侧边栏的按钮
   */
  // let timerId: NodeJS.Timeout | null = null;
  // const handleMouseenter = () => {
  //   timerId = setTimeout(() => {
  //     setShowButton(true);
  //   }, 500);
  // };
  // const handleMouseout = () => {
  //   if (timerId) {
  //     clearTimeout(timerId);
  //   }
  //   setShowButton(false);
  // };

  useEffect(() => {
    // 使用 useDebounce 对 handleMouseenter 进行防抖处理，延迟时间为50 毫秒
    // const debouncedMouseenter = debounce(handleMouseenter, 50);
    // const debouncedMouseout = debounce(handleMouseout, 50);
    getSpacePinListFetch();
    setMenus(items);
    setSelectedMenuItem(
      sideMenuDataList.find((item) => item.key === selectedKeys) || {
        key: 'recent',
        label: siderMenuRecentText,
      },
    );
    // const element = silderRef.current;
    // if (element) {
    //   // 为 ref 对应的 DOM 元素添加鼠标移动监听事件
    //   element.addEventListener('mousemove', handleMouseMove);
    //   element.addEventListener('mouseenter', debouncedMouseenter);
    //   element.addEventListener('mouseleave', debouncedMouseout);
    // }
    return () => {
      // if (timerId) {
      //   clearTimeout(timerId);
      // }
      // if (element) {
      //   // 组件卸载时，移除鼠标移动监听事件，避免内存泄漏
      //   element.removeEventListener('mousemove', handleMouseMove);
      //   element.removeEventListener('mouseenter', debouncedMouseenter);
      //   element.removeEventListener('mouseleave', debouncedMouseout);
      // }
    };
  }, []);
  //侧边栏点击事件
  const onClick = (e: MenuItem) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const path: any = e?.key;
    // 避免快速访问误操作
    if (path === 'quickAccess') return;
    history.push(`/${path}` || '/');
  };

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpenTypePop(newOpen);
  };

  const goManagement = () => {
    window.open('/enterprise/members'); // 默认跳转通讯录
  };

  const folderSpaceInfo = useCurrentFolderSpaceInfo((state) => state.folderSpaceInfo);
  const { isFolderPage, isSpaceFolderPage } = useCurrentPage();
  const canNotCreateChildFile = useMemo(() => {
    return (isFolderPage() || isSpaceFolderPage()) && !folderSpaceInfo.permissionsAndReasons.canCreateChildFile.value;
  }, [folderSpaceInfo, isFolderPage, isSpaceFolderPage]);
  const renderMenuItems = (items: MenuItem[] = []) => {
    return items.map((item: MenuItem, index: number) => {
      if (item?.type === 'divider') {
        return <Menu.Divider key={`${item.key}`} />;
      }

      // 处理包含 extra 属性的特殊菜单项（如快速访问）
      if (item?.extra) {
        return (
          <Menu.Item key={item?.key} className="quickAccessContainer" style={{ padding: 0 }}>
            {item.extra}
          </Menu.Item>
        );
      }

      if (item && Array.isArray(item.children) && item.children && item.children.length > 0) {
        return (
          <Menu.SubMenu key={item?.key} icon={item?.icon} title={item?.label} onTitleClick={(e) => onClick(e)}>
            {renderMenuItems(item?.children)}
          </Menu.SubMenu>
        );
      }
      //团队空间菜单
      return (
        <Menu.Item
          key={item?.key}
          className={classNames({
            thresholdLeft: dragState?.dropPosition === 'before' && dragState?.targetIndex === index,
            thresholdRight: dragState?.dropPosition === 'after' && dragState?.targetIndex === index,
            dragItem: dragState?.isDragging && dragState?.sourceIndex === index,
            dragItemSpace: true,
          })}
          draggable={!!item.draggable}
          style={{ paddingLeft: 0 }}
          {...(item.draggable ? { id: `dragMenu${index}` } : {})}
          onDragEnd={handleDragEnd}
          onDragOver={(e) => handleDragOver(e, index)}
          onDragStart={(e) => handleDragStart(e, item as any, index)}
          onDrop={(e) => handleDrop(e, index)}
        >
          <Flex align="center">
            <Flex align="center" style={{ marginRight: 12 }}>
              {item?.icon}
            </Flex>
            <Typography.Text ellipsis>{item?.label}</Typography.Text>
          </Flex>
        </Menu.Item>
      );
    });
  };

  const handleMenuOpenChange = (key: string) => {
    const newOpenKeys = [...openKeys];
    if (newOpenKeys.includes(key)) {
      const result = newOpenKeys.filter((item) => item !== key);
      setOpenKeys(result);
      return;
    }
    setOpenKeys((pre) => [...pre, key]);
  };

  const renderExpandIcon = (props: SubMenuProps) => {
    const { isOpen, eventKey } = props as SubMenuProps & { isOpen: boolean; eventKey: string };
    const expandIcon = isOpen ? <ArrowDownIcon /> : <ArrowRightIcon />;
    return (
      <span className={styles.expandIcon} onClick={() => handleMenuOpenChange(eventKey)}>
        {expandIcon}
      </span>
    );
  };

  return (
    <Sider className={styles.siderStyle} width={collapsed ? 83 : 240}>
      <div className={styles.creatDiv}>
        <Popover
          arrow={false}
          content={content}
          open={isOpenTypePop}
          placement="bottomLeft"
          trigger="click"
          onOpenChange={handleOpenChange}
        >
          <Popover content={canNotCreateChildFile ? disableContent : ''} placement="right">
            <Button
              className={classNames(styles.createBtn, 'sm-btn', 'sm-btn-normal-primary')}
              disabled={canNotCreateChildFile}
              onClick={() => setIsOpenTypePop(!isOpenTypePop)}
            >
              {collapsed ? <PlusOutlined className={styles.plusIcon} /> : siderMenuCreactText}
            </Button>
          </Popover>
        </Popover>
      </div>
      <div className={styles.menus}>
        <Menu
          className="my-side-menu"
          defaultOpenKeys={['dashboard']}
          expandIcon={renderExpandIcon}
          inlineCollapsed={collapsed}
          mode="inline"
          openKeys={openKeys}
          selectedKeys={Array.isArray(selectedKeys) ? selectedKeys : [selectedKeys]}
          onClick={onClick}
        >
          {renderMenuItems(menus)}
        </Menu>
      </div>
      <div className={styles.footer}>
        <Divider />
        <div className={styles.footerDiv}>
          <Tooltip placement="right" title={collapsed ? siderMenuBusinessText : ''}>
            <div className={styles.business} onClick={goManagement}>
              <UnionSvg />
              {collapsed ? null : <span className={styles.title}>{siderMenuBusinessText}</span>}
            </div>
          </Tooltip>
        </div>
      </div>
      <div ref={silderRef} className={styles.rightSilder} />
      {/* {showButton && silderRef.current
        ? ReactDOM.createPortal(
            <button
              className={`${styles.silderButton}  ${collapsed ? styles.rightSvg : ''}`}
              type="button"
              onClick={() => setCollapsed(!collapsed)}
            >
              <SilderLfSvg />
            </button>,
            silderRef.current,
          )
        : null} */}
    </Sider>
  );
};
export default React.memo(SiderMenu);
