import { Ava<PERSON>, Button, List, message, Space, Typography } from 'antd';
import type { AxiosResponse } from 'axios';

import { requestApplyPermission } from '@/api/applyPermission';
import type { NotificationWrapper, OtherProps, RoleApply } from '@/api/Message';
import { readAlon } from '@/api/Message';
import { ReactComponent as EnterpriseAvatarIcon } from '@/assets/images/svg/enterpriseAvatar.svg';
import UserCardPopover from '@/components/UserCardPopover';
import { fm } from '@/modules/Locale';
import { getFileIcon, useFormatTime } from '@/utils/file';

import styles from '../index.less';

export const MessageCard = ({ item, callback }: { item: NotificationWrapper; callback: () => void }) => {
  const addCollaboratorRoleMap = {
    '1': fm('ShareCollaboration.coauthor'),
    '2': fm('ShareCollaboration.coauthor'),
    '3': fm('ShareCollaboration.coauthor'),
    '4': fm('ShareCollaboration.admin'),
  };

  const actionMap: Record<number, (other?: OtherProps) => string> = {
    0: () => fm('MessageCenter.commented'), //'评论了',
    1: () => fm('MessageCenter.mentioned'), // '提到了你',
    2: (other?: OtherProps) =>
      fm('MessageCenter.addCollaborator', {
        invitedRole: other?.invitedRole ? addCollaboratorRoleMap[other?.invitedRole] : addCollaboratorRoleMap[1],
      }), // '添加你为协作者'
    3: () => fm('MessageCenter.setAdministrator'), // '将你设置为企业管理员',
    4: () => fm('MessageCenter.inviteJoinBusiness'), // '邀请你加入企业',
    5: () => fm('MessageCenter.newMembersJoin'), // '新成员加入',
    6: () => fm('MessageCenter.deleteDocument'), // '删除文档',
    7: () => fm('MessageCenter.remindsReviewTasks'), // '提醒你查看任务',
    8: () => fm('MessageCenter.liked'), // '点赞了',
    9: () => fm('MessageCenter.NotificationToDoChanges'), // '待办事项修改通知'：删除了你在
    10: () => fm('MessageCenter.mentionYou'), // '提到你',
    11: () => fm('MessageCenter.moveYouOfBusiness'), // '将你移出企业',
    12: () => fm('MessageCenter.handingOverBusinessToYou'), // '将企业移交给你',
    13: (other?: OtherProps) => fm('MessageCenter.companyNameChanged', { name: other?.name }), // '修改了企业名称“${name}”',
    14: () => fm('MessageCenter.openedBusinessLink'), // '打开企业邀请链接',
    15: () => fm('MessageCenter.closedBusinessLink'), // '关闭了企业邀请链接',
    16: () => fm('MessageCenter.taskReminders'), // '任务提醒',
    17: () => fm('MessageCenter.tableSelectionReminders'), // '表格选区提醒',
    18: () => fm('MessageCenter.changeUserConfig'), // '修改了帐户信息',
    20: () => fm('MessageCenter.systemNotifications'), // '系统通知',
    21: () => fm('MessageCenter.application'), // '申请', //  申请权限
    22: () => fm('MessageCenter.fileRecovered'), // 文件被恢复了
    23: () => fm('MessageCenter.updateFile'), // 更新了文档
    24: () => fm('MessageCenter.formNotifications'), // 表单通知
    25: () => fm('MessageCenter.quotaNotifications'), // 配额报警通知
    26: () => fm('MessageCenter.memberReminders'), // 例如会员到期续费提醒、文件协作达到上限升级账号提醒
    27: () => fm('MessageCenter.fileAction'), // 文件的新增、修改、删除通知
  };

  const i18nText = {
    markRead: fm('MessageCenter.markRead'),
    join: fm('MessageCenter.join'),
    discussion: fm('MessageCenter.discussion'),
    permissions: fm('MessageCenter.permissions'),
    dateArrived: fm('MessageCenter.dateArrived'),
    allow: fm('Notification.allow'),
    processed: fm('Notification.processed'),
    processedByAnother: fm('Notification.processedByAnother'),
    noModifyRolePermission: fm('Notification.noModifyRolePermission'),
    fileNotFound: fm('Notification.fileNotFound'),
    userNotAdmin: fm('Notification.userNotAdmin'),
  };

  const readMessage = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    readAlon(id)
      .then(() => {
        callback();
      })
      .catch((err) => {
        message.error(err.data?.msg);
      });
  };

  const approvedPermission = async (id: number) => {
    try {
      await requestApplyPermission(id);
    } catch (error) {
      const err = error as AxiosResponse;
      if (err.status !== 200) {
        message.error(err.data.msg);
      }
    } finally {
      callback(); // 失败也会申请成功
    }
  };

  const { formatTime } = useFormatTime();

  const UserName = () => (
    <div className={styles.messageUserName}>
      <Typography.Text ellipsis={{ tooltip: item.notification?.user.name }} style={{ maxWidth: 40 }}>
        {item.notification?.user.name}
      </Typography.Text>
    </div>
  );

  const UserAvatar = () => (
    <UserCardPopover userId={item.notification?.user.id}>
      <Avatar size={32} src={item.notification?.user?.avatar} />
    </UserCardPopover>
  );

  const FileAvatar = ({ type, isSpace }: { type?: string; isSpace: boolean }) => {
    const newType = type ? type : 'folder';
    return <Avatar shape="square" size={32} src={getFileIcon({ type: newType, isSpace })} />;
  };

  const FileName = ({ maxWidth }: { maxWidth: number }) => (
    <span className={styles.messageFileName}>
      <Typography.Text ellipsis={{ tooltip: item.notification.fileName }} style={{ maxWidth: maxWidth }}>
        {`「${item.notification.fileName}」`}
      </Typography.Text>
    </span>
  );

  const CreatedAt = () => (
    <span className={styles.messageCreatedAt}>{formatTime(new Date(item.createdAt).getTime())}</span>
  );

  const ActionText = ({ maxWidth, value }: { maxWidth: number; value?: OtherProps }) => (
    <span className={styles.messageType}>
      <Typography.Text
        ellipsis={{ tooltip: actionMap[item.notification.msgType](value) }}
        style={{ maxWidth: maxWidth }}
      >
        {actionMap[item.notification.msgType](value)}
      </Typography.Text>
    </span>
  );

  const MarkReadButton = ({ isRead }: { isRead: boolean }): JSX.Element | null => {
    if (isRead) return null;
    {
      return (
        <Button className={styles.markRead} size="small" type="link" onClick={(e) => readMessage(e, item.id)}>
          {i18nText.markRead}
        </Button>
      );
    }
  };

  const RoleApplyText = ({ roleApply }: { roleApply?: RoleApply }) => {
    let text = '';
    const role = roleApply?.role;
    switch (role) {
      case 'reader':
        text = fm('MessageCenter.permissionReadable');
        break;
      case 'commentator':
        text = fm('MessageCenter.permissionCommentable');
        break;
      case 'editor':
        text = fm('MessageCenter.permissionEditable');
        break;
      default:
        text = fm('MessageCenter.permissionEditable');
    }
    return (
      <div>
        <span className={styles.messageRole}>{text}</span>
        <span className={styles.messagePermissions}>{i18nText.permissions}</span>
      </div>
    );
  };

  const NotificationComment = ({ comment, rows = 1 }: { comment?: string; rows?: number }) => {
    if (!comment) return null;
    return (
      <div className={styles.messageComment}>
        <Typography.Paragraph ellipsis={{ rows, tooltip: comment }} style={{ width: '100%' }}>
          {`“ ${comment} ”`}
        </Typography.Paragraph>
      </div>
    );
  };

  const RequestforPermission = ({ roleApply }: { roleApply?: RoleApply }): JSX.Element | null => {
    if (!roleApply) return null;
    switch (roleApply.status) {
      case '':
        return (
          <Button
            color="default"
            size="small"
            variant="solid"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              approvedPermission(roleApply.id);
            }}
          >
            {i18nText.allow}
          </Button>
        );
      case 'ROLE_APPLY_IS_APPROVED':
        return <div className={styles.processedBtn}>{i18nText.processed}</div>;
      case 'ROLE_APPLY_IS_APPROVED_BY_ANOTHER':
        return <div className={styles.processedBtn}>{i18nText.processedByAnother}</div>;
      case 'NO_MODIFY_ROLE_PERMISSION':
        return <div className={styles.processedBtn}>{i18nText.noModifyRolePermission}</div>;
      case 'FILE_NOT_FOUND':
        return <div className={styles.processedBtn}>{i18nText.fileNotFound}</div>;
      case 'USER_NOT_ADMIN':
        return <div className={styles.processedBtn}>{i18nText.userNotAdmin}</div>;
      default:
        return null;
    }
  };

  return (
    <>
      {/* 评论了 */}
      {[0].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Paragraph ellipsis={{ rows: 2, tooltip: item.notification.msg }} style={{ width: '100%' }}>
                  {`“ ${item.notification.msg} ”`}
                </Typography.Paragraph>
              </div>
              <Space>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={50} />
              <FileName maxWidth={120} />
            </Space>
          }
        />
      ) : null}
      {/* 提到了你 */}
      {[1].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <FileName maxWidth={120} />
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} />
            </Space>
          }
        />
      ) : null}
      {/* 添加你为协作者 */}
      {[2].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<FileAvatar isSpace={item.notification.file.isSpace} type={item.notification.file.type} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <FileName maxWidth={120} />
              <Space>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} value={{ invitedRole: item.notification.invitedRole || 2 }} />
            </Space>
          }
        />
      ) : null}
      {/* 设置企业管理员,修改了企业名称 */}
      {[3, 13].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText
                maxWidth={100}
                value={item.notification.msgType === 3 ? { name: item.notification.user?.team?.name || '' } : undefined}
              />
            </Space>
          }
        />
      ) : null}
      {/* 企业邀请 */}
      {[4].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.user?.team?.name }} style={{ width: 120 }}>
                  {`「${item.notification.user?.team?.name}」`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <CreatedAt />
                <Button size="small" type="text">
                  {i18nText.join}
                </Button>
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} />
            </Space>
          }
        />
      ) : null}
      {/* 提醒你查看任务 */}
      {[7].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <FileName maxWidth={160} />
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} />
            </Space>
          }
        />
      ) : null}
      {/* 点赞 */}
      {[8].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <div className={styles.messageTarget}>
                <Typography.Text ellipsis={{ tooltip: item.notification.msg }} style={{ width: 120 }}>
                  {`”${item.notification.msg}”`}
                </Typography.Text>
              </div>
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <>
              <Space>
                <UserName />
                <ActionText maxWidth={50} />
              </Space>
              <Space>
                <FileName maxWidth={100} />
                <span className={styles.messageType}>
                  <Typography.Text style={{ width: 20 }}>{i18nText.discussion}</Typography.Text>
                </span>
              </Space>
            </>
          }
        />
      ) : null}
      {/* 删除了你在 */}
      {[9].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={65} />
              <FileName maxWidth={60} />
              <span className={styles.messageType}>
                <Typography.Text ellipsis={{ tooltip: i18nText.dateArrived }} style={{ maxWidth: 40 }}>
                  {i18nText.dateArrived}
                </Typography.Text>
              </span>
            </Space>
          }
        />
      ) : null}
      {/* 提醒你查看任务 */}
      {[16].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              {/* 具体内容 */}
              <FileName maxWidth={120} />
              <Space>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} />
            </Space>
          }
        />
      ) : null}
      {/* 修改了帐户信息，表格选区提醒 */}
      {[17, 18].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={
            item.notification.msgType === 17 ? (
              <Avatar size={32} src={item.notification?.user?.avatar} />
            ) : (
              <EnterpriseAvatarIcon />
            )
          }
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <div className={styles.messageTarget}>
                <Typography.Paragraph ellipsis={{ rows: 2, tooltip: item.notification.msg }} style={{ width: '100%' }}>
                  {item.notification.msgType === 17 ? `「${item.notification?.fileName}」` : item.notification.msg}
                </Typography.Paragraph>
              </div>
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={120} />
            </Space>
          }
        />
      ) : null}
      {/* 申请权限 */}
      {[21].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<UserAvatar />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space className={styles.messageHover}>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
              <RequestforPermission roleApply={item.notification.roleApply} />
            </Space>
          }
          title={
            <div>
              <Space>
                <UserName />
                <span className={styles.messageType}>
                  {/* 申请  */}
                  <ActionText maxWidth={40} />
                  <FileName maxWidth={120} />
                </span>
              </Space>
              <RoleApplyText roleApply={item.notification?.roleApply} />
              <NotificationComment comment={item.notification?.roleApply?.comment} />
            </div>
          }
        />
      ) : null}
      {/* 未添加的消息类型 全部展示，避免有消息不展示，0630 */}
      {[5, 6, 10, 11, 12, 14, 15, 19, 20, 22, 23, 24, 25, 26, 27].includes(item.notification.msgType) ? (
        <List.Item.Meta
          avatar={<Avatar size={32} src={item.notification?.user?.avatar} />}
          className={`${styles.messageBg} ${item.isRead ? null : styles.messageUnRead}`}
          description={
            <Space direction="vertical">
              <Space>
                <CreatedAt />
                <MarkReadButton isRead={item.isRead} />
              </Space>
            </Space>
          }
          title={
            <Space>
              <UserName />
              <ActionText maxWidth={100} />
            </Space>
          }
        />
      ) : null}
    </>
  );
};
