.searchList {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.searchListLeft {
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.searchHeader {
  display: flex;
  align-items: center;
  margin: 12px 24px 8px;
  justify-content: space-between;
  font-size: 12px;

  .default {
    line-height: 20px;
    padding: 2px 7px;
    font-weight: 400;
    cursor: pointer;

    :global {
      .ant-typography {
        font-size: 12px;
        max-width: 60px;
      }
    }
  }

  .primary {
    line-height: 20px;
    padding: 2px 7px;
    font-weight: 400;
    cursor: pointer;
    background: var(--theme-datepicker-color-cell-active-range-bg);

    :global {
      .ant-typography {
        font-size: 12px;
        max-width: 60px;
        color: var(--theme-button-color-primary);
      }
    }
  }
}

.searchFieldsStyle {
  line-height: 24px;

  :global {
    .ant-radio-button-label {
      font-size: 12px;
    }

    .ant-radio-button-wrapper:first-child {
      border-start-start-radius: 4px;
      border-end-start-radius: 4px;
    }

    .ant-radio-button-wrapper:last-child {
      border-start-end-radius: 4px;
      border-end-end-radius: 4px;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      background-color: var(--theme-card-info-bg-active-border);
    }

    .ant-radio-button-wrapper {
      color: var(--theme-text-color-secondary);
      background: var(--theme-table-color-header-gray-bg);
      border-color: var(--theme-table-color-header-gray-bg);
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: var(--theme-text-color-default);
      border-color: var(--theme-card-info-bg-active-border);
      background: var(--theme-checkbox-color-check);
    }
  }
}

.searchSelectList {
  display: flex;
  padding: 4px 16px;

  &:hover {
    cursor: pointer;
    background: var(--theme-menu-color-bg-hover);
  }
}

.searchSelectRight {
  padding-left: 24px;
  display: flex;
  flex: 1;
  flex-direction: column;
}

.searchSelectHighlight {
  line-height: 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-basic-color-primary);
}

.searchSelectContent {
  line-height: 20px;

  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--theme-text-color-secondary);
    }
  }
}

.searchSelectLast {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  line-height: 20px;
}

.searchSelectLastLeft {
  color: var(--theme-text-color-medium);
  display: flex;

  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--theme-text-color-medium);
    }
  }
}

.searchUserName {
  :global {
    .ant-typography {
      line-height: 20px;
      max-width: 50px;
    }
  }
}

.searchUserName::after {
  content: '•';
  padding: 0 4px;
}

.searchSelectLastLegend {
  line-height: 20px;
  display: flex;
  padding-right: 4px;
}

.searchSelectTarget {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: var(--transparency90);

  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--transparency90);
      max-width: 100px;
    }
  }

  &:hover,
  span:hover {
    color: var(--theme-link-button-color);
  }
}

.searchSelectLastRight {
  display: flex;
  color: var(--theme-icon-info-color);
}

.searchSelectUpdatedAt {
  :global {
    .ant-typography {
      color: var(--theme-icon-info-color);
      max-width: 70px;
      font-size: 12px;
    }
  }
}

.searchSelectLastUpdatedUser {
  color: var(--theme-icon-info-color);
  max-width: 60px;
  font-size: 12px;
  padding: 0 4px;

  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--theme-icon-info-color);
      max-width: 50px;
    }
  }
}

.searchSelectLastUpdated {
  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--theme-icon-info-color);
      max-width: 50px;
    }
  }
}

.searchRight {
  width: 124px;
  padding: 14px 16px;
  background: var(--theme-input-color-bg);

  :global {
    .ant-menu-item {
      height: 24px;
      line-height: 24px;
      padding-left: 10px;
      font-size: 12px;
    }

    .ant-menu-item-selected {
      color: var(--theme-datepicker-color-cell-active-bg);
      background: var(--theme-datepicker-color-cell-active-range-bg);
    }

    .ant-form-item .ant-form-item-label {
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
    }

    .ant-radio-wrapper {
      line-height: 20px;
      font-size: 12px;
    }

    .ant-radio-wrapper:not(:last-child) {
      padding-bottom: 4px;
    }

    .ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active {
      background-color: unset;
    }
  }
}

.searchListRightMenu {
  :global {
    .ant-menu-light.ant-menu-root.ant-menu-vertical {
      border: none;
      background: border-box;
    }
  }
}

.searchMoreMenu {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  padding: 0 10px;
  margin: 4px;
  cursor: pointer;
  color: var(--theme-text-color-secondary);
  border-radius: 4px;

  &:hover {
    background-color: var(--theme-text-button-color-hover);
  }
}

.createdByOptions {
  display: flex;
  flex-direction: column;

  :global {
    span:nth-child(1) {
      font-size: 13px;
      line-height: 20px;
    }

    span:nth-child(2) {
      font-size: 12px;
      line-height: 20px;
      color: var(--theme-text-color-disabled);
    }

    .ant-typography {
      max-width: 84px;
    }
  }
}
