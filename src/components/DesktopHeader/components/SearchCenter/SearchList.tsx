import { Avatar, Form, Menu, Radio, Select, Space, Typography } from 'antd';
import type { MenuItemType } from 'antd/es/menu/interface';
import { debounce } from 'lodash';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'umi';

import { getRecentContact } from '@/api/Collaboration';
import type { ContactData } from '@/api/Collaboration.type';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as FolderSvg } from '@/assets/images/svg/folder.svg';
import { LoadingOverlay } from '@/components/Loading';
import UserCardPopover from '@/components/UserCardPopover';
import { RootType } from '@/model/File';
import { fm } from '@/modules/Locale';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
import { useCurrentFolderSpaceInfo } from '@/store/CurrentFolderSpaceInfo';
import { useFileStore } from '@/store/File';
import { useMeStore } from '@/store/Me';
import { getFileIcon, useFormatTime, usePathAnalysis } from '@/utils/file';

import { HybridHighlighter } from '../TextHeightLight';
import IconRangePickerForm from './IconRangePickerForm';
import { SearchEmpty } from './SearchEmpty';
import styles from './SearchList.less';
import type { GotoArg, SearchOptions } from './types';
export const SearchList = ({
  options,
  value,
  height,
  advanced,
  onValuesChange,
  gotoFolder,
  loading,
}: {
  options: SearchOptions[];
  value?: string;
  height: number;
  advanced: boolean;
  onValuesChange: any;
  gotoFolder: ({ url, type }: GotoArg) => void;
  loading: boolean;
}) => {
  const i18nText = {
    alLocations: fm('SearchCenter.alLocations'),
    siderMenuDesktopText: fm('SiderMenu.siderMenuDesktopText'),
    currentSpace: fm('SearchCenter.currentSpace'),
    forTitle: fm('SearchCenter.forTitle'),
    forContent: fm('SearchCenter.forContent'),
    fileType: `${fm('SearchCenter.fileType')}：`,
    folder: fm('File.folder'),
    newdoc: fm('File.newdoc'),
    mosheet: fm('File.mosheet'),
    table: fm('File.table'),
    presentation: fm('File.presentation'),
    img: fm('File.png'),
    board: fm('File.board'),
    mindMap: fm('File.mindMap'),
    pdf: 'PDF',
    form: fm('File.form'),
    uploadedFiles: fm('SearchCenter.uploadedFiles'),
    all: fm('SearchCenter.all'),
    used: fm('SearchCenter.used'),
    onlyShare: fm('SearchCenter.onlyShare'),
    createdBy: `${fm('useFileDetail.creator')}：`,
    created: `${fm('File.createdAt')}：`,
    searchFields: `${fm('SearchCenter.searchFields')}：`,
    category: `${fm('SearchCenter.category')}：`,
    searchFieldsAll: fm('SearchCenter.searchFieldsAll'),
    searchFieldsName: fm('SearchCenter.searchFieldsName'),
    shearchCreator: fm('SearchCenter.shearchCreator'),
    more: fm('File.more'),
    me: fm('SearchCenter.me'),
  };
  const [form] = Form.useForm();

  const rootType = useFileStore((state) => state.rootType);

  const location = useLocation();

  const pathName = location.pathname;

  const fileType = Form.useWatch('fileType', form);

  const searchFields = Form.useWatch('searchFields', form);

  const [searchFieldsDefault, setSearchFieldsDefault] = useState<string>(searchFields || 'null');

  const [searchFile, setSearchFile] = useState('all');

  const { guid } = usePathAnalysis(location.pathname);

  const [creatorOptions, setCreatorOptions] = useState<ContactData[]>([]);

  const [selectLoading, setSelectLoading] = useState(false);

  const [collapse, setCollapse] = useState(false);

  const fetchRef = useRef(0);

  const folderSpaceInfo = useCurrentFolderSpaceInfo((state) => state.folderSpaceInfo);

  const { formatTime } = useFormatTime();

  const me = useMeStore((state) => state.me);

  const changeSearchFile = (key: string) => {
    setSearchFile(key);
  };

  const fetchOptions = (keyword?: string) => {
    fetchRef.current += 1;
    const fetchId = fetchRef.current;

    setCreatorOptions([]);
    setSelectLoading(true);

    getRecentContact(keyword)
      .then((res) => {
        if (fetchId === fetchRef.current) {
          const { results } = res.data;

          setCreatorOptions(results);
        }
      })
      .finally(() => {
        setSelectLoading(false);
      });
  };

  const debouncedFetch = debounce(fetchOptions, 300);

  const handleSearch = (value?: string) => {
    debouncedFetch(value);
  };

  const menuItems: MenuItemType[] = useMemo(() => {
    const baseItems: MenuItemType[] = [
      { key: 'all', label: i18nText.all },
      { key: FileIconType.Folder, label: i18nText.folder },
      { key: 'modoc,newdoc,document,docx,wps', label: i18nText.newdoc },
      { key: 'spreadsheet,sheet,mosheet,xls', label: i18nText.mosheet },
      { key: FileIconType.Table, label: i18nText.table },
      { key: 'presentation,slide,ppt', label: i18nText.presentation },
    ];

    if (!collapse) return baseItems;

    return [
      ...baseItems,
      { key: 'xmind,mindmap', label: i18nText.mindMap },
      { key: FileIconType.Board, label: i18nText.board },
      { key: FileIconType.Form, label: i18nText.form },
      { key: FileIconType.Image, label: i18nText.img },
      { key: FileIconType.Pdf, label: i18nText.pdf },
      { key: 'img,pdf,xls,docx,ppt,mp3,zip,mp4,wps,xmind,unknown', label: i18nText.uploadedFiles },
    ];
  }, [collapse]);

  const getFirstPath = (): string => {
    const parts = pathName.split('/');
    if (parts.length >= 1) {
      return parts[1];
    } else {
      return '';
    }
  };

  const changPositon = (key: string): void => {
    changeSearchFile(key);
    const filter: { desktop: boolean; ancestorGuids?: string[]; spaceGuids?: string[] } = {
      desktop: false,
    };
    if (key === 'all') {
      filter.desktop = false;
    }
    if (key === 'desktop') {
      filter.desktop = true;
    }
    if (key === 'space') {
      if (guid) {
        filter.desktop = false;
        // 区分团队空间 or 子文件夹
        if (rootType === RootType.Space) {
          filter.spaceGuids = [folderSpaceInfo.guid];
        } else {
          filter.ancestorGuids = [folderSpaceInfo.guid];
        }
      }
    }
    if (key === 'folder') {
      if (guid) {
        filter.desktop = false;
        filter.ancestorGuids = [guid];
      }
    }
    if (advanced) {
      onValuesChange({ ...form.getFieldsValue(), ...filter });
    } else {
      onValuesChange({ ...filter, searchFields: searchFieldsDefault });
    }
  };

  const filterButtonOptons: { label: string; key: string }[] = useMemo(() => {
    /** 所有位置 */
    const all = {
      label: i18nText.alLocations,
      key: 'all',
    };
    /** 我的桌面 */
    const desktop = {
      label: i18nText.siderMenuDesktopText,
      key: 'desktop',
    };
    /** 文件夹名字 */
    const folder = {
      label: folderSpaceInfo.name,
      key: 'folder',
    };

    /** 当前空间 */
    const space = {
      label: i18nText.currentSpace,
      key: 'space',
    };

    const firstPath = getFirstPath();

    if (firstPath === 'desktop') {
      if (guid) {
        return [all, desktop, folder];
      } else {
        return [all, desktop];
      }
    } else if (firstPath === 'space') {
      if (guid) {
        return [all, space];
      } else {
        return [all];
      }
    } else if (firstPath === 'folder') {
      if (rootType === RootType.Space) {
        return [all, space, folder];
      } else {
        return [all, desktop, folder];
      }
    } else {
      return [all];
    }
  }, [guid, rootType]);

  useEffect(() => {
    if (!advanced) {
      setSearchFieldsDefault(searchFieldsDefault);

      changPositon(searchFile);
    }
  }, [advanced]);

  return (
    <div className={styles.searchList}>
      <div className={styles.searchListLeft}>
        <div className={styles['searchHeader']}>
          <Space>
            {filterButtonOptons.map((item) => {
              return (
                <div
                  key={item.key}
                  className={searchFile === item.key ? styles['primary'] : styles['default']}
                  onClick={() => changPositon(item.key)}
                >
                  <Typography.Text
                    ellipsis={{
                      tooltip: item.label,
                    }}
                  >
                    {item.label}
                  </Typography.Text>
                </div>
              );
            })}
          </Space>
          <div className={styles.searchFieldsStyle}>
            <Radio.Group
              buttonStyle="outline"
              defaultValue="null"
              size="small"
              value={advanced ? searchFields : searchFieldsDefault}
              onChange={(e) => {
                const radioValue = e.target.value;
                if (advanced) {
                  form.setFieldValue('searchFields', radioValue);
                } else {
                  setSearchFieldsDefault(radioValue);
                }
                onValuesChange({ ...form.getFieldsValue(), searchFields: radioValue });
              }}
            >
              <Radio.Button type="text" value="name">
                {i18nText.forTitle}
              </Radio.Button>
              <Radio.Button type="text" value="null">
                {i18nText.forContent}
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>
        {options.length ? (
          <OverlayScrollbarsComponent
            options={{
              scrollbars: {
                autoHide: 'scroll', // 滚动时隐藏滚动条
                clickScroll: true, // 点击轨道滚动
              },
            }}
            style={{ overflowY: 'scroll', height: height - 108, width: '100%' }}
          >
            {options.map((data: SearchOptions, index: number) => {
              return (
                <div
                  key={index}
                  className={styles['searchSelectList']}
                  onClick={() => gotoFolder({ url: data.url, type: data.type })}
                >
                  <div className={styles['searchSelectLeft']}>
                    <img height={24} src={getFileIcon({ type: data.type, isSpace: data.isSpace })} width={24} />
                  </div>
                  <div className={styles['searchSelectRight']}>
                    <div className={styles['searchSelectHighlight']}>
                      <Typography.Text
                        ellipsis={{ tooltip: data.highlight }}
                        style={{ maxWidth: advanced ? 300 - 156 : 300 }}
                      >
                        <HybridHighlighter searchTerm={value} text={data.highlight} />
                      </Typography.Text>
                    </div>
                    {data.content && (
                      <div className={styles['searchSelectContent']}>
                        <Typography.Text
                          ellipsis={{ tooltip: data.content }}
                          style={{ maxWidth: advanced ? 480 - 156 : 480 }}
                        >
                          <HybridHighlighter searchTerm={value} text={data.content} />
                        </Typography.Text>
                      </div>
                    )}
                    <div className={styles['searchSelectLast']}>
                      <div className={styles['searchSelectLastLeft']}>
                        <span className={styles['searchUserName']}>
                          <Typography.Text className={styles.footerUser} ellipsis={{ tooltip: data.user.name }}>
                            {data.user.name}
                          </Typography.Text>
                        </span>
                        <span
                          className={styles['searchSelectTarget']}
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            gotoFolder({ url: data.target?.url, type: data.type });
                          }}
                        >
                          <span className={styles['searchSelectLastLegend']}>
                            {data.target?.isSpace ? <SpaceSvg /> : <FolderSvg />}
                          </span>
                          <Typography.Text ellipsis={{ tooltip: data.target?.name }}>
                            {data.target?.name}
                          </Typography.Text>
                        </span>
                      </div>
                      <div className={styles['searchSelectLastRight']}>
                        <span className={styles['searchSelectUpdatedAt']}>
                          <Typography.Text ellipsis={{ tooltip: formatTime(data.updatedAt) }}>
                            {formatTime(data.updatedAt)}
                          </Typography.Text>
                        </span>
                        <span className={styles['searchSelectLastUpdatedUser']}>
                          <Typography.Text ellipsis={{ tooltip: data.updatedUser }}>
                            {me.id === data.updatedBy ? i18nText.me : data.updatedUser}
                          </Typography.Text>
                        </span>
                        <span className={styles['searchSelectLastUpdated']}>
                          <Typography.Text ellipsis={{ tooltip: fm('SearchCenter.update') }}>
                            {fm('SearchCenter.update')}
                          </Typography.Text>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </OverlayScrollbarsComponent>
        ) : (
          <LoadingOverlay loading={loading}>
            <SearchEmpty />
          </LoadingOverlay>
        )}
      </div>

      {advanced && (
        <div className={styles['searchRight']}>
          <Form
            form={form}
            initialValues={{
              fileType: 'all',
              category: 'all',
              searchFields: 'null',
            }}
            layout="vertical"
            size="small"
            onValuesChange={(changedValues, allValues) => {
              onValuesChange(allValues);
            }}
          >
            <Form.Item className={styles.searchListRightMenu} colon={true} label={i18nText.fileType} name="fileType">
              <Menu
                getPopupContainer={(node) => node.parentNode as HTMLElement}
                items={menuItems}
                mode="vertical"
                selectedKeys={fileType}
                onClick={({ key }) => {
                  form.setFieldValue('fileType', key);
                  onValuesChange({ ...form.getFieldsValue(), fileType: key });
                }}
              />
              {!collapse && (
                <div className={styles.searchMoreMenu} onClick={() => setCollapse(!collapse)}>
                  {i18nText.more}
                </div>
              )}
            </Form.Item>
            <Form.Item label={i18nText.category} name="category">
              <Radio.Group>
                <Radio value="all">{i18nText.all}</Radio>
                <Radio value="used">{i18nText.used}</Radio>
                <Radio value="shared">{i18nText.onlyShare}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label={i18nText.searchFields} name="searchFields">
              <Radio.Group onChange={(e) => setSearchFieldsDefault(e.target.value)}>
                <Radio value="null">{i18nText.searchFieldsAll}</Radio>
                <Radio value="name">{i18nText.searchFieldsName}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label={i18nText.createdBy} name="createdBy">
              <Select
                allowClear
                showSearch
                className={'searchSelectDropdown'}
                fieldNames={{
                  label: 'name',
                  value: 'id',
                }}
                filterOption={false}
                getPopupContainer={() => document.getElementById('advance-search-content')!}
                loading={selectLoading}
                optionRender={(option) => (
                  <Space>
                    <UserCardPopover userId={option.data.id}>
                      <Avatar size={32} src={option.data.avatar} />
                    </UserCardPopover>
                    <div className={styles.createdByOptions}>
                      <Typography.Text ellipsis={{ tooltip: option.data.name }}>{option.data.name}</Typography.Text>
                      <Typography.Text ellipsis={{ tooltip: option.data.email }}>{option.data.email}</Typography.Text>
                    </div>
                  </Space>
                )}
                options={creatorOptions}
                placeholder={i18nText.shearchCreator}
                suffixIcon={false}
                onFocus={() => handleSearch()}
                onSearch={handleSearch}
              />
            </Form.Item>
            <Form.Item label={i18nText.created} name="created">
              <IconRangePickerForm />
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  );
};
