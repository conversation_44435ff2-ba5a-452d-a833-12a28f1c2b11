.iconPickerItem {
  display: flex;
  justify-content: space-between;
}

.searchPickerButton {
  background: var(--theme-status-color-bg-disabled);
}

.buttonDirection {
  transform: rotate(180deg);
}

.iconPicker {
  width: 0;
  height: 0;
  overflow: hidden;
  z-index: 3;
  transform: scale(0.1);
}

.pickerPosition {
  display: flex;
  position: absolute;
  left: -380px !important; // 覆盖行内样式
  :global {
    .ant-picker-panel-container {
      width: 488px;
    }

    .ant-picker-date-panel {
      width: 244px;
    }

    .ant-picker-body {
      width: 240px;
      padding: 12px;
    }

    .ant-picker-content .ant-picker-cell-in-range .ant-picker-cell-inner {
      color: var(--theme-datepicker-color-cell-active-bg);
    }

    .ant-picker-cell::before {
      margin: 0 4px;
      border-radius: 2px;
    }
  }
}

.searchRangeTime {
  color: var(--theme-text-color-disabled);
}
