.mainCardTable {
  background: var(--theme-layout-color-bg-new-page);
  width: 100%;

  .iconButtonActive {
    background: var(--theme-custom-button-icon-select);
    border-radius: 4px;
  }

  .viewContainer {
    width: 100%;
    display: flex;
    flex-direction: row;
    position: absolute;
    left: 0;
    height: 100%;
  }

  .viewWrapper {
    box-sizing: border-box;
    flex: 1 1 auto;
    max-width: 100%;
    overflow: hidden;
  }

  :global {
    .ant-card-body {
      padding: 1px 40px;
      padding-left: 28px;
    }

    .ant-breadcrumb-link {
      display: flex;
    }

    .unauthorized {
      color: var(--theme-text-color-secondary);
    }

    .breadcrumb-ellipsis {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 400px;
      min-width: 20px;
      white-space: nowrap;
      padding-right: 4px;
      padding-left: 4px;
    }

    .breadcrumb-ellipsis-space {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
      min-width: 20px;
      white-space: nowrap;
      padding-right: 4px;
      padding-left: 4px;
    }

    .ant-card-head {
      border-bottom: 1px solid var(--theme-box-shadow-color-level10);

      .desktopCursor {
        cursor: pointer;
        display: flex;
        align-items: center;
      }

      .checkedTotal {
        color: var(--theme-text-color-secondary);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        min-width: 80px;
        white-space: nowrap;
      }

      .ant-card-head-title {
        line-height: 32px;
        font-weight: 500;
        padding-right: 12px;
      }
    }

    .ant-table-row {
      &:hover {
        .more {
          visibility: visible;
        }

        .trashIcon {
          visibility: visible;
        }

        .ant-table-cell .fileItem .recoverIcon {
          visibility: visible;
        }
      }

      .fileInfo {
        overflow: hidden;
        display: flex;
        align-items: center;

        .ant-space-item {
          display: flex;
          align-items: center;
        }
      }

      .fileName {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--theme-brand-color);
        font-weight: 400;
        max-width: 400px;
        min-width: 20px;
        white-space: nowrap;
        font-size: 14px;
        // padding: 0 4px;
        padding: 0 0 0 4px;
        font-family: 'PingFang SC';

        &:hover {
          cursor: pointer;
          text-decoration: underline;
        }
      }

      .more {
        visibility: hidden;
      }

      .trashIcon {
        visibility: hidden;
        cursor: pointer;

        &:hover {
          background: var(--theme-text-button-color-hover);
        }
      }

      .share-user {
        line-height: 38px;
        font-size: 13px;
        color: var(--theme-text-color-secondary);
      }
    }

    .ant-table-thead {
      .ant-table-cell {
        font-size: 13px;
        font-weight: 400;
        padding: 15px 10px;
      }
    }

    .fileItem {
      display: flex;
      align-items: center;

      .recoverIcon {
        cursor: pointer;
        visibility: hidden;
      }
    }

    .ant-table-body {
      .ant-table-cell {
        padding: 10px 12px;
      }
    }
  }

  .spaceDropdown {
    width: 100px;
  }
}

.xCard {
  width: 100%;
  height: 100%;

  :global {
    .ant-card-body {
      width: 100%;
      height: calc(100% - 56px);
      padding: 0;
      padding-top: 1px;
      position: relative;
    }
  }
}

.noAuthor {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shortcutBox {
  display: flex;

  .shortcutIcon {
    position: absolute;
    top: 32px;
  }
}

.noData {
  text-align: center;

  .title {
    color: var(--theme-basic-color-primary);
    font-size: 28px;
    font-style: normal;
    font-weight: 500;
    line-height: 48px;
  }

  .description {
    width: 372px;
    color: var(--theme-basic-color-primary);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}
