import { message } from 'antd';
import type { Key } from 'react';
import { history } from 'umi';

import { getNavigation } from '@/api/File';
import type { DataType } from '@/model/Desktop';
import { RootType } from '@/model/File';
import { fm2 } from '@/modules/Locale';
// 1:文件夹，2:桌面，3:团队空间
export const getPositioning = async (record: { isFolder: boolean; guid: string; type: string }) => {
  const response = await getNavigation(record.guid);
  const { data } = response;
  const rootType = data.rootType;
  let url = '';
  if (data.ancestors.length <= 1 && record.type !== 'folder') {
    message.warning(fm2('RightclickMouse.noPermissionToOpenTheParentDirectory'));
  }
  const dataAncestors = data.ancestors;
  const count = dataAncestors.length;
  const getFolderUrl = (index: number) => `folder/${dataAncestors[index].guid}`;

  if (rootType === RootType.Space) {
    history.replace({ pathname: `/space/${record.guid}` });
    return;
  }

  if (rootType === 1 && count === 1 && record.isFolder) {
    history.replace({ pathname: `/folder/${dataAncestors[0].guid}` });
    return;
  }

  if (count > 1) {
    switch (rootType) {
      case 1:
        url = getFolderUrl(record.isFolder ? count - 1 : count - 2);
        break;
      case 2:
        if (count === 2) {
          url = 'desktop';
        } else {
          url = getFolderUrl(record.isFolder ? count - 1 : count - 2);
        }
        break;
      case 3:
        if (count === 2) {
          url = record.isFolder ? getFolderUrl(1) : `space/${dataAncestors[0].guid}`;
        } else {
          url = getFolderUrl(record.isFolder ? count - 1 : count - 2);
        }
        break;
      default:
        break;
    }
    history.replace({
      pathname: `/${url}`,
    });
  }
};

// 删除文件还是快捷方式文案
export const deleteFileOrShortcut = async (
  data: DataType[],
  selectedRowKeys: Key[],
  record: DataType | undefined,
  i18nText: {
    deleteShortcut: string;
    deleteShortcutOnly: string;
    title: string;
    deleteShortcutConfirm: string;
    content: string;
  },
) => {
  let title: string = '';
  let content: string = '';
  if (selectedRowKeys.length === 1) {
    if (record?.isShortcut) {
      title = i18nText.deleteShortcut; // 全部是快捷方式
      content = i18nText.deleteShortcutOnly;
    } else {
      title = i18nText.title; // 没有快捷方式
      content = i18nText.content;
    }
  } else {
    const selectedItems = data.filter((item) => selectedRowKeys.includes(item.guid));
    const hasShortcut = selectedItems.some((item) => item.isShortcut);
    const allShortcut = selectedItems.every((item) => item.isShortcut);

    if (allShortcut) {
      title = i18nText.deleteShortcut; // 全部是快捷方式
      content = i18nText.deleteShortcutOnly;
    } else if (hasShortcut) {
      title = i18nText.title; // 包含快捷方式
      content = i18nText.deleteShortcutConfirm;
    } else {
      title = i18nText.title; // 没有快捷方式
      content = i18nText.content;
    }
  }

  return { title, content };
};

export const canDeleteText = ({
  data,
  selectedRowKeys,
  record,
}: {
  data: DataType[];
  selectedRowKeys: Key[];
  record: DataType;
}) => {
  if (selectedRowKeys.length > 1) {
    const selectedItems = data.filter((item) => selectedRowKeys.includes(item.guid));
    return selectedItems.every((item) => item.isShortcut);
  } else {
    if (record.isShortcut) {
      return true;
    } else {
      return false;
    }
  }
};

export const moreHidden = (data: DataType[], selectedRowKeys: Key[]) => {
  if (selectedRowKeys.length >= 2) {
    const selectedItems = data.filter((item) => selectedRowKeys.includes(item.guid));
    const allShortcut = selectedItems.every((item) => item.isShortcut);
    if (allShortcut) {
      return false;
    } else {
      return true;
    }
  } else {
    return true;
  }
};
