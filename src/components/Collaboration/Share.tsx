import { Button, Input } from 'antd';

import { ReactComponent as Link16 } from '@/assets/images/svg/link16.svg';
import { ReactComponent as SolidDownwardArrow } from '@/assets/images/svg/solidDownwardArrow.svg';
import { fm2 } from '@/modules/Locale';

import { CopyLinkUrl } from './components';
interface ShareProps {
  url: string;
}
export const Share: React.FC<ShareProps> = ({ url }) => {
  const fullUrl = new URL(url, window.location.href).href;
  const handleCopy = () => {
    CopyLinkUrl(fullUrl);
  };
  return (
    <div className="shareLinkCopy">
      <Input
        disabled
        className="inputDisabled"
        defaultValue={fm2('ShareCollaboration.linkReadOnly')}
        suffix={<SolidDownwardArrow />}
      />
      <Button icon={<Link16 />} onClick={handleCopy}>
        {fm2('ShareCollaboration.copyLink')}
      </Button>
    </div>
  );
};
