.shareByLink {
  :global(.ant-modal-content) {
    padding: 16px 24px;
  }
}

.back {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  cursor: pointer;

  > span {
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
  }
}

.switchBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  align-items: center;
}

.desc {
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  color: #000;
  font-size: 12px;
  line-height: 20px;
}

.text {
  font-size: 12px;
  color: var(--theme-text-color-secondary);
  line-height: 20px;
}

.linkCopy {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;

  > input {
    margin-right: 8px;
  }
}

.linkPermission {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  > div {
    margin-top: 8px;
    display: flex;
    align-items: center;

    > label {
      flex: 1;
    }
  }
}
