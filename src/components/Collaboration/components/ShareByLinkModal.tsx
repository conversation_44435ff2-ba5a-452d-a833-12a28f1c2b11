import { Button, Input, message, Modal, Radio, Switch } from 'antd';
import { useCallback } from 'react';

import { ReactComponent as ArrowLeft22 } from '@/assets/images/svg/arrowLeft22.svg';
import { ReactComponent as GroupIcon } from '@/assets/images/svg/group.svg';
import { ReactComponent as VectorIcon } from '@/assets/images/svg/vector.svg';
import { useInviteCode } from '@/components-mobile/Collaboration/hooks/useInviteCode';
import { fm, useFormatMessage } from '@/modules/Locale';

import { CopyLinkUrl, OptionPermission, OptionsLinkExpiry } from '.';
import styles from './ShareByLinkModal.less';

export function ShareByLinkModal({ guid, closeLink }: { guid: string; closeLink: () => void }) {
  const [messageApi, contextHolder] = message.useMessage();

  const i18n = {
    linkShareEnabled: fm('ShareCollaborationMobile.linkShareEnabled'),
    linkShareEnabledDescription: fm('ShareCollaborationMobile.linkShareEnabledDescription'),
    inviteByLink: fm('ShareCollaborationMobile.inviteByLink'),
    inviteByLinkDesc: fm('ShareCollaborationMobile.inviteByLinkDesc'),
    copyLinkText: fm('ShareCollaboration.copyLink'),
    linkExpiryPeriod: fm('ShareCollaborationMobile.linkExpiryPeriod'),
    linkPermission: fm('ShareCollaborationMobile.linkPermission'),
  };

  const backText = useFormatMessage('ShareCollaboration.back');
  const { checked, url, handleCopyLink, toggleShare, expiresAt, role, isLoading } = useInviteCode(guid, CopyLinkUrl);

  const handleChange = useCallback(
    (checked: boolean) => {
      toggleShare(checked).catch((err) => {
        if (err) {
          messageApi.error(err);
        }
      });
    },
    [messageApi, toggleShare],
  );

  return (
    <>
      {contextHolder}

      <Modal
        centered
        destroyOnClose
        open
        className={styles.shareByLink}
        closeIcon={null}
        footer={null}
        onCancel={closeLink}
      >
        <div className={styles.back} onClick={closeLink}>
          <ArrowLeft22 style={{ cursor: 'pointer' }} />
          <span>{backText}</span>
        </div>
        <div className={styles.switchBox}>
          <div className={styles.left}>
            <GroupIcon height={28} width={28} />
            <div className={styles.desc}>
              <span className={styles.title}>{checked ? i18n.linkShareEnabled : i18n.inviteByLink}</span>
              <span className={styles.text}>{checked ? i18n.linkShareEnabledDescription : i18n.inviteByLinkDesc}</span>
            </div>
          </div>
          <Switch checked={checked} loading={isLoading} onChange={handleChange} />
        </div>
        <VectorIcon width="100%" />
        <div className={styles.linkCopy}>
          <Input disabled value={url} />
          <Button disabled={!checked} onClick={handleCopyLink}>
            {i18n.copyLinkText}
          </Button>
        </div>
        {checked && (
          <>
            <div className={styles.linkPermission}>
              <span className={styles.text}>{i18n.linkPermission}</span>
              <Radio.Group value={role.role} onChange={(e) => role.handleChange(e.target.value as string)}>
                {OptionPermission().map((item) => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            </div>
            <div className={styles.linkPermission}>
              <span className={styles.text}>{i18n.linkExpiryPeriod}</span>
              <Radio.Group
                value={expiresAt.expiresAt}
                onChange={(e) => expiresAt.handleChange(e.target.value as number)}
              >
                {OptionsLinkExpiry().map((item) => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            </div>
            {/* <div> */}
            {/*   <QRCode value={url} /> */}
            {/*   <span>微信扫码邀请协作</span> */}
            {/* </div> */}
          </>
        )}
      </Modal>
    </>
  );
}
