import { message } from 'antd';

import { fm2 } from '@/modules/Locale';

export const CopyLinkUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      message.success(fm2('ShareCollaboration.copySuccess'));
    })
    .catch(() => {
      message.error(fm2('ShareCollaboration.copyFail'));
    });
};
export const OptionsDays = () => [
  { value: 1, label: `1${fm2('ShareCollaboration.day')}` },
  { value: 7, label: `7${fm2('ShareCollaboration.day')}` },
  { value: 30, label: `30${fm2('ShareCollaboration.day')}` },
];

export const OptionPermission = () => [
  { value: 'reader', label: fm2('ShareCollaboration.readOnly') },
  { value: 'commentator', label: fm2('ShareCollaboration.comment') },
  { value: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
];

export type PermissionType = ReturnType<typeof OptionPermission>[number]['value'];

export const OptionsLinkExpiry = () => [
  { value: 3600, label: `1${fm2('Time.hour')}` },
  { value: 86400, label: `1${fm2('ShareCollaboration.day')}` },
  { value: 604800, label: `7${fm2('ShareCollaboration.day')}` },
];

export const startCountdown = (shareModeExpiredAt: number, setRemainingTime: (time: string) => void): number => {
  const intervalId = window.setInterval(() => {
    const now = Math.floor(Date.now() / 1000);
    const remainingSeconds = shareModeExpiredAt - now;
    if (remainingSeconds <= 0) {
      clearInterval(intervalId);
      setRemainingTime('00:00:00');
      return;
    }
    const days = Math.floor(remainingSeconds / 86400);
    const hours = Math.floor((remainingSeconds % 86400) / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;
    let formattedTime: string;
    if (days === 0) {
      formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    } else {
      formattedTime = `${String(days).padStart(2, '0')}${fm2('ShareCollaboration.day')}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }

    setRemainingTime(formattedTime);
  }, 1000);

  return intervalId;
};

export const stopCountdown = (intervalId: number | null): void => {
  if (intervalId) {
    clearInterval(intervalId);
  }
};

export const itemsPrenet = () => [
  { key: 'inherited', label: fm2('ShareCollaboration.inheritPermission') },
  { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
  { key: 'commentator', label: fm2('ShareCollaboration.comment') },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
];
export const itemRole = () => [
  { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
  { key: 'commentator', label: fm2('ShareCollaboration.comment') },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
  { key: 'remove', label: fm2('ShareCollaboration.removePermission'), danger: true },
];
export const itemsAdmin = () => [
  { key: 'merger', label: fm2('ShareCollaboration.admin') },
  { key: 'remove', label: fm2('ShareCollaboration.removeManager'), danger: true },
];

import { getCollaborationList } from '@/api/Collaboration';

import type { CollaborationData, DataItem, Role, RoleItem } from '../types';
export const getMergerAccount = async (Arr: DataItem[], guid: string, callback?: (updatedArr: DataItem[]) => void) => {
  const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
  Arr?.forEach((el) => {
    el.isAdmin = false;
    getUserData?.data?.admins?.forEach((e: { id: number; isInherited: boolean }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.isAdmin = true;
        el.isInherited = e.isInherited;
      }
    });
  });
  if (callback) {
    callback([...Arr]);
  }
};

export const getMergerRoles = async (Arr: DataItem[], guid: string, callback?: (updatedArr: DataItem[]) => void) => {
  const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
  Arr.forEach((el) => {
    getUserData?.data?.admins?.forEach((e: { id: number; isInherited: boolean }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.isAdmin = true;
        el.isInherited = e.isInherited;
      }
    });
    el.role = undefined;
    getUserData?.data?.roles?.forEach((e: { id: number; role: string }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.role = e.role;
      }
    });
  });
  if (callback) {
    callback([...Arr]);
  }
};

/**
 * 管理员按钮的删除权限
 * 当前管理员是文件的拥有者
 * 当前管理员是最后的管理员
 * 当前管理员是本人
 */
export const isDisabledAdmin = (
  data: CollaborationData,
  item: RoleItem,
  CollaborationAdmins: Role[],
  meId: number | null,
): boolean => {
  return (
    !data?.permissionsAndReasons?.canManageAdmin?.value ||
    CollaborationAdmins.length === 1 ||
    item.isInherited ||
    data?.userId === item.id ||
    meId === item.id
  );
};

// 协作者怕断本人是否可以删除本人
export const editCollaboration = (key: string, item: RoleItem | DataItem, meId: null | number, canExit: boolean) => {
  if (meId === item?.id || meId === item?.user?.id) {
    if (!canExit) {
      return key === 'remove';
    }
  }
};
//协作者本人可以操作本人选项
export const editCollaborationIsMe = (key: string, item: RoleItem | DataItem, meId: null | number) => {
  if (meId === item?.id || meId === item?.user?.id) {
    if (key === 'remove' || key === item.role) {
      return false;
    } else {
      return true;
    }
  }
};

// 判断当前管理员是否是文件本身的拥有者
export const isFileAdminSelf = (item: RoleItem, userId?: number | string) => {
  return userId === item.id;
};
