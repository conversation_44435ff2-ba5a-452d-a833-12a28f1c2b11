import { useMemo } from 'react';

import type { CollaborationData } from '../types';

export function usePermissions(data: CollaborationData | null | undefined) {
  const roleCheckable = useMemo(() => {
    return !!(data?.role && data?.role !== 'none');
  }, [data]);

  const isSpecicalType = useMemo(() => {
    if (!data) {
      return false;
    }
    return data.isSpace || data.isFolder || data.isCloudFile;
  }, [data]);

  const shareDisabled = useMemo(() => {
    if (!data) return true;
    const canChangeShareMode = data?.permissionsAndReasons?.canChangeShareMode?.value ?? false;
    if (isSpecicalType) {
      return true;
    }
    return !canChangeShareMode;
  }, [data, isSpecicalType]);

  const shareDisabledYun = useMemo(() => {
    if (!data) return true;
    const isSpecialType = ['folder', 'newdoc', 'modoc', 'mosheet', 'table', 'presentation', 'form'].includes(
      data.type || '',
    );
    if (data.isSpace || data.isFolder || !isSpecialType) {
      return true;
    } else {
      return false;
    }
  }, [data]);

  const canManageCollaborator = useMemo(() => {
    return data?.permissionsAndReasons?.canManageCollaborator?.value ?? false;
  }, [data]);

  const canExit = useMemo(() => {
    return data?.permissionsAndReasons?.canExit?.value ?? false;
  }, [data]);

  const canManageAdmin = useMemo(() => {
    return data?.permissionsAndReasons?.canManageAdmin?.value ?? false;
  }, [data]);

  const shareStatus = useMemo(() => {
    if (data?.shareMode && data?.shareMode !== 'private') {
      return true;
    } else {
      return false;
    }
  }, [data]);
  const shareStatusVal = useMemo(() => {
    if (data?.shareMode && data?.shareMode !== 'private') {
      return data?.shareMode;
    }
  }, [data]);
  const passwordStatus = useMemo(() => {
    if (data?.shareMode && data?.shareMode !== 'private' && data?.passwordProtected) {
      return true;
    } else {
      return false;
    }
  }, [data]);
  const password = useMemo(() => {
    if (data?.shareMode && data?.shareMode !== 'private' && data?.passwordProtected) {
      return data?.password;
    }
  }, [data]);
  const shareTimeStatus = useMemo(() => {
    if (data?.shareModeExpireDuration && data?.shareModeExpireDuration !== 0) {
      return true;
    } else {
      return false;
    }
  }, [data]);
  const shareModeExpireDuration = useMemo(() => {
    if (data?.shareModeExpireDuration && data?.shareModeExpireDuration !== 0) {
      return data?.shareModeExpireDuration;
    }
  }, [data]);

  const shareModeExpiredAt = useMemo(() => {
    return data?.shareModeExpiredAt || 0;
  }, [data]);

  return {
    roleCheckable, //判断是否有协作权限
    shareDisabled, //判断可以公开分享权限（是否可以操作）
    shareDisabledYun, //云文件直接就不能分享操作
    canManageCollaborator, //判断协作者权限
    canExit, //本人能不能退出协作者权限
    canManageAdmin, //判断管理者权限
    shareStatus, //公开分享开关 （进来的时候是开着还是关着）
    shareStatusVal, //公开分享值（开关开着才有值）
    passwordStatus, //密码开关
    password, //密码值（开关开着才有值）
    shareTimeStatus, //有效期开关
    shareModeExpireDuration, //返回有效期选择框的值（秒）
    shareModeExpiredAt, //剩余天数（秒）
    isSpecicalType,
  };
}

// 非hooks的实现
export function getPermissions(data: CollaborationData | null | undefined) {
  const roleCheckable = !!(data?.role && data?.role !== 'none');

  const isSpecicalType = (() => {
    if (!data) {
      return false;
    }
    return data.isSpace || data.isFolder || data.isCloudFile;
  })();

  const shareDisabled = (() => {
    if (!data) return true;
    const canChangeShareMode = data?.permissionsAndReasons?.canChangeShareMode?.value ?? false;
    if (isSpecicalType) {
      return true;
    }
    return !canChangeShareMode;
  })();

  const canManageCollaborator = data?.permissionsAndReasons?.canManageCollaborator?.value ?? false;

  const canExit = data?.permissionsAndReasons?.canExit?.value ?? false;

  const canManageAdmin = data?.permissionsAndReasons?.canManageAdmin?.value ?? false;

  const shareStatus = (() => {
    if (data?.shareMode && data?.shareMode !== 'private') {
      return true;
    } else {
      return false;
    }
  })();

  const shareStatusVal = (() => {
    if (data?.shareMode && data?.shareMode !== 'private') {
      return data?.shareMode;
    }
  })();

  const passwordStatus = (() => {
    if (data?.shareMode && data?.shareMode !== 'private' && data?.passwordProtected) {
      return true;
    } else {
      return false;
    }
  })();

  const password = (() => {
    if (data?.shareMode && data?.shareMode !== 'private' && data?.passwordProtected) {
      return data?.password;
    }
  })();

  const shareTimeStatus = (() => {
    if (data?.shareModeExpireDuration && data?.shareModeExpireDuration !== 0) {
      return true;
    } else {
      return false;
    }
  })();

  const shareModeExpireDuration = (() => {
    if (data?.shareModeExpireDuration && data?.shareModeExpireDuration !== 0) {
      return data?.shareModeExpireDuration;
    }
  })();

  const shareModeExpiredAt = data?.shareModeExpiredAt || 0;

  return {
    roleCheckable, //判断是否有协作权限
    shareDisabled, //判断可以公开分享权限（是否可以操作）
    canManageCollaborator, //判断协作者权限
    canExit, //本人能不能退出协作者权限
    canManageAdmin, //判断管理者权限
    shareStatus, //公开分享开关 （进来的时候是开着还是关着）
    shareStatusVal, //公开分享值（开关开着才有值）
    passwordStatus, //密码开关
    password, //密码值（开关开着才有值）
    shareTimeStatus, //有效期开关
    shareModeExpireDuration, //返回有效期选择框的值（秒）
    shareModeExpiredAt, //剩余天数（秒）
    isSpecicalType, // 是否特殊类型（云文件/文件夹/团队空间）
  };
}
