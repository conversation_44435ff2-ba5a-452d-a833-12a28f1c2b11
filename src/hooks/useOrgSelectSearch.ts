import debounce from 'lodash/debounce';
import { useCallback, useMemo, useRef, useState } from 'react';

import type { OrgItemType } from '../components/OrgSelectCard/type';
import { DEBOUNCE_INPUT_WAIT } from '../configs/configs';
import { getSearchOrg } from '../service/userOrg/service';
import { convertOrgList } from '../utils/orgSelect';

interface OrgSelectSearchType {
  loading: boolean;
  result?: OrgItemType[];
  onSearch: (value: string) => void;
  clearSearch: () => void;
  clearAllSelected: () => void;
}

interface OrgSelectSearchProps {
  checkDepartment: boolean;
  selectedList: OrgItemType[];
  searchUserConfig?: {
    includeDisabledMember?: boolean;
    includeRecentContact?: boolean;
    includeTeamMember?: boolean;
  };
  admin: boolean;
}

export function useOrgSelectSearch({
  selectedList,
  checkDepartment,
  searchUserConfig,
  admin,
}: OrgSelectSearchProps): OrgSelectSearchType {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<OrgItemType[]>();
  const valueRef = useRef<string | undefined>(undefined);
  const onSearch = useCallback(
    (value: string) => {
      valueRef.current = value;
      if (!value) {
        setResult(undefined);
        return;
      }
      setLoading(true);
      getSearchOrg({
        keyword: value,
        department: checkDepartment ? {} : undefined,
        user: searchUserConfig,
        admin: admin,
      })
        .then((res) => {
          if (value === valueRef.current) {
            setResult(
              convertOrgList({
                list: res,
                checkDepartment,
              }),
            );
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [checkDepartment, selectedList, searchUserConfig, admin],
  );

  const clearAllSelected = useCallback(() => {
    setResult((list) => {
      if (!list) {
        return undefined;
      }
      return list.map((item) => ({ ...item, checked: false }));
    });
  }, []);

  const clearSearch = useCallback(() => {
    setResult(undefined);
  }, []);

  return useMemo(
    () => ({
      loading,
      result: result?.map((item) => ({
        ...item,
        checked: selectedList.some((selected) => selected.id === item.id),
      })),
      clearSearch,
      clearAllSelected,
      onSearch: debounce(onSearch, DEBOUNCE_INPUT_WAIT),
    }),
    [loading, result, clearSearch, onSearch, selectedList, clearAllSelected],
  );
}
