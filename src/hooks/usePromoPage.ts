import s18n from '@shimo/simple-i18n';
import type { ButtonType } from 'antd/lib/button';
import type { HTMLAttributeAnchorTarget, MutableRefObject } from 'react';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';

import BannerBackgroundImage from '../assets/components/promoPage/<EMAIL>';
import BannerBackgroundDarkImage from '../assets/components/promoPage/<EMAIL>';
import type { EnterprisePaymentSource } from '../configs/paymentSourceConfigs';
import { MeContext } from '../contexts/me';
import { isElementEvent } from '../pages/pc/EfficiencyPanel/utils/assertUtils';
import { formatterTeamId } from '../utils/formatterTeamId';
import { RoutePaths } from '../utils/path';
import { useThemeIsDark } from './Theme';

export interface PromoPageType {
  ref: MutableRefObject<HTMLDivElement | null>;
  header: {
    theme: string;
    teamId?: number;
    teamName?: string;
  } | null;
  action: {
    text: string;
    href: string;
    type: ButtonType;
    target: HTMLAttributeAnchorTarget;
  };
  bannerBackgroundImageSrc: string;
}

interface PromoPageParams {
  paymentSource: EnterprisePaymentSource;
}

export function usePromoPage({ paymentSource }: PromoPageParams): PromoPageType {
  const me = useContext(MeContext);
  const ref = useRef<HTMLDivElement | null>(null);
  const [headerTheme, setHeaderTheme] = useState<string | undefined>();
  const isDark = useThemeIsDark();

  const subscriptionUrl = useMemo(() => {
    if (me?.accountMetadata?.isEnterprisePremium) {
      return `${(RoutePaths as any).Billing}?dialog=purchase_duration&paymentSource=${paymentSource}`;
    }
    return `${(RoutePaths as any).Billing}?dialog=purchase_edition&paymentSource=${paymentSource}`;
  }, [me, paymentSource]);

  const scrollHandler = useCallback((event: unknown) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (isElementEvent(event) && (event as any).target.scrollTop >= 316) {
      setHeaderTheme('scroll');
      return;
    }
    setHeaderTheme(undefined);
  }, []);

  useEffect(() => {
    if (ref.current) {
      ref.current.addEventListener('scroll', scrollHandler);
    }
    return () => {
      if (ref.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        ref.current.removeEventListener('scroll', scrollHandler);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scrollHandler, ref.current]);

  const isEnterprisePremiumExpired: boolean = useMemo(() => {
    if (!me) {
      return false;
    }
    const { membership } = me;
    return membership.isEnterprisePremium && membership.isExpired;
  }, [me]);

  return useMemo(
    () => ({
      ref,
      action: {
        text: isEnterprisePremiumExpired ? s18n('续费企业高级版以使用') : s18n('升级企业高级版以使用'),
        type: 'primary',
        href: subscriptionUrl,
        target: '_blank',
      },
      header:
        me && headerTheme
          ? {
              theme: headerTheme,
              teamId: formatterTeamId(me.teamId),
              teamName: me.team.name,
            }
          : null,
      bannerBackgroundImageSrc: isDark ? BannerBackgroundDarkImage : BannerBackgroundImage,
    }),
    [me, ref, headerTheme, isDark, subscriptionUrl, isEnterprisePremiumExpired],
  );
}
