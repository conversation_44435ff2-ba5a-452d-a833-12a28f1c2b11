import type { Key } from "react";
import { useCallback, useEffect, useState } from "react";

import { filesNumUrl } from "@/api/File";

import { useEventSource } from './useEventSource';

export const useListNumStr = ({ selectedRowKeys }: { selectedRowKeys: Key[] }) => {
    const [listNumStr, setListNumStr] = useState('');
    const { disconnect } = useEventSource({
        url: filesNumUrl,
        method: 'POST',
        params: { guids: selectedRowKeys },
        debounceDelay: 300,
        enableCache: false,
        onMessage: useCallback((data: { isEnd: boolean; nums: number }) => {
            if (data.isEnd) {
                setListNumStr(data.nums <= 500 ? data.nums.toString() : `${500}+`);
            }
        }, []),
    });

    useEffect(() => {
        return () => {
            disconnect();
        };
    }, []);
    return { listNumStr };
};
