import { fetchEventSource } from '@microsoft/fetch-event-source';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { CommonApi } from '@/api/Request';

// 配置选项接口
interface UseEventSourceOptions<T = unknown> {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    params?: unknown;
    // 重试配置
    retryAttempts?: number;
    retryDelay?: number;
    // 防抖延迟（毫秒）
    debounceDelay?: number;
    // 事件处理器
    onMessage?: (data: T) => void;
    onError?: (error: Error) => void;
    onOpen?: () => void;
    onClose?: () => void;
    parser?: (data: string) => T;
    enableCache?: boolean;
}

// 返回值接口
interface UseEventSourceReturn<T> {
    data: T | null;
    error: Error | null;
    connect: () => void;
    disconnect: () => void;
    clearCache: () => void;
}

// 深度比较函数
function deepEqual(a: unknown, b: unknown): boolean {
    if (a === b) return true;
    if (a === null || b === null) return false;
    if (typeof a !== typeof b) return false;
    if (typeof a !== 'object') return false;

    const keysA = Object.keys(a);
    const keysB = Object.keys(b as object);
    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
        if (!keysB.includes(key)) return false;
        if (!deepEqual((a as Record<string, unknown>)[key], (b as Record<string, unknown>)[key])) return false;
    }
    return true;
}

export function useEventSource<T = unknown>({
    url,
    method = 'GET',
    params,
    debounceDelay = 300,
    onMessage,
    onError,
    onOpen,
    onClose,
    parser = (data: string) => JSON.parse(data) as T,
    enableCache = true,
}: UseEventSourceOptions<T>): UseEventSourceReturn<T> {
    const [data, setData] = useState<T | null>(null);
    const [error, setError] = useState<Error | null>(null);

    const controllerRef = useRef<AbortController | null>(null);
    const lastDataRef = useRef<T | null>(null);
    const isConnectingRef = useRef(false);

    // 缓存上一次的参数，避免不必要的重新连接
    const prevParamsRef = useRef(params);

    // 使用 useMemo 优化序列化的依赖检查
    const serializedParams = useMemo(() => {
        try {
            return JSON.stringify(params);
        } catch {
            return String(params);
        }
    }, [params]);

    // 检查依赖是否真正发生变化
    const hasDependencyChanged = useMemo(() => {
        const paramsChanged = !deepEqual(prevParamsRef.current, params);
        if (paramsChanged) {
            prevParamsRef.current = params;
            return Date.now();
        }
        return 0;
    }, [serializedParams]);

    // 清理函数
    const cleanup = useCallback(() => {
        if (controllerRef.current) {
            controllerRef.current.abort();
            controllerRef.current = null;
        }
        isConnectingRef.current = false;
    }, []);

    // 优化的数据更新函数
    const updateData = useCallback((newData: T) => {
        if (enableCache && deepEqual(lastDataRef.current, newData)) return;
        lastDataRef.current = newData;
        setData(newData);
    }, [enableCache]);

    // 连接函数
    const connect = useCallback(async () => {
        setError(null);
        try {
            controllerRef.current = new AbortController();
            await fetchEventSource(`${CommonApi.defaults.baseURL}${url}`, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                },
                body: method !== 'GET' ? JSON.stringify(params) : undefined,
                signal: controllerRef.current.signal,

                async onopen() {
                    isConnectingRef.current = true;
                    onOpen?.();
                },

                onmessage(event) {
                    try {
                        const parsedData = parser(event.data);
                        updateData(parsedData);
                        onMessage?.(parsedData);
                    } catch (parseError) {
                        console.error('Failed to parse SSE message:', parseError);
                        const error = new Error(`Parse error: ${parseError}`);
                        setError(error);
                        onError?.(error);
                    }
                },

                onerror() {
                    const error = new Error('SSE connection failed');
                    setError(error);
                    onError?.(error);
                    cleanup();
                },
            });
        } catch (connectionError) {
            isConnectingRef.current = false;
            const error = connectionError instanceof Error
                ? connectionError
                : new Error('Connection failed');
            setError(error);
            onError?.(error);
        }
    }, [url, method, serializedParams, onMessage, onError, onOpen, onClose, parser, cleanup, updateData]);

    // 防抖的连接函数
    const debouncedConnect = useMemo(() => {
        return debounce(connect, debounceDelay);
    }, [connect, debounceDelay]);

    // 断开连接
    const disconnect = useCallback(() => {
        cleanup();
        onClose?.();
    }, [cleanup, onClose]);

    // 清除缓存
    const clearCache = useCallback(() => {
        lastDataRef.current = null;
        setData(null);
    }, []);

    useEffect(() => {
        if (hasDependencyChanged) {
            debouncedConnect();
        }
    }, [hasDependencyChanged]);

    return {
        data,
        error,
        connect,
        disconnect,
        clearCache,
    };
}
