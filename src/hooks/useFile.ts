import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { history, useParams } from 'umi';

import { fileDetail, getNavigation } from '@/api/File';
import { FileSettingContext } from '@/contexts/FileSetting';
import { RootType } from '@/model/File';
import type { FileDataModel, FileItemBase } from '@/model/FileList';
import { fm } from '@/modules/Locale';
import { hasProperty } from '@/pages/pc/Enterprise/utils/typeScript';
import { useCreateFileBtnStore } from '@/store/createFileBtn';
import { useShowErrorPageStore } from '@/store/errorPage';
import { useFileTabStore } from '@/store/FileTab';
import { useMeStore } from '@/store/Me';
import { useMobileNavigationStore } from '@/store/MobileNavigation';
import { setBrowserTabTitle } from '@/utils/browser';
import { openFile, useFormatTime } from '@/utils/file';
import type { ErrorCodes } from '@/utils/request/ErrorCodeMap';

export const useFile = (getList?: (setFileList: Dispatch<SetStateAction<FileDataModel[]>>) => Promise<unknown>) => {
  const [fileList, setFileList] = useState<FileDataModel[]>([]);
  const [detail, setDetail] = useState<Partial<FileDataModel> | null>();
  const { guid } = useParams();
  const setNavigators = useFileTabStore((state) => state.setNavigators);
  const navigators = useFileTabStore((state) => state.navigators);
  const setFrom = useMobileNavigationStore((state) => state.setFrom);
  const setSpaceGuids = useMobileNavigationStore((state) => state.setSpaceGuids);
  const setFolderGuids = useMobileNavigationStore((state) => state.setFolderGuids);
  const setFolderName = useMobileNavigationStore((state) => state.setFolderName);
  const setShowErrorPage = useShowErrorPageStore((state) => state.setShowErrorPage);
  const [loading, setLoading] = useState(true);
  const [errorCode, setErrorCode] = useState<ErrorCodes | undefined>(undefined);
  const unknownParentDirectoryText = fm('File.unknownParentDirectory');

  const { refreshFileList } = useContext(FileSettingContext);

  const getFileList = useCallback(() => {
    if (getList) {
      setLoading(true);
      getList?.(setFileList).finally(() => {
        setLoading(false);
      });
    }
  }, [guid]);
  const clickItem = useCallback((data: FileDataModel | FileDataModel[]) => {
    const newData = data as FileDataModel;
    const getUrl = () => {
      if (newData?.isSpace) {
        return `/space/${newData.guid}`;
      } else if (newData.type === 'folder') {
        return `/folder/${newData.guid}`;
      } else {
        return newData.isShortcut ? newData.shortcutSource?.url : newData.url;
      }
    };
    openFile({
      url: getUrl() ?? '',
      type: newData.isFolder || newData.isSpace ? 'folder' : newData.subType,
      guid: newData.isShortcut ? (newData.shortcutSource?.guid ?? '') : (newData.guid ?? ''),
    });
  }, []);
  const setCanCreateChildFile = useCreateFileBtnStore((state) => state.setCanCreateChildFile);
  const getFileDetail = useCallback(() => {
    if (guid) {
      fileDetail(guid)
        .then((res) => {
          if (res.status === 200) {
            setBrowserTabTitle(res.data.name);
            setDetail(res.data);
            if (res?.data?.isSpace) {
              setFrom('currentSpace');
              setSpaceGuids(res?.data?.guid || '');
            }
            if (res?.data?.isFolder) {
              setFrom('folder');
              setFolderGuids(res?.data?.guid || '');
              setFolderName(res?.data?.name || '');
            }
            if (res?.data?.isFolder && res?.data?.space_guid) {
              setFrom('space2folder');
              setSpaceGuids(res?.data?.space_guid || '');
              setFolderGuids(res?.data?.guid || '');
              setFolderName(res?.data?.name || '');
            }
            if (res?.data?.isFolder && res?.data?.isDesktop) {
              setFrom('desktop2folder');
              setFolderGuids(res?.data?.guid || '');
              setFolderName(res?.data?.name || '');
            }

            // 共享文件夹内当前用户是否有创建文件权限
            setCanCreateChildFile(!!res?.data?.permissionsAndReasons?.canCreateChildFile?.value);
          }
        })
        .catch((error) => {
          const code = hasProperty(error, 'data') && hasProperty(error.data, 'code') && (error.data.code as ErrorCodes);
          if (code) {
            setErrorCode(code);
          }
          setShowErrorPage(true);
          setBrowserTabTitle(unknownParentDirectoryText);
        });
    } else {
      setDetail(null);
    }
  }, [guid]);

  const back = useCallback(() => {
    const index = navigators.ancestors.findIndex((item) => item.guid === guid);
    const currentObj = navigators.ancestors[index - 1];
    if (currentObj?.id) {
      history.push(`/folder/${currentObj.guid}`);
      return;
    }
    if (navigators.rootType === RootType.Space) {
      history.push(`/space`);
    } else {
      history.push(`/desktop`);
    }
  }, [navigators, guid]);

  const fileHeaderProp = useMemo(
    () => ({
      back,
      data: detail,
      guid,
    }),
    [back, guid, detail],
  );
  const getBackList = useCallback(async () => {
    const response = await getNavigation(guid);
    setNavigators(response.data);
  }, [guid, setNavigators]);
  useEffect(() => {
    if (guid) {
      getBackList();
    }
  }, [getBackList, guid]);

  useEffect(() => {
    getFileList();
    getFileDetail();
  }, [getFileList, getFileDetail, refreshFileList]);
  return { fileList, setLoading, fileHeaderProp, clickItem, detail, setFileList, loading, errorCode, guid };
};
export const useFileItem = <R extends FileItemBase = FileDataModel>(data: R, selectedType?: string) => {
  const id = useMeStore((state) => state.me.id);
  const { formatTime } = useFormatTime();
  const createTimeText = fm('SiderMenu.siderMenuCreactText');
  const deleteText = fm('File.delete');
  const defaultText = fm('SearchCenter.update');
  const openText = fm('SearchCenter.open');
  const editText = fm('Header.editButtonText');
  const sharedAtText = fm('File.shared');
  const meText = fm('SearchCenter.me');

  const timeText = useMemo(() => {
    return {
      createTime: createTimeText,
      delete: deleteText,
      default: defaultText,
      open: openText,
      edit: editText,
      sharedAt: sharedAtText,
      me: meText,
    };
  }, [createTimeText, deleteText, defaultText, openText, editText, sharedAtText, meText]);
  let name = '';
  if (selectedType === 'createTime') {
    name = data.userId === id ? timeText.me : data?.user?.name || '';
  } else {
    name = data.updatedUser?.id === id ? timeText.me : data.updatedUser?.name || '';
  }
  const updatedAtTimeText = formatTime(data.updatedAt!);
  const sharedAtTimeText = formatTime(data.sharedAt!);
  const defaultAtTimeText = `${formatTime(selectedType === 'createTime' ? data.createdAt! : data.updatedAt!)} ${name} ${timeText[selectedType as 'createTime'] || timeText.default}`;
  const noneText = `${formatTime(data.updatedAt!)} ${timeText.me} ${timeText[data.lastAction as 'open' | 'edit']}`;
  const timerText = useMemo(() => {
    if (selectedType === 'delete') {
      return `${updatedAtTimeText} ${timeText.delete}`;
    } else if (selectedType === 'sharedAt') {
      return `${sharedAtTimeText} ${name} ${timeText[selectedType]}`;
    } else if (selectedType === 'unknown') {
      return `${updatedAtTimeText}`;
    } else if (selectedType) {
      return defaultAtTimeText;
    } else {
      return noneText;
    }
  }, [name, noneText, sharedAtTimeText, updatedAtTimeText, defaultAtTimeText, timeText, selectedType]);

  return {
    timerText,
  };
};
