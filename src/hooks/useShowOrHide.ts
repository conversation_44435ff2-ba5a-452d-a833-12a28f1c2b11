import { useEffect, useState } from 'react';

import { getSMEditor } from '@/utils/ShimoSDK';

export const useShowOrHide = () => {
  const [showComment, setShowComment] = useState(true);

  const [showMenu, setShowMenu] = useState(false); // 侧边栏显示/隐藏状态

  const [showWriter, setWriter] = useState(false);

  const setControlStatus = (exportType?: string) => {
    if (!exportType) return;
    if (exportType === 'menu') {
      if (showMenu) {
        getSMEditor()?.hideToc();
        setShowMenu(false);
      } else {
        getSMEditor()?.showToc();
        setShowMenu(true);
      }
      return;
    }
    if (exportType === 'comment') {
      if (showComment) {
        getSMEditor()?.hideComments(); // 轻文档暂不生效 TODO
        setShowComment(false);
      } else {
        getSMEditor()?.showComments();
        setShowComment(true);
      }
      return;
    }
    if (exportType === 'writer') {
      if (showWriter) {
        getSMEditor()?.hideCollaborator();
        setWriter(false);
      } else {
        getSMEditor()?.showCollaborator();
        setWriter(true);
      }
    }
  };
  useEffect(() => {
    const isSidebar = getSMEditor()?.isSidebarVisible?.();
    setShowMenu(isSidebar);
  }, []);

  return {
    showComment,
    showMenu,
    showWriter,
    setShowComment,
    setControlStatus,
  };
};
