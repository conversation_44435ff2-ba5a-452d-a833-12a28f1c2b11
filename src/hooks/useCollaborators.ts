import CollaboratorsClient, { CollaboratorsClientEvents } from '@shimo/collaborators-client';
import { useEffect, useState } from 'react';

import { CustomEventName } from '@/model/CustomEvent';
import type { AvatarItem, CollaboratorEnterData } from '@/model/Editor';
import { onCustomEvent } from '@/utils/customEvent';

declare global {
  interface Window {
    collaboratorsClient: CollaboratorsClient | null;
  }
}

export function useCollaborators() {
  const [collaborators, setCollaborators] = useState<AvatarItem[]>([]);

  useEffect(() => {
    function handleUserEnter(data: CollaboratorEnterData) {
      const { collaborators } = data;
      setCollaborators(collaborators);
    }
    function handleUserLeave(data: CollaboratorEnterData) {
      const { collaborators } = data;
      setCollaborators(collaborators);
    }

    // 初始化协作者
    function initCollaborators(socket: SocketIOClient.Socket) {
      const collaboratorsClient = new CollaboratorsClient({
        ws: socket,
        fileGuid: window.file.guid,
        currentUserId: window.user.id,
      });
      collaboratorsClient.start();
      collaboratorsClient.on(CollaboratorsClientEvents.UserEnter, handleUserEnter);
      collaboratorsClient.on(CollaboratorsClientEvents.UserLeave, handleUserLeave);
      return collaboratorsClient;
    }

    let collaboratorsClient: CollaboratorsClient | null = null;
    const removeListener = onCustomEvent<{ socket: SocketIOClient.Socket }>(CustomEventName.socketReady, (detail) => {
      collaboratorsClient = initCollaborators(detail.socket);
      window.collaboratorsClient = collaboratorsClient;
    });

    return () => {
      // 退出协作者客户端
      if (collaboratorsClient) {
        collaboratorsClient.quit();
      }

      removeListener();
    };
  }, []);

  return { collaborators };
}
