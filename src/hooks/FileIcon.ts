// 导入需要的图标
import cloudSrc from '@/assets/images/fileIcon/<EMAIL>';
import excelSrc from '@/assets/images/fileIcon/<EMAIL>';
import folderSrc from '@/assets/images/fileIcon/<EMAIL>';
import formSrc from '@/assets/images/fileIcon/<EMAIL>';
import zipSrc from '@/assets/images/fileIcon/<EMAIL>';
import mindmapSrc from '@/assets/images/fileIcon/<EMAIL>';
import modocSrc from '@/assets/images/fileIcon/<EMAIL>';
import mosheetSrc from '@/assets/images/fileIcon/<EMAIL>';
import mp3Src from '@/assets/images/fileIcon/<EMAIL>';
import mp4Src from '@/assets/images/fileIcon/<EMAIL>';
import newdocSrc from '@/assets/images/fileIcon/<EMAIL>';
import pdfSrc from '@/assets/images/fileIcon/<EMAIL>';
import picSrc from '@/assets/images/fileIcon/<EMAIL>';
import pptSrc from '@/assets/images/fileIcon/<EMAIL>';
import presentationSrc from '@/assets/images/fileIcon/<EMAIL>';
import tableSrc from '@/assets/images/fileIcon/<EMAIL>';
import boardSrc from '@/assets/images/fileIcon/<EMAIL>';
import wordSrc from '@/assets/images/fileIcon/<EMAIL>';
import wpsSrc from '@/assets/images/fileIcon/<EMAIL>';

/**
 * 根据文件类型获取对应的图标
 */
export function getFileIcon(type?: string) {
  if (!type) return cloudSrc;

  switch (type.toLowerCase()) {
    // 文档
    case 'newdoc':
      return newdocSrc;
    case 'modoc':
      return modocSrc;
    case 'docx':
    case 'doc':
    case 'word':
      return wordSrc;
    case 'pdf':
      return pdfSrc;
    case 'wps':
      return wpsSrc;
    case 'board':
      return boardSrc;
    case 'mindmap':
      return mindmapSrc;

    // 表格，表单
    case 'form':
      return formSrc;
    case 'mosheet':
      return mosheetSrc;
    case 'table':
      return tableSrc;
    case 'excel':
    case 'xlsx':
    case 'xls':
    case 'csv':
      return excelSrc;

    // 幻灯片
    case 'presentation':
    case 'ppt':
      return presentationSrc;
    case 'pptx':
      return pptSrc;

    // 音视频
    case 'mp3':
      return mp3Src;
    case 'mp4':
    case 'mov':
    case 'qt':
      return mp4Src;

    // 图片
    case 'img':
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'tiff':
      return picSrc;

    // 压缩文件
    case 'zip':
    case 'rar':
      return zipSrc;

    // 文件夹
    case 'folder':
      return folderSrc;

    // 默认
    default:
      return cloudSrc;
  }
}
