import type { ModalProps } from 'antd';
import { useCallback, useMemo, useState } from 'react';

import { EnterprisePaymentSource } from '../configs/paymentSourceConfigs';
import { RoutePaths } from '../utils/path';

export interface CapacityDetailsModalType extends ModalProps {
  onUpgradeOrRenew: () => void;
  onBuyPress: () => void;
  onOpen: () => void;
}

export function useCapacityDetailsModal(props?: {
  onBuyPress: () => void;
  onUpgradeOrRenew: () => void;
}): CapacityDetailsModalType {
  const [open, setOpen] = useState(false);

  const onBuyPress = useCallback(() => {
    if (props) {
      const { onBuyPress } = props;
      setOpen(false);
      onBuyPress();
    } else {
      location.href = `${RoutePaths.Billing}?redirect_url=${location.href}&dialog=purchase_capacity&paymentSource=${EnterprisePaymentSource.StorageDetailStoragePayment}`;
    }
  }, [props?.onBuyPress]);

  const onUpgradeOrRenew = useCallback(() => {
    if (props) {
      const { onUpgradeOrRenew } = props;
      setOpen(false);
      onUpgradeOrRenew();
    } else {
      location.href = `${RoutePaths.Billing}?redirect_url=${location.href}&dialog=purchase_duration&paymentSource=${EnterprisePaymentSource.StorageDetailRenew}`;
    }
  }, [props?.onUpgradeOrRenew]);

  return useMemo(
    () => ({
      open,
      onOpen: () => {
        setOpen(true);
      },
      onCancel: () => {
        setOpen(false);
      },
      onBuyPress,
      onUpgradeOrRenew,
    }),
    [open, onBuyPress, onUpgradeOrRenew],
  );
}
