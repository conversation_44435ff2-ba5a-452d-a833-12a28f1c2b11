import { history } from 'umi';

import * as createApi from '@/api/Create';
import { catchApiResult } from '@/api/Request';
import { CreateFolder as CreateFolderModal } from '@/components/Modal/CreateFolder';
import type { FileType } from '@/constants/fileList.config';
import { useFileTabStore } from '@/store/FileTab';
import { useUploadStore } from '@/store/Upload';

import useFileUpload from './useFileUpload';

export const useCreateFile = (guid: string) => {
  const { setH5ActionShow, setIsCreateFile } = useUploadStore((state) => state);
  const activeTab = useFileTabStore((state) => state.activeTab);
  const { triggerUpload: uploadFile, triggerUploadFolder: uploadFolder } = useFileUpload({
    parentGuid: guid,
  });

  const createFolder = async (folderName: string) => {
    const [, res] = await catchApiResult(createApi.createFolder(folderName, guid));
    if (res?.status !== 200) return;
    window.open(`/folder/${res.data.guid}`, '_self');
  };

  const createSpace = async (folderName: string) => {
    const [, res] = await catchApiResult(createApi.createSpace(folderName));
    if (res?.status !== 200) return;
    window.open(`/space/${res.data.guid}`, '_self');
  };
  const createTemplates = () => {
    history.push(`/templates/customTemplates?referer=${activeTab}&guid=${guid}`);
  };
  const createFile = (fileType: FileType) => {
    switch (fileType) {
      case 'newdoc':
        setIsCreateFile(true);
      case 'modoc':
        setIsCreateFile(true);
      case 'mosheet':
        setIsCreateFile(true);
      case 'table':
        setIsCreateFile(true);
      case 'presentation':
        setIsCreateFile(true);
      case 'form':
        setIsCreateFile(true);
      case 'mindmap':
        setIsCreateFile(true);
        window.open(`/api/v1/files/create/${fileType}?parentGuid=${guid}`, '_blank');
        break;
      case 'whiteboard':
        setIsCreateFile(true);
        window.open(`/api/v1/files/create/board?parentGuid=${guid}`, '_blank');
        break;

      case 'table-form':
        setIsCreateFile(true);
        window.open(`/api/v1/files/create/form?parentGuid=${guid}&formType=table`, '_blank');
        break;

      case 'test-form':
        setIsCreateFile(true);
        window.open(`/api/v1/files/create/form?parentGuid=${guid}&formType=quiz`, '_blank');
        break;

      case 'folder':
      case 'space':
        CreateFolderModal({
          type: fileType,
          onOk: (fileName) => (fileType === 'folder' ? createFolder(fileName) : createSpace(fileName)),
        });
        break;

      case 'upload-file':
        uploadFile();
        break;
      case 'upload-folder':
        uploadFolder();
        break;

      case 'template':
        break;

      default:
        break;
    }
  };

  const createFileForH5 = (fileType: FileType, fileName: string) => {
    switch (fileType) {
      case 'newdoc': // 新建文档
      case 'mosheet': // 新建表格
      case 'form': // 创建表单, H5只支持普通表单
      case 'table': // 应用表格
        window.location.href = `/api/v1/files/create/${fileType}?parentGuid=${guid}&name=${fileName}`;
        break;

      case 'folder':
        createFolder(fileName);
        break;

      case 'template':
        createTemplates();
        break;

      case 'upload-file':
        setH5ActionShow(true);
        break;

      default:
        break;
    }
  };

  return {
    createFile,
    createFileForH5,
  };
};
