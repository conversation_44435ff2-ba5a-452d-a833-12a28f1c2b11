import { useCallback, useEffect, useMemo, useState } from 'react';

import type { SortModel } from '@/model/FileList';
import type { FileDataModel } from '@/model/FileList';
import { ModeTypeEnum } from '@/model/FileList';

import { useLocalStorage } from './UseStorage';
type Props = {
  defaultStoredValue: Partial<SortModel>;
  fileKey: string;
  fileList: FileDataModel[];
  setFileList: (callback: (fileList: FileDataModel[]) => FileDataModel[]) => void;
  refreshFileList?: number;
  sortRefresh?: number;
};
export const useFileSort = ({ sortRefresh, fileKey, setFileList, defaultStoredValue }: Props) => {
  const [setting, setSetting] = useState({
    isHiddenFolderTop: false,
  });
  const [storedValue, upDateValue] = useLocalStorage<Partial<SortModel>>(fileKey, defaultStoredValue);

  const changeMode = useCallback(() => {
    upDateValue({
      ...storedValue,
      layout: storedValue.layout === ModeTypeEnum.list ? ModeTypeEnum.card : ModeTypeEnum.list,
    });
  }, [storedValue, upDateValue]);
  const findAndMoveNoGuid = (arr: FileDataModel[]): FileDataModel[] => {
    const noGuidItems = arr.filter((item) => !item.guid);

    if (noGuidItems.length === 0) {
      return [...arr];
    }

    const restItems = arr.filter((item) => item.guid);
    return [...noGuidItems, ...restItems];
  };

  useEffect(() => {
    setFileList((preFileList: FileDataModel[]) => {
      if (!preFileList.length) return [];
      // 主排序逻辑
      const compareFn = (a: FileDataModel, b: FileDataModel) => {
        switch (storedValue.selectedType) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'createTime':
            return a.createdAt - b.createdAt;
          case 'sharedAt':
            return a.sharedAt! - b.sharedAt!;
          case 'updateTime':
          default:
            return a.updatedAt - b.updatedAt;
        }
      };
      // 应用排序方向
      const direction = storedValue.isReversed ? -1 : 1;
      // 分别排序
      if (storedValue.isFolderPinned) {
        const folders = preFileList.filter((item) => item.isFolder);
        const nonFolders = preFileList.filter((item) => !item.isFolder);
        nonFolders.sort((a, b) => direction * compareFn(a, b));
        folders.sort((a, b) => direction * compareFn(a, b));
        return findAndMoveNoGuid([...folders, ...nonFolders]);
      } else {
        const sorted = [...preFileList];
        sorted.sort((a, b) => direction * compareFn(a, b));
        return findAndMoveNoGuid(sorted);
      }
    });
  }, [setFileList, storedValue, sortRefresh]);

  const fileSortProp = useMemo(
    () => ({
      setSetting,
      setting,
      changeMode,
      upDateSortData: upDateValue,
      sortData: storedValue,
    }),
    [changeMode, setting, setSetting, upDateValue, storedValue],
  );

  return {
    fileSortProp,
    changeMode,
  };
};
