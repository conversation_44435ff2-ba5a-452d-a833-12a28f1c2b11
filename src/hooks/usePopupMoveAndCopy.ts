import { Toast } from 'antd-mobile';
import path from 'path';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { getSpaces, move as moveApi } from '@/api/File';
import { to } from '@/api/Request';
import { getSpacePinList } from '@/api/Space';
import { FileSettingContext } from '@/contexts/FileSetting';
import type { FileDataModel } from '@/model/FileList';
import { fm, fm2 } from '@/modules/Locale';

type ObjCacheModel = Map<string, { currentData: Partial<FileDataModel> | undefined; list: FileDataModel[] }>;
export const usePopupMoveAndCopy = () => {
  const text = {
    areCreating: fm('TempLatePicker.areCreating'),
    desktop: fm('File.desktop'),
    loading: fm('Loading.loading'),
    copy: fm('FilePathPicker.copy'),
  };

  const desktop = {
    name: text.desktop,
    guid: 'Desktop',
    isFolder: true,
  };

  const [objCache, setObjCache] = useState<ObjCacheModel>(new Map());
  const objCacheRef = useRef<[string, { currentData: Partial<FileDataModel> | undefined; list: FileDataModel[] }]>();
  const { visibleData, setVisibleData } = useContext(FileSettingContext);
  const [refreshFileList, setRefreshFileList] = useState(0);
  const guid = useMemo(() => {
    return visibleData.data?.guid;
  }, [visibleData.data?.guid]);

  const goRefreshFileList = useCallback(() => {
    setRefreshFileList((num) => num + 1);
  }, []);
  const changeObjCache = (guid: string, currentData: Partial<FileDataModel> | undefined, list: FileDataModel[]) => {
    setObjCache((prev) => {
      const newMap = new Map(prev);
      newMap.set(guid, {
        currentData,
        list,
      });
      return newMap;
    });
  };
  const getAllSpacesList = useCallback(() => {
    if (objCacheRef.current?.[1]?.list.length) return;
    return Promise.all([getSpaces({}), getSpacePinList()])
      .then((res) => {
        const spaceList = res[0]?.data?.spaces || [];
        const spaceTopList = res[1]?.data?.spaces || [];
        changeObjCache(guid!, visibleData.data, [desktop, ...spaceTopList, ...spaceList]);
      })
      .catch(() => {
        return [];
      });
  }, [guid, visibleData.data, setVisibleData]);
  const filterFileName = useCallback((name: string) => {
    const ext = path.extname(name);
    const baseName = path.basename(name, ext);
    return `${baseName} ${fm2('FilePathPicker.copy')}${ext}`;
  }, []);
  const getPrevKey = (objCache: ObjCacheModel, currentKey: string): string | undefined => {
    let prev: string | undefined = undefined;
    for (const key of objCache.keys()) {
      if (key === currentKey) {
        return prev;
      }
      prev = key;
    }
    return undefined;
  };
  const cancel = useCallback(
    (bol: boolean = false) => {
      if (bol) {
        setObjCache((prevCache) => {
          const newCache = new Map(prevCache);
          newCache.clear();
          return newCache;
        });
        setVisibleData({
          bol: false,
          key: undefined,
          data: undefined,
        });
      } else {
        const preGuid = getPrevKey(objCache, guid!);
        const preData = objCache.get(preGuid!)?.currentData;
        if (!visibleData.data) {
          cancel(true);
          return;
        }
        setVisibleData((oldPreData) => {
          return {
            ...oldPreData,
            data: preData,
          };
        });
      }
    },
    [setVisibleData, guid, objCache, visibleData.data],
  );

  function checkMoveAuth({
    isAdmin,
    curFolder,
    role,
    sourceFileParentGuid,
  }: {
    isAdmin: boolean;
    curFolder?: Partial<FileDataModel>;
    role: string;
    sourceFileParentGuid: string;
  }) {
    let tip = '';
    let auth = true;
    // 相同位置时不能移动
    if (curFolder?.guid === sourceFileParentGuid) {
      tip = fm2('FilePathPicker.targetLocationExistFile');
    } else if (isAdmin) {
      if (curFolder) {
        // 当前位置不可编辑
        if (curFolder.role && curFolder.role !== 'editor') {
          tip = fm2('FilePathPicker.noMoveToTargetLocationTip');
          auth = false;
        }
      }
    }
    // 普通协作者
    else if (role !== 'none') {
      // 有操作权限
      if (role === 'editor') {
        if (curFolder) {
          // 当前位置可编辑
          if (curFolder.role === 'editor') {
            tip = fm2('FilePathPicker.noMoveFilePermissionTip');
            auth = false;
          } else {
            tip = fm2('FilePathPicker.noMoveToTargetLocationTip');
            auth = false;
          }
        }
      } else {
        // 没有操作权限
        tip = '';
        auth = false;
      }
    }
    return { tip, auth };
  }
  async function moveFile(locationGuid: string, fileGuid: string, callback?: () => void) {
    const params = {
      entries: [
        {
          to: locationGuid,
          fileGuid: fileGuid,
        },
      ],
    };
    const [err, res] = await to(moveApi(params));
    if (res?.status !== 204) {
      Toast.show({
        icon: 'fail',
        content: err?.data?.msg,
      });
      return;
    }
    callback?.();
  }
  useEffect(() => {
    objCacheRef.current = objCache.entries().next().value;
  }, [objCache]);
  const fileList = useMemo(() => {
    return objCache.get(guid!)?.list || [];
  }, [guid, objCache]);
  return {
    cancel,
    goRefreshFileList,
    refreshFileList,
    guid,
    fileList,
    getAllSpacesList,
    filterFileName,
    checkMoveAuth,
    moveFile,
    objCache,
    changeObjCache,
  };
};
