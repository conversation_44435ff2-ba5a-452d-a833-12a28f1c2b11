import type { ColumnsType } from 'antd/es/table';
import { useCallback, useMemo } from 'react';

type Props<T> = {
  columns?: ColumnsType<T>;
  tableWidth: number;
  isRowSelection: boolean;
};
export const useXTable = <T extends object>({ columns, tableWidth, isRowSelection = false }: Props<T>) => {
  const newTableWidth = isRowSelection ? tableWidth - 40 - 40 : tableWidth - 40;
  const averagePerMinute = useMemo(() => {
    if (!columns?.length) return 1;
    if (columns?.length - 1 === 2) {
      return Math.floor(newTableWidth / 4);
    } else if (columns?.length - 1 === 3) {
      return Math.floor(newTableWidth / 6);
    } else if (columns?.length - 1 === 4) {
      return Math.floor(newTableWidth / 7);
    } else {
      return 1;
    }
  }, [columns?.length, newTableWidth]);

  const widths = useMemo(() => {
    if (columns?.length) {
      if (columns?.length - 1 === 2) {
        return [3, 1];
      } else if (columns?.length - 1 === 3) {
        return [3, 1, 2];
      } else if (columns?.length - 1 === 4) {
        return [3, 1, 2, 1];
      } else {
        return [];
      }
    } else {
      return [];
    }
  }, [columns?.length]);

  const changeColumnsProportion = useCallback(() => {
    if (!columns) return [];
    if (columns.length - 1 === 2) {
      columns?.forEach((item, index) => {
        if (item.title) {
          item.width = widths[index] * averagePerMinute;
        } else {
          item.width = 40;
        }
      });
    } else if (columns.length - 1 === 3) {
      columns?.forEach((item, index) => {
        if (item.title) {
          item.width = widths[index] * averagePerMinute;
        } else {
          item.width = 40;
        }
      });
    } else if (columns?.length - 1 === 4) {
      columns?.forEach((item, index) => {
        if (item.title) {
          item.width = widths[index] * averagePerMinute;
        } else {
          item.width = 40;
        }
      });
    } else {
      return columns;
    }
    return columns;
  }, [widths, averagePerMinute, columns]);
  const newColumns = useMemo(() => {
    return changeColumnsProportion();
  }, [changeColumnsProportion]);

  return {
    columns: newColumns,
  };
};
