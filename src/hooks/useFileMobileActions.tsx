import { Dialog, SpinLoading, Toast } from 'antd-mobile';
import { useCallback, useState } from 'react';

import { deleteCollaboration } from '@/api/Collaboration';
import {
  addQuickAccess,
  cancelStar,
  deleteFile,
  deleteRecentFile,
  deleteTrash,
  importFile,
  importFileProgress,
  recoveryFile,
  removeQuickAccess,
  rename,
  saveTemplates,
  star,
} from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { deleteSpace } from '@/api/Space';
import CreateFileModal from '@/components-mobile/Modal';
import type { CloudImportExt } from '@/constants/fileTypes';
import MoTypeActions from '@/contexts/MoTypeActions';
import type { FileDataModel } from '@/model/FileList';
import { fm2, useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { openFile } from '@/utils/file';

import { useFileTypeDownload } from './useFileTypeDownload';
interface RenameDlgProps {
  guid: string;
  name: string;
}

interface CollectionStarProps {
  guid: string;
  isStar: boolean;
}

interface DeleteFileProps {
  isSpace?: boolean;
  guid: string;
  name: string;
  type: string;
}

interface QuickFileProps {
  guid: string;
  isQuick: boolean;
}

interface PermanentlyDeleteFileFuncProp {
  name: string;
  guid: string;
}

export const getFileTypeName = (type: string) => {
  switch (type) {
    case 'newdoc':
    case 'docx':
    case 'doc':
    case 'txt':
      return fm2('File.newdoc');
    case 'modoc':
      return fm2('File.modoc');
    case 'spreadsheet':
    case 'mosheet':
    case 'xls':
    case 'xlsx':
      return fm2('File.mosheet');
    case 'table':
      return fm2('File.table');
    case 'presentation':
    case 'ppt':
    case 'pptx':
      return fm2('File.presentation');
    case 'folder':
      return fm2('File.folder');
    case 'space':
      return fm2('SiderMenu.siderMenuCreateSpace');
    case 'form':
      return fm2('File.form');
    case 'board':
      return fm2('File.board');
    case 'pdf':
      return fm2('search.pdf');
    case 'img':
    case 'jpg':
    case 'gif':
    case 'svg':
      return fm2('File.picture');
    case 'zip':
    case 'rar':
      return fm2('File.package');
    case 'shortcut':
      return fm2('File.shortcut');
    default:
      return fm2('File.file');
  }
};
export const useFileMobileActions = () => {
  const saveTemplate = (prop: RenameDlgProps): Promise<void> => {
    return new Promise((resolve) => {
      CreateFileModal.show({
        type: 'input',
        initialValues: { filename: prop.name },
        title: fm2('SaveTemplate.title'),
        onConfirm: async (filename) => {
          saveTemplates({ originGuid: prop.guid, name: filename })
            .then((res) => {
              if (res.status === 200) {
                Toast.show({ icon: 'success', content: fm2('SaveTemplate.success') });
                resolve();
              }
            })
            .catch((err) => {
              Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
            });
        },
      });
    });
  };

  const renameFile = (prop: RenameDlgProps): Promise<void> => {
    const setSubName = (fileName: string) => {
      let suffix = '';
      let newName = '';
      if (fileName) {
        const index = fileName.lastIndexOf('.');
        if (index >= 0) {
          newName = fileName.slice(0, index);
          const after = fileName.substring(index);
          suffix = after;
        } else {
          newName = fileName;
        }
      }
      return {
        name: newName,
        suffix,
      };
    };
    return new Promise((resolve) => {
      CreateFileModal.show({
        type: 'input',
        initialValues: { filename: setSubName(prop.name).name },
        title: fm2('File.reName'),
        onConfirm: async (filename) => {
          rename(prop.guid, `${filename}${setSubName(prop.name).suffix}`)
            .then((res) => {
              if (res.status === 200) {
                Toast.show({ icon: 'success', content: fm2('RenameModal.editSuccess') });
                resolve();
              }
            })
            .catch((err) => {
              Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
            });
        },
      });
    });
  };

  const collectionStar = (prop: CollectionStarProps): Promise<void> => {
    return new Promise((resolve) => {
      if (prop.isStar) {
        cancelStar(prop.guid)
          .then((res) => {
            if (res.status === 204) {
              Toast.show({ icon: 'success', content: fm2('File.removeSuccess') });
              resolve();
            }
          })
          .catch((err) => {
            Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
          });
      } else {
        star(prop.guid)
          .then((res) => {
            if (res.status === 204) {
              Toast.show({ icon: 'success', content: fm2('File.starSuccess') });
              resolve();
            }
          })
          .catch((err) => {
            Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
          });
      }
    });
  };

  const getFileTypeName = (type: string) => {
    switch (type) {
      case 'newdoc':
      case 'docx':
      case 'doc':
      case 'txt':
        return fm2('File.newdoc');
      case 'modoc':
        return fm2('File.modoc');
      case 'spreadsheet':
      case 'mosheet':
      case 'xls':
      case 'xlsx':
        return fm2('File.mosheet');
      case 'table':
        return fm2('File.table');
      case 'presentation':
      case 'ppt':
      case 'pptx':
        return fm2('File.presentation');
      case 'folder':
        return fm2('File.folder');
      case 'space':
        return fm2('SiderMenu.siderMenuCreateSpace');
      case 'form':
        return fm2('File.form');
      case 'board':
        return fm2('File.board');
      case 'pdf':
        return fm2('search.pdf');
      case 'img':
      case 'jpg':
      case 'gif':
      case 'svg':
        return fm2('File.picture');
      case 'zip':
      case 'rar':
        return fm2('File.package');
      case 'shortcut':
        return fm2('File.shortcut');
      default:
        return fm2('File.file');
    }
  };

  const deleteFileFunc = (prop: DeleteFileProps): Promise<void> => {
    const typeName = `${getFileTypeName(prop.type)}「${prop.name}」`;
    return new Promise((resolve) => {
      Dialog.confirm({
        className: prop.isSpace ? 'space-delete-confirm' : 'file-delete-confirm',
        content: prop.isSpace
          ? fm2('Space.teamspaceDeleteContentMobile')
          : fm2('deleteConfirm.mobile-title', { typeName }),
        confirmText: fm2('deleteConfirm.title'),
        title: prop.isSpace ? fm2('Space.teamspaceDeleteTitleMobile') : null,
        onConfirm: () => {
          const api = prop.isSpace ? deleteSpace : deleteFile;
          api(prop.guid)
            .then(() => {
              Toast.show({ icon: 'success', content: fm2('deleteConfirm.success') });
              resolve();
            })
            .catch((err) => {
              Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
            });
        },
      });
    });
  };

  const permanentlyDeleteFileFunc = (prop: PermanentlyDeleteFileFuncProp): Promise<void> => {
    return new Promise((resolve) => {
      Dialog.confirm({
        className: 'file-delete-confirm',
        content: fm2('File.deleteCompletelyContent', { name: prop.name }),
        confirmText: fm2('File.deleteCompletely'),
        title: fm2('File.deleteCompletely') + prop.name,
        onConfirm: () => {
          const api = deleteTrash;
          api(prop.guid)
            .then(() => {
              Toast.show({ icon: 'success', content: fm2('deleteConfirm.success') });
              resolve();
            })
            .catch((err) => {
              Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
            });
        },
      });
    });
  };

  const [open, setOpen] = useState(true);
  const { downloadDiffFile, getSecondActions, getEditSecondActions } = useFileTypeDownload();
  const downloadFileFunc = (downloadType: string, item: FileDataModel): Promise<void> => {
    return new Promise((resolve) => {
      if (downloadType === 'moType') {
        MoTypeActions.show({
          type: item.type,
          open,
          guid: item.guid,
          fileData: item,
          name: item.name,
          downloadDiffFile,
          getSecondActions,
          close: () => setOpen(false),
        });
        resolve();
      } else {
        downloadDiffFile({ type: 'downloadOther', guid: item.guid, fileName: item.name });
      }
    });
  };
  const clearLastRecord = (record: FileDataModel): Promise<void> => {
    return new Promise((resolve) => {
      deleteRecentFile(record['guid'])
        .then(() => {
          Toast.show({ icon: 'success', content: fm2('File.recordClearSuccess') });
          resolve();
        })
        .catch((err) => {
          Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
        });
    });
  };

  const toggleFileFromQuick = (prop: QuickFileProps): Promise<void> => {
    return new Promise((resolve) => {
      if (prop.isQuick) {
        //todo
      } else {
        //todo
      }
      resolve();
    });
  };

  const me = useMeStore((state) => state.me);
  const exitShare = (prop: DeleteFileProps): Promise<void> => {
    const typeName = `${getFileTypeName(prop.type)}「${prop.name}」`;
    return new Promise((resolve) => {
      Dialog.confirm({
        className: 'file-delete-confirm',
        content: fm2('deleteConfirm.mobile-share', { typeName }),
        confirmText: fm2('File.exitShareSure'),
        title: fm2('File.exitShare'),
        onConfirm: () => {
          deleteCollaboration(prop.guid, me.id as number)
            .then(() => {
              Toast.show({ icon: 'success', content: fm2('File.exitSuccess') });
              resolve();
            })
            .catch((err) => {
              Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
            });
        },
      });
    });
  };

  const recoveryFileFunc = (guid: string): Promise<void> => {
    return new Promise((resolve) => {
      recoveryFile(guid)
        .then(() => {
          Toast.show({ icon: 'success', content: fm2('useRecoverFile.resetSuccess') });
          resolve();
        })
        .catch((err) => {
          Toast.show({ icon: 'fail', content: err?.data?.msg || fm2('Members.operationFailed') });
        });
    });
  };

  const downloadEditFileFunc = (item: FileDataModel): Promise<void> => {
    return new Promise((resolve) => {
      MoTypeActions.show({
        type: item.type,
        open,
        guid: item.guid,
        name: item.name,
        fileData: item,
        downloadDiffFile,
        from: 'editor',
        getEditSecondActions,
        close: () => setOpen(false),
      });
      resolve();
    });
  };
  const i18n_fileLoadError = useFormatMessage('Editor.loadingFileError');
  const i18n_fileLoaded = useFormatMessage('Editor.fileLoaded');
  const i18n_openLater = useFormatMessage('Editor.openLater');
  const i18n_openFile = useFormatMessage('Editor.openFile');
  const i18n_fileIsLoading = useFormatMessage('Editor.loadingFile');

  const showError = useCallback(() => {
    Toast.show({
      content: i18n_fileLoadError,
    });
  }, [i18n_fileLoadError]);

  const convertToOnlineFile = useCallback(
    async (fileDetail: FileDataModel, callback?: () => void) => {
      const { guid, subType, type, url } = fileDetail;

      // 开始导入
      const [importErr, importRes] = await catchApiResult(importFile(guid, subType as CloudImportExt));

      if (importErr) {
        showError();
        return;
      }

      const taskId = importRes?.data.data.taskId;
      if (!taskId) {
        showError();
        return;
      }

      // 轮询进度
      const pollImportProgress = (taskId: string) => {
        const interval = 50;

        const { close } = Toast.show({
          duration: 0,
          content: (
            <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column', padding: '12px' }}>
              <SpinLoading />
              <span style={{ marginTop: '8px' }}>{i18n_fileIsLoading}</span>
            </div>
          ),
        });

        const check = async () => {
          const [progressErr, progressRes] = await catchApiResult(importFileProgress(taskId));
          if (progressErr) {
            close();
            showError();
            return;
          }

          if (progressRes?.data.data.progress >= 100) {
            close();
            callback?.();
            Dialog.confirm({
              title: i18n_fileLoaded,
              cancelText: <span style={{ color: 'var(--theme-basic-color-primary)' }}>{i18n_openLater}</span>,
              confirmText: i18n_openFile,
              onConfirm: () => {
                openFile({ type, guid, url, model: 'default' });
              },
            });
          } else {
            setTimeout(check, interval);
          }
        };

        check();
      };

      pollImportProgress(taskId);
    },
    [showError, i18n_fileIsLoading, i18n_fileLoaded, i18n_openLater, i18n_openFile],
  );

  const removeMobileQuickAccess = useCallback((item: FileDataModel) => {
    return new Promise((resolve) => {
      removeQuickAccess(item.guid)
        .then(() => {
          Toast.show({ icon: 'success', content: fm2('QuickAccess.removeSuccess', { name: item.name }) });
          resolve(true);
        })
        .catch((err) => {
          Toast.show({ icon: 'fail', content: err?.data?.msg });
        });
    });
  }, []);

  const addMobileQuickAccess = useCallback((item: FileDataModel) => {
    return new Promise((resolve) => {
      addQuickAccess(item.guid)
        .then(() => {
          Toast.show({ icon: 'success', content: fm2('QuickAccess.addSuccess', { name: item.name }) });
          resolve(true);
        })
        .catch((err) => {
          Toast.show({ icon: 'fail', content: err?.data?.msg });
        });
    });
  }, []);

  return {
    saveTemplate,
    renameFile,
    collectionStar,
    deleteFileFunc,
    permanentlyDeleteFileFunc,
    downloadFileFunc,
    clearLastRecord,
    exitShare,
    toggleFileFromQuick,
    recoveryFileFunc,
    downloadEditFileFunc,
    convertToOnlineFile,
    addMobileQuickAccess,
    removeMobileQuickAccess,
  };
};
