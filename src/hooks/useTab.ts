import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import type { SpaceEntity, TabModel } from '@/model/FileList';
import type { FileDataModel } from '@/model/FileList';
import { fm } from '@/modules/Locale';

export function useTab<K extends string, Q, D>(
  staticTab: TabModel<K, Q, D>,
  setLoading: (loading: boolean) => void,
  refreshFileList?: number,
  defaultTabValue?: K,
) {
  const text = fm('Space.createSpace');
  const [sortRefresh, setSortRefresh] = useState(0);
  const prevRefresh = useRef(refreshFileList);
  const [tabs, setTabs] = useState<TabModel<K, Q, D>>(staticTab);
  const [activeTab, setActiveTab] = useState<K>((defaultTabValue as K) || (Object.keys(staticTab)[0] as K));
  const gitList = useCallback(
    async (isRefresh: boolean = false) => {
      if (!isRefresh) {
        if (tabs[activeTab].list.length) {
          return;
        }
      }
      setLoading(true);
      try {
        const param = tabs[activeTab]?.param;
        const api = tabs[activeTab]?.api;
        const res = await api?.(param);
        if (res?.status === 200) {
          const tempList = (
            'list' in res.data ? res.data.list || [] : 'spaces' in res.data ? res.data.spaces || [] : res.data || []
          ) as FileDataModel[];
          let list = tempList;
          if (['space'].includes(activeTab)) {
            tempList.unshift({
              name: text,
              guid: '',
              updatedAt: 0,
              isFolder: false,
              createdAt: 0,
              type: 'folder',
              url: '',
              starred: false,
              subType: 'folder',
              lastAction: 'open',
              isSpace: true,
              updatedUser: {
                avatar: '',
                email: '',
                id: 0,
                name: '',
              },
            });
            list = tempList.map((l) => {
              return { ...l, isSpace: true };
            });
          }
          setSortRefresh((pre) => pre + 1);
          setTabs((prev) => ({
            ...prev,
            [activeTab]: { ...prev[activeTab], list },
          }));
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    },
    [activeTab, setLoading, text, tabs[activeTab]?.param],
  );

  const list = useMemo(() => {
    return (
      tabs[activeTab].list?.map((item) => {
        if (['space'].includes(activeTab)) {
          const file = item as SpaceEntity & {
            updatedUser: {
              id: number;
              name: string;
            };
          };
          const updatedUser = file.updatedUser ?? {
            id: file?.updated?.user.id,
            name: file.updated.user.name,
          };

          return {
            ...file,
            updatedUser,
            guid: file.guid,
            lastAction: 'open',
            url: '',
            isDesktop: file.isDesktop,
            updatedAt: file.updatedAt,
            createdAt: file.createdAt,
            name: file.name,
            starred: file.starred,
            type: file.type,
          } as unknown as FileDataModel;
        } else {
          const file = item as FileDataModel;
          return {
            ...file,
            updatedUser: file.updatedUser,
            guid: file.guid,
            url: file.url,
            isSpace: file?.isSpace,
            lastAction: file.lastAction,
            updatedAt: (file?.updatedAt || 0) * 1000,
            isFolder: file.isFolder || false,
            createdAt: (file?.createdAt || 0) * 1000,
            isDesktop: file.isDesktop,
            name: file.name,
            user: file.user,
            userId: file.userId,
            sharedAt: (file?.sharedAt || 0) * 1000,
            sharedUser: file.sharedUser,
            starred: file.starred,
            type: file.type,
            subType: file.subType,
          };
        }
      }) || []
    );
  }, [activeTab, tabs]);
  useEffect(() => {
    setTabs(staticTab);
  }, [staticTab]);
  useEffect(() => {
    tabs[activeTab].list = [];
    if (prevRefresh.current !== refreshFileList) {
      gitList(true);
      prevRefresh.current = refreshFileList;
    } else {
      gitList();
    }
  }, [gitList, refreshFileList, tabs[activeTab].param, activeTab]);
  return {
    sortRefresh,
    tabs,
    activeTab,
    setActiveTab,
    setTabs,
    list,
  };
}
