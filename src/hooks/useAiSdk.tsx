import { useCallback, useEffect } from 'react';

import aiPng from '@/assets/images/ai/help.png';
import type { AiSDK, APIConfig, EditorTypeEnum } from '@/components/EditorSDK/AI';
import { EditorTypeEnum as MyEditorTypeEnum, FileType } from '@/components/EditorSDK/AI';
import { CustomEventName } from '@/model/CustomEvent';
import { fm, fm2, getStoredLocale } from '@/modules/Locale';
import type { AiSDKProp } from '@/store/TemplateLib';
import { useTemplateStore } from '@/store/TemplateLib';
import { onCustomEvent } from '@/utils/customEvent';
import { adaptor } from '@/utils/ShimoSDK/APIAdaptor';

export const applyFileTypes: FileType[] = [FileType.newdoc, FileType.presentation];

function getApiConfig(): Record<string, APIConfig> {
  const api: Record<string, APIConfig> = {
    /**
     * 获取AI模型
     */
    getAIModels: {
      url: '/ai/conversation/models',
      method: 'GET',
    },
    /**
     * 会话
     */
    createConversation: {
      url: '/ai/conversation',
      method: 'POST',
    },
    /**
     * 停止生成会话内容
     */
    breakConversation: {
      url: '/ai/conversation/{conversationId}/break',
      method: 'POST',
    },
    /**
     * 输入聊天(流式接口)
     */
    chat: {
      url: '/ai/conversation/{conversationId}/chat',
      method: 'POST',
    },

    /**
     * 获取文本信息
     */
    recognition: {
      url: '/ai/tools/recognition',
      method: 'POST',
    },

    /**
     * 对话列表  example: /api/chat
     */
    chatRecord: {
      url: '/ai/conversation',
      method: 'GET',
    },
    /**
     * 会话历史  /api/chat/{conversation_id}
     */
    chatHistory: {
      url: '/ai/conversation/{conversationId}',
      method: 'GET',
    },
  };

  const apiMap: Record<string, APIConfig> = {};

  Object.entries(api).forEach(([key, value]) => {
    apiMap[key] = adaptor({
      ...value,
      headers: value.headers || {},
      method: value.method || 'GET',
    });
  });

  return {
    ...api,
  };
}

export const useAiSdk = () => {
  const { templateProp, setTemplateProp } = useTemplateStore((state) => state);
  let removeListener: (() => void) | null = null;
  const aiName = fm('AI.aiName');

  const localLanguage = (getStoredLocale() as string) || 'zh-CN';

  const initWelcomeText = (editorType: EditorTypeEnum | string) => {
    let welcomeText = fm2('AI.welcomeText');
    if (editorType === MyEditorTypeEnum.doc) {
      //轻文档
    } else if (editorType === MyEditorTypeEnum.presentation) {
      //幻灯片
    } else {
      // list
      welcomeText = fm2('AI.welcomeText');
    }
    return welcomeText;
  };
  const initSdk = useCallback((detail: AiSDKProp): Promise<AiSDK> => {
    return new Promise((resolve) => {
      let sdk: AiSDK;
      const timer = setInterval(() => {
        if (window.LizardAISDK) {
          clearInterval(timer);
          const createSDK = window.LizardAISDK.createSDK;
          let prop = {};
          if (!detail?.guid) {
            //非套件
            prop = {};
          } else {
            prop = {
              fileGuid: detail.guid, // 文件id
              editorMode: detail.editorMode, // 'edit' | 'preview'
              editorSDK: detail.editorSDK, // 套件的实例
              editorType: detail.editorType,
            };
          }
          sdk = createSDK({
            ...prop,
            icon: aiPng,
            name: aiName,
            generateFileTypes: applyFileTypes,
            uploadFileTypes: applyFileTypes,
            i18n: {
              language: localLanguage,
              direction: 'ltr',
            },
            welcome: initWelcomeText(detail.editorType || ''),
            openTemplate: (props: { onOk: (templateId: string) => void; onCancel: () => void }) => {
              // 点击Ok
              setTemplateProp({ ...templateProp, propType: 'ai', isShowTemplateLib: true });
              removeListener = onCustomEvent<{ guid: string }>(CustomEventName.aiTemplateOk, (detail) => {
                props.onOk(detail.guid);
              });
              removeListener = onCustomEvent(CustomEventName.aiTemplateCancel, () => {
                props.onCancel();
              });
            },
            api: getApiConfig(),
          });

          resolve(sdk);
        }
      }, 100);
    });
  }, []);

  useEffect(() => {
    return () => {
      removeListener?.();
    };
  }, [removeListener]);

  return { initSdk };
};
