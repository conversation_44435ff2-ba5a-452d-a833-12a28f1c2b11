import { message } from 'antd';
import { Toast } from 'antd-mobile';

import { getDownloadUrl } from '@/api/File';
import { fm } from '@/modules/Locale';
import { exportFileType } from '@/utils/file';
import { sanitizeFilename } from '@/utils/tools';

export type DownLoadFileProps = {
  from?: string;
  type: string;
  guid: string;
  fileName: string;
};

/** 转换为指定的文档类型（仅用于创建的） */
export const downloadType = ['jpg', 'xmind', 'pptx', 'pdf', 'wps', 'docx', 'md', 'xlsx'];

export const useFileTypeDownload = () => {
  const i18nText = {
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
    loading: fm('Common.loadingText'),
    png: fm('File.png'),
    imagePdf: fm('File.imagePdf'),
  };

  const downloadDiffFile = ({ type, from = 'pc', guid, fileName }: DownLoadFileProps) => {
    const custDownload = (guid: string) => {
      getDownloadUrl(guid)
        .then(() => {
          if (from === 'mobile') Toast.show({ icon: 'success', content: i18nText.downloadSuccess });
          else message.success(i18nText.downloadSuccess);
        })
        .catch(() => {
          if (from === 'mobile') Toast.show({ icon: 'fail', content: i18nText.downloadError });
          else message.error(i18nText.downloadError);
        });
    };

    if (type === 'downloadOther') {
      // 下载自定义上传的文件
      custDownload(guid);
      return;
    }

    if (downloadType.includes(type)) {
      exportFileType({ guid: guid, data: { type }, name: sanitizeFilename(fileName) })
        .then(() => {
          if (from === 'mobile') Toast.show({ icon: 'success', content: i18nText.downloadSuccess });
          else message.success(i18nText.downloadSuccess);
        })
        .catch((error) => {
          if (from === 'mobile') Toast.show({ icon: 'fail', content: error.data.msg || i18nText.downloadError });
          else message.error(error.data.msg || i18nText.downloadError);
        });
    } else {
      custDownload(guid);
    }
  };

  const getSecondActions = (type: string) => {
    return [
      {
        label: i18nText.png,
        key: 'jpg',
        hidden: !['mindmap', 'newdoc'].includes(type) || ['img'].includes(type), // mindmap
      },
      {
        label: 'Xmind',
        key: 'xmind',
        hidden: !['mindmap'].includes(type) || ['img'].includes(type), // mindmap
      },
      {
        label: 'PPTX',
        key: 'pptx',
        hidden: !['presentation'].includes(type) || ['img'].includes(type), // presentation
      },
      {
        label: 'PDF',
        key: 'pdf',
        hidden: !['presentation', 'newdoc', 'modoc'].includes(type) || ['img'].includes(type), //presentation
      },
      {
        label: 'Excel',
        key: 'xlsx',
        hidden: !['mosheet'].includes(type) || ['img'].includes(type), // mosheet
      },
      {
        label: 'WPS',
        key: 'wps',
        hidden: !['modoc'].includes(type) || ['img'].includes(type), // modoc
      },
      {
        label: 'Word',
        key: 'docx',
        hidden: !['modoc', 'newdoc'].includes(type) || ['img'].includes(type), // modoc
      },
      {
        label: 'MarkDown',
        key: 'md',
        hidden: !['newdoc'].includes(type) || ['img'].includes(type), // newdoc
      },
    ];
  };

  const getEditSecondActions = (type: string) => {
    return [
      {
        label: i18nText.png,
        key: 'jpg',
        hidden: ['table', 'mosheet'].includes(type), // table  编辑器内的表格 没有图片下载797
      },
      {
        label: 'Xmind',
        key: 'xmind',
        hidden: !['mindmap'].includes(type),
      },
      {
        label: 'PPTX',
        key: 'pptx',
        hidden: !['presentation'].includes(type), // presentation
      },
      {
        label: 'PDF',
        key: 'pdf',
        hidden: !['presentation', 'newdoc', 'mosheet'].includes(type),
      },
      {
        label: 'Excel',
        key: 'xlsx',
        hidden: !['mosheet', 'table'].includes(type),
      },
      {
        label: 'WPS',
        key: 'wps',
        hidden: !['modoc'].includes(type),
      },
      {
        label: 'Word',
        key: 'docx',
        hidden: !['modoc', 'newdoc'].includes(type),
      },
      {
        label: 'MarkDown',
        key: 'md',
        hidden: !['newdoc'].includes(type),
      },
      {
        label: i18nText.imagePdf,
        key: 'imagePdf',
        hidden: !['modoc', 'presentation'].includes(type),
      },
    ];
  };
  return {
    downloadDiffFile,
    getSecondActions,
    getEditSecondActions,
  };
};
