import type { Key } from 'react';
import { useLocation } from 'umi';

type KeyModel = {
  key?: Key;
};
type BaseTypeModel = {
  shareMode?: string;
  role?: string;
};
export const useMenuItem = (arr: string[]) => {
  const location = useLocation();

  const getContextMenuItems = <R extends KeyModel, T extends BaseTypeModel>(list: R[], data: T): R[] => {
    if (!(data.shareMode !== 'private' && !data.role && location.pathname.includes('/recent'))) {
      return list;
    }
    const keyMap = new Map<Key, R>();
    list.forEach((item) => keyMap.set(item.key!, item));
    return arr.map((key) => keyMap.get(key)).filter((item): item is R => item !== undefined);
  };
  return {
    getContextMenuItems,
  };
};
