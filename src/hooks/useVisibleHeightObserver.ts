import { useEffect, useRef, useState } from 'react';

export const useVisibleHeightObserver = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [visibleHeight, setVisibleHeight] = useState(0);
  const [visibleWidth, setVisibleWidth] = useState(0);

  useEffect(() => {
    const container = ref.current;
    // 增加类型检查，确保 container 是有效的 DOM 节点
    if (!container || !(container instanceof HTMLDivElement)) {
      console.warn('Container element not found or is not a valid DOM node');
      return;
    }

    const handleResize = () => {
      setVisibleHeight(container.clientHeight);
      setVisibleWidth(container.clientWidth);
    };

    // 初始设置高度
    handleResize();

    // 添加 resize 事件监听
    window.addEventListener('resize', handleResize);

    // 使用 ResizeObserver
    let observer: ResizeObserver;
    if (window.ResizeObserver) {
      observer = new ResizeObserver(handleResize);

      try {
        observer.observe(container);
      } catch (error) {
        console.error('Failed to observe container:', error);
      }
    }

    // 清理函数
    return () => {
      if (observer) {
        observer.disconnect();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [ref]);

  return { ref, visibleHeight, visibleWidth };
};
