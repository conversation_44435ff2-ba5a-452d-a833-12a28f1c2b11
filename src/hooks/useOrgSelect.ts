import debounce from 'lodash/debounce';
import { useCallback, useMemo, useRef, useState } from 'react';

import type { CrumbsType, OrgItemType, OrgSelectAll } from '@/components/OrgSelectCard/type';
import { DEBOUNCE_INPUT_WAIT, DEFAULT_PAGE_CURRENT, DEFAULT_PAGE_SIZE, ROOT_DEPARTMENT, ZERO } from '@/configs/configs';
import { convertCrumbs, convertOrgList } from '@/utils/orgSelect';

import { getOrgTree, getUserList } from '../service/userOrg/service';
import { OrgType } from '../service/userOrg/type';
import { useOrgSelectSearch } from './useOrgSelectSearch';

export interface OrgSelectType {
  loading: boolean;
  searchLoading: boolean;
  crumbs?: CrumbsType[];
  orgList: OrgItemType[];
  searchResult?: OrgItemType[];
  selectedList: OrgItemType[];
  orgSelectAll: OrgSelectAll;
  cancelSelected: (id: number) => void;
  clearAllSelected: () => void;
  onScroll: () => void;
  initSelect: (initList?: OrgItemType[]) => void;
  clearOrgList: () => void;
  onSearch: (value: string) => void;
  clearSearch: () => void;
}

interface OrgSelectParams {
  pageSize?: number;
  checkDepartment?: boolean; // 是否可以选择企业，默认为 true
  searchUserConfig?: {
    includeDisabledMember?: boolean;
    includeRecentContact?: boolean;
    includeTeamMember?: boolean;
  };
  ignorePending?: boolean; // 忽略「邀请中」的用户
  admin?: boolean; // 是否拥有管理权限（无视组织架构可见性的过滤），默认为 false
  enableSelectAllEnterprise?: boolean;
}
export function useOrgSelect({
  pageSize = DEFAULT_PAGE_SIZE,
  checkDepartment = true,
  searchUserConfig,
  ignorePending,
  admin = false,
}: OrgSelectParams): OrgSelectType {
  const [loading, setLoading] = useState(false);
  const [orgList, setOrgList] = useState<OrgItemType[]>([]);
  const [selectedList, setSelectedList] = useState<OrgItemType[]>([]);
  const [currentCrumb, setCurrentCrumb] = useState(ROOT_DEPARTMENT);
  const [crumbs, setCrumbs] = useState<CrumbsType[]>();
  const pageRef = useRef(DEFAULT_PAGE_CURRENT);
  const isEndRef = useRef(false);
  const isPendingRef = useRef(false);
  const search = useOrgSelectSearch({
    selectedList,
    checkDepartment,
    searchUserConfig,
    admin,
  });

  const getOrgList = useCallback(
    (params: { page: number; departmentId: number; init?: OrgItemType[]; ignorePending?: boolean }) => {
      if (isPendingRef.current) {
        return;
      }
      isPendingRef.current = true;
      getOrgTree({ perPage: pageSize, ...params }, admin)
        .then((res) => {
          if (res !== null) {
            const { tree } = res;
            setCurrentCrumb(tree.id);
            setCrumbs((list) =>
              convertCrumbs({
                list: list ?? [],
                crumb: { id: tree.id, title: tree.title },
              }),
            );
            setOrgList((list) =>
              pageRef.current === params.page
                ? list.concat(
                    convertOrgList({
                      list: tree?.children,
                      checkDepartment,
                    }),
                  )
                : list,
            );
            pageRef.current = DEFAULT_PAGE_CURRENT;
            isEndRef.current = false;
          } else {
            setCrumbs(undefined);
            setOrgList([]);
          }
        })
        .finally(() => {
          isPendingRef.current = false;
          setLoading(false);
        });
    },
    [pageSize, checkDepartment, admin],
  );

  const addSelected = useCallback((addList: OrgItemType[]) => {
    const updateSelectedHandler = (arr: OrgItemType[], cur: OrgItemType) => {
      if (!arr.find((item) => item.id === cur.id)) {
        arr.push(cur);
      }
      return arr;
    };
    setSelectedList((list) => {
      return [...list, ...addList].reduce(updateSelectedHandler, [] as OrgItemType[]);
    });
  }, []);

  const clearAllSelected = useCallback(() => {
    setSelectedList([]);
    search.clearAllSelected();
  }, [search]);

  const cancelSelected = useCallback(
    (id: number) => {
      setSelectedList((list) => list.filter((item) => item.id !== id));
    },
    [search],
  );

  const onPressDepartment = useCallback(
    (id: number) => {
      setLoading(true);
      setOrgList([]);
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: id,
        ignorePending: ignorePending,
      });
    },
    [getOrgList, ignorePending],
  );

  const onCheckHandler = useCallback(
    (org: OrgItemType) => {
      if (!org.checked) {
        setSelectedList((list) => list.concat({ ...org, checked: true }));
        return;
      }
      cancelSelected(org.id);
    },
    [cancelSelected],
  );

  const clickCrumbsHandler = useCallback(
    (id: number) => {
      setLoading(true);
      setOrgList([]);
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: id,
        ignorePending,
      });
    },
    [getOrgList, ignorePending],
  );

  const orgSelectAll = useMemo((): OrgSelectAll => {
    const isSelected = (item: OrgItemType) => selectedList.some((selected) => selected.id === item.id);
    const checked = orgList.length > ZERO && orgList.every((item) => isSelected(item));
    const indeterminate = orgList.some((item) => isSelected(item)) && orgList.some((item) => !isSelected(item));
    return {
      checked,
      indeterminate,
      onPress: (value: boolean) => {
        if (!value) {
          orgList.forEach((item) => {
            cancelSelected(item.id);
          });
        } else {
          addSelected(orgList);
        }
      },
    };
  }, [orgList, cancelSelected, addSelected]);

  const onScroll = useCallback(() => {
    if (isEndRef.current || orgList.length === ZERO || isPendingRef.current) {
      return;
    }
    const page = pageRef.current + 1;
    pageRef.current = page;
    isPendingRef.current = true;
    getUserList(
      {
        page,
        perPage: pageSize,
        departmentId: currentCrumb,
        ignorePending: ignorePending,
      },
      admin,
    )
      .then(({ list }) => {
        if (!list || list?.length <= 0 || list.length < pageSize) {
          isEndRef.current = true;
        }
        setOrgList((oldList) =>
          pageRef.current === page
            ? oldList.concat(
                convertOrgList({
                  list,
                  checkDepartment,
                }),
              )
            : oldList,
        );
      })
      .catch(() => {
        isEndRef.current = true;
      })
      .finally(() => {
        isPendingRef.current = false;
      });
  }, [orgList, currentCrumb, getOrgList, checkDepartment, pageSize, ignorePending, admin]);

  const initSelect = useCallback(
    (initList?: OrgItemType[]) => {
      setLoading(true);
      if (initList) {
        addSelected(initList);
      }
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: ROOT_DEPARTMENT,
        init: initList,
        ignorePending,
      });
    },
    [getOrgList, clearAllSelected, ignorePending],
  );

  const clearOrgList = useCallback(() => {
    pageRef.current = DEFAULT_PAGE_CURRENT;
    isEndRef.current = false;
    setOrgList([]);
  }, []);

  return useMemo(() => {
    return {
      loading: loading,
      searchLoading: search.loading,
      crumbs: crumbs
        ? crumbs.map((item) => ({
            ...item,
            onClick:
              item.id === currentCrumb
                ? undefined
                : () => {
                    clickCrumbsHandler(item.id);
                  },
          }))
        : undefined,
      orgList: orgList.map((item) => {
        const basicItem = {
          ...item,
          checked: selectedList.some((selected) => selected.id === item.id),
        };
        return {
          ...basicItem,
          onPress:
            item.type === OrgType.Department
              ? () => {
                  onPressDepartment(item.id);
                }
              : undefined,
          onCheck: () => {
            onCheckHandler(basicItem);
          },
        };
      }),
      selectedList: selectedList.map((item) => ({
        ...item,
        onCancel: () => {
          cancelSelected(item.id);
        },
      })),
      orgSelectAll,
      clearAllSelected,
      cancelSelected,
      onScroll: debounce(onScroll, DEBOUNCE_INPUT_WAIT),
      initSelect,
      clearOrgList,
      onSearch: search.onSearch,
      clearSearch: search.clearSearch,
      searchResult: search.result
        ? search.result.map((item) => ({
            ...item,
            onCheck: () => {
              onCheckHandler(item);
            },
          }))
        : undefined,
    };
  }, [
    crumbs,
    orgList,
    loading,
    selectedList,
    cancelSelected,
    clickCrumbsHandler,
    clearAllSelected,
    orgSelectAll,
    onScroll,
    initSelect,
    clearOrgList,
    search,
    onCheckHandler,
  ]);
}
