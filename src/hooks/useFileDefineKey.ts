import type { AxiosResponse } from 'axios';
import { useCallback, useMemo } from 'react';
import { useParams } from 'umi';

import { files, getSpaces, getStarredFileList } from '@/api/File';
import { getSpacePinList } from '@/api/Space';
import type { FilesQuery } from '@/model/File';
import type { SortModel } from '@/model/FileList';
import { type FileDataModel, ModeTypeEnum, type sortTabItemModel } from '@/model/FileList';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

export const useFileDefineKey = (type?: string) => {
  const id = useMeStore((state) => state.me.id);
  const { guid } = useParams();
  const byCreationTimeText = fm('File.byCreationTime');
  const bySharingTimeText = fm('File.bySharingTime');
  const myDesktopText = fm('BackToPopover.myDesktop');
  const spaceText = fm('FilePathPicker.space');
  const usedText = fm('SearchCenter.used');
  const favoriteText = fm('Header.favorite');
  const siderMenuCreactText = fm('SiderMenu.siderMenuCreactText');
  const sharedText = fm('File.shared');
  const defaultSortText = fm('File.defaultSort');
  const byNewnessSortByText = fm('File.byNewnessSortBy');
  const sortByCreationTimeText = fm('File.sortByCreationTime');
  const sortByNameText = fm('File.sortByName');
  const sortByDeletionTimeText = fm('File.sortByDeletionTime');
  const sortByFileNameText = fm('File.sortByFileName');
  const emptyFavoritesText = fm('File.emptyFavorites');
  const emptyRecycle = fm('File.emptyRecycle');
  const emptyDesktop = fm('File.emptyDesktop');
  const emptyShare = fm('File.emptyShare');
  const emptyCreate = fm('File.emptyCreate');

  const i18nText = {
    created: byCreationTimeText,
    share: bySharingTimeText,
    myDesktop: myDesktopText,
    space: spaceText,
    used: usedText,
    favorite: favoriteText,
    siderMenuCreactText: siderMenuCreactText,
    shared: sharedText,
    defaultSort: defaultSortText,
    byNewnessSortBy: byNewnessSortByText,
    sortByCreationTime: sortByCreationTimeText,
    sortByName: sortByNameText,
    sortByDeletionTime: sortByDeletionTimeText,
    sortByFileName: sortByFileNameText,
    emptyFavorites: emptyFavoritesText,
    emptyRecycle,
    emptyDesktop,
    emptyShare,
    emptyCreate,
  };
  const sortKey = useMemo<Record<string, sortTabItemModel[]>>(
    () => ({
      created: [
        {
          name: i18nText.created,
          type: 'createTime',
          key: 'File.byCreationTime',
        },
      ],
      share: [
        {
          name: i18nText.share,
          type: 'sharedAt',
          key: 'File.bySharingTime',
        },
      ],
      space: [
        {
          name: i18nText.defaultSort,
          type: 'default',
          key: 'File.defaultSort',
        },
        {
          name: i18nText.byNewnessSortBy,
          type: 'updateTime',
          key: 'File.byNewnessSortBy',
        },
        {
          name: i18nText.sortByCreationTime,
          type: 'createTime',
          key: 'File.sortByCreationTime',
        },
        {
          name: i18nText.sortByName,
          type: 'name',
          key: 'File.sortByName',
        },
      ],
      recycle: [
        {
          name: i18nText.sortByDeletionTime,
          type: 'default',
          key: 'File.sortByDeletionTime',
        },
        {
          name: i18nText.sortByFileName,
          type: 'name',
          key: 'File.sortByFileName',
        },
      ],
    }),
    [
      i18nText.sortByDeletionTime,
      i18nText.sortByFileName,
      i18nText.created,
      i18nText.share,
      i18nText.sortByCreationTime,
      i18nText.sortByName,
      i18nText.byNewnessSortBy,
      i18nText.defaultSort,
    ],
  );
  const localStorageObj = useMemo<Record<string, string>>(
    () => ({
      created: `${id}-CREATED`,
      share: `${id}-SHARE`,
      space: `${id}-SPACE`,
      recent: `${id}-RECENT`,
      favorites: `${id}-FAVORITES`,
    }),
    [id],
  );

  const localStorageKey = useMemo(() => {
    const folderKey = `${id}-FOLDER`;
    if (!type) {
      return folderKey;
    }
    return type && localStorageObj[type] ? localStorageObj[type] : folderKey;
  }, [id, type, localStorageObj]);

  const sortList = useMemo(() => {
    return type ? sortKey[type] : undefined;
  }, [sortKey, type]);

  const getAllSpacesList = useCallback(() => {
    return Promise.all([getSpaces({}), getSpacePinList()]).then((res) => {
      const spaceList = res[0]?.data?.spaces || [];
      const spaceTopList = res[1]?.data?.spaces || [];
      return {
        status: 200,
        data: {
          list: [...spaceTopList.map((item: FileDataModel) => ({ ...item, isDesktop: true })), ...spaceList],
        },
      } as AxiosResponse<{ list: FileDataModel[] }>;
    });
  }, []);
  const starredFileListWrapper = useCallback((query: FilesQuery) => {
    return getStarredFileList({ orderBy: query.orderBy ?? 'updatedAt' });
  }, []);

  const allTabs = useMemo(() => {
    const obj = {
      desktop: {
        desktop: {
          name: i18nText.myDesktop,
          param: {
            folder: guid,
          },
          list: [],
          api: files,
          type: 'desktop',
        },
        space: {
          name: i18nText.space,
          param: {},
          list: [],
          api: getAllSpacesList,
          type: 'space',
        },
      },
      recent: {
        recent: {
          name: i18nText.used,
          param: {},
          list: [],
          type: 'recent',
        },
        favorites: {
          name: i18nText.favorite,
          param: {},
          list: [],
          type: 'favorites',
          api: starredFileListWrapper,
        },
        created: {
          name: i18nText.siderMenuCreactText,
          param: {
            type: 'created',
          },
          list: [],
          api: files,
          type: 'created',
        },
        share: {
          name: i18nText.shared,
          param: {
            type: 'shared',
          },
          list: [],
          api: files,
          type: 'share',
        },
      },
      templates: {
        customTemplates: {
          name: '我的模版',
          param: {},
          list: [],
          type: 'customTemplates',
        },
        publicTemplates: {
          name: '企业模版库',
          param: {},
          list: [],
          type: 'publicTemplates',
        },
      },
    };
    return obj;
  }, [
    guid,
    getAllSpacesList,
    starredFileListWrapper,
    i18nText.favorite,
    i18nText.myDesktop,
    i18nText.shared,
    i18nText.siderMenuCreactText,
    i18nText.space,
    i18nText.used,
  ]);

  const defaultStoredValue: Record<string, Partial<SortModel>> = {
    created: {
      name: i18nText.created,
      layout: ModeTypeEnum.list,
      selectedType: 'createTime',
      key: 'File.byCreationTime',
    },
    recent: {
      layout: ModeTypeEnum.list,
    },
    share: {
      name: i18nText.share,
      isFolderPinned: false,
      isReversed: true,
      layout: ModeTypeEnum.list,
      selectedType: 'sharedAt',
      key: 'File.bySharingTime',
    },
    favorites: {
      name: i18nText.defaultSort,
      isFolderPinned: true,
      isReversed: true,
      layout: ModeTypeEnum.list,
      selectedType: 'default',
      key: 'File.defaultSort',
    },
    desktop: {
      name: i18nText.defaultSort,
      isFolderPinned: true,
      isReversed: true,
      layout: ModeTypeEnum.list,
      selectedType: 'default',
      key: 'File.defaultSort',
    },
    space: {
      name: i18nText.defaultSort,
      isFolderPinned: false,
      isReversed: true,
      layout: ModeTypeEnum.list,
      selectedType: 'default',
      key: 'File.defaultSort',
    },
    recycle: {
      name: i18nText.sortByDeletionTime,
      selectedType: 'default',
      layout: ModeTypeEnum.list,
      key: 'File.sortByDeletionTime',
    },
  };

  const emptyAllText = useMemo<Record<string, string>>(() => {
    return {
      favorites: i18nText.emptyFavorites,
      recycle: i18nText.emptyRecycle,
      desktop: i18nText.emptyDesktop,
      share: i18nText.emptyShare,
      create: i18nText.emptyCreate,
    };
  }, [
    i18nText.emptyFavorites,
    i18nText.emptyRecycle,
    i18nText.emptyDesktop,
    i18nText.emptyShare,
    i18nText.emptyCreate,
  ]);
  const emptyText = useMemo(() => {
    const textKey = Object.keys(emptyAllText).find((item) => type?.includes(item));
    if (textKey) {
      return emptyAllText[textKey];
    }
    return '';
  }, [emptyAllText, type]);
  return {
    sortList,
    localStorageKey,
    allTabs,
    defaultStoredValue,
    emptyText,
  };
};
