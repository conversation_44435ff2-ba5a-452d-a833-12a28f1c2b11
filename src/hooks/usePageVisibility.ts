import { useEffect } from 'react';

import { useUploadStore } from '@/store/Upload';

// 自定义 Hook 用于监听窗口可见性
export const usePageVisibility = (refreshList: () => void) => {
  const { setIsCreateFile, isCreateFile } = useUploadStore((state) => state);
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isCreateFile) {
        refreshList();
        setIsCreateFile(false);
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refreshList]);
};
