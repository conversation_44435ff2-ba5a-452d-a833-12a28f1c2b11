// 定义滚动条变量，使用 CSS 变量替代 Less 变量，保持与项目一致性
:root {
  --scrollbar-width: 8px;
  --scrollbar-thumb-color: var(--theme-text-color-secondary);
  --scrollbar-thumb-hover: var(--theme-text-color-default);
  --scrollbar-radius: 4px;
}

// 全局滚动条样式（WebKit 内核浏览器，包括 Chrome、Safari 等）
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

// 滚动条轨道（完全透明）
::-webkit-scrollbar-track {
  background: transparent; // 关键：让轨道透明
  border: none; // 移除可能的边框
  box-shadow: none; // 移除可能的阴影
}

// 滚动条滑块
::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
  border-radius: var(--scrollbar-radius);
  border: none; // 确保无滑块边框

  &:hover {
    background: var(--scrollbar-thumb-hover);
  }
}

// Firefox 滚动条（Firefox 不支持完全透明轨道，但可接近效果）
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) transparent; // 滑块颜色+透明轨道
}

// 微软 Edge 浏览器（基于 Chromium 内核）
@supports (-ms-ime-align: auto) {
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb-color) transparent;
  }
}

// 高 DPI 屏幕适配
@media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 2dppx) {
  ::-webkit-scrollbar {
    width: calc(var(--scrollbar-width) * 0.8);
    height: calc(var(--scrollbar-width) * 0.8);
  }
}
