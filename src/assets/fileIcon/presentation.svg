<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55726)">
<g filter="url(#filter0_d_2040_55726)">
<rect x="1.5" y="4.5" width="21" height="15.75" fill="white"/>
<rect x="1" y="4" width="22" height="16.75" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<rect x="2" y="5" width="20" height="14.75" fill="url(#paint0_linear_2040_55726)" stroke="white"/>
<path d="M7.5 17.55V17.2882L8.8475 16.6336V7.66636L7.5 7.01182V6.75H12.158C13.4667 6.75 14.5148 6.98455 15.3022 7.45364C16.1007 7.92273 16.5 8.66455 16.5 9.67909C16.5 10.2791 16.2893 10.8136 15.8678 11.2827C15.4575 11.7409 14.8974 12.1064 14.1876 12.3791C13.4778 12.6409 12.6682 12.7718 11.7588 12.7718H10.5943V16.6336L12.1248 17.2882V17.55H7.5ZM10.5943 7.65V11.9045H11.8586C12.7348 11.9045 13.4224 11.7082 13.9214 11.3155C14.4205 10.9227 14.6701 10.4209 14.6701 9.81C14.6701 9.04636 14.415 8.49545 13.9048 8.15727C13.4057 7.81909 12.6848 7.65 11.7421 7.65H10.5943Z" fill="#D0692F" fill-opacity="0.75"/>
</g>
<defs>
<filter id="filter0_d_2040_55726" x="-3.5" y="1.5" width="31" height="25.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55726"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55726" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55726" x1="22.5" y1="20.25" x2="22.5" y2="4.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_2040_55726">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
