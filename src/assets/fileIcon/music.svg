<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55743)">
<g filter="url(#filter0_d_2040_55743)">
<path d="M14.9944 1.5H3.75V22.5H20.25V6.75558L14.9944 1.5Z" fill="white"/>
<path d="M15.348 1.14645L15.2015 1H14.9944H3.75H3.25V1.5V22.5V23H3.75H20.25H20.75V22.5V6.75558V6.54848L20.6036 6.40203L15.348 1.14645Z" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<path d="M4.25 22V2H14.7873L19.75 6.96269V22H4.25Z" fill="url(#paint0_linear_2040_55743)" stroke="white"/>
<g filter="url(#filter1_d_2040_55743)">
<path d="M15.0105 1.5H15V6.75H20.25V6.73953L15.0105 1.5Z" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3768 15.7183C13.3821 15.7838 13.3848 15.85 13.3848 15.9167C13.3848 17.4815 11.8995 18.75 10.0674 18.75C8.23524 18.75 6.75 17.4815 6.75 15.9167C6.75 14.3519 8.23524 13.0833 10.0674 13.0833C10.9715 13.0833 11.7912 13.3923 12.3896 13.8933V11.188C12.3896 10.1478 12.3934 8.9753 12.401 7.67061C12.401 7.41496 12.4584 7.20559 12.5732 7.04251C12.688 6.87942 12.8411 6.78465 13.0324 6.75821C13.1931 6.73176 13.3251 6.76923 13.4284 6.8706C13.5317 6.97198 14.7174 8.31902 15.749 8.96812C16.4368 9.40085 17.0862 9.43557 17.6974 9.07227C17.5974 9.52208 16.7331 11.0077 15.2879 10.6667C13.8426 10.3257 13.6178 9.01938 13.526 9.07227C13.4342 9.12516 13.3882 9.22214 13.3882 9.36318V11.1748C13.3882 11.6684 13.3863 12.2018 13.3825 12.7748C13.3787 13.3478 13.3768 13.8899 13.3768 14.4012V15.7183Z" fill="url(#paint1_linear_2040_55743)"/>
</g>
<defs>
<filter id="filter0_d_2040_55743" x="-1.25" y="-1.5" width="26.5" height="31" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55743"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55743" result="shape"/>
</filter>
<filter id="filter1_d_2040_55743" x="13" y="0.5" width="9.25" height="9.25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55743"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55743" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55743" x1="20.25" y1="22.5" x2="20.25" y2="1.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<linearGradient id="paint1_linear_2040_55743" x1="17.5554" y1="18.75" x2="17.5554" y2="7.06133" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7D470"/>
<stop offset="1" stop-color="#FBE29A"/>
</linearGradient>
<clipPath id="clip0_2040_55743">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
