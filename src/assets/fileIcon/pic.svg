<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55740)">
<g filter="url(#filter0_d_2040_55740)">
<rect x="1.5" y="4.5" width="21" height="15.75" fill="white"/>
<rect x="1" y="4" width="22" height="16.75" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<rect x="2" y="5" width="20" height="14.75" fill="url(#paint0_linear_2040_55740)" stroke="white"/>
<path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M21.75 16.5L17.625 12.375L13.875 15L7.875 9L2.25 15V19.5H21.75V16.5Z" fill="#ABC7EE"/>
<circle opacity="0.8" cx="16.875" cy="8.625" r="1.875" fill="#ABC7EE"/>
</g>
<defs>
<filter id="filter0_d_2040_55740" x="-3.5" y="1.5" width="31" height="25.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55740"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55740" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55740" x1="22.5" y1="20.25" x2="22.5" y2="4.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_2040_55740">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
