import type { HandoverStatusEnum, PageType } from '@/model/handover';

import { CommonApi } from './Request';

export const getDesktopFiles = async (id: number) => {
  const response = await CommonApi({ url: `/handover/user/${id}` });
  return response.data;
};

/**
 * 获取文件夹子文件
 * @param guid
 * @returns
 */
export const getHandoverChildrenFiles = async (props: {
  guid: string;
  token: string;
  leftUser: number;
  pageType: PageType;
  currentFolderStatus: HandoverStatusEnum;
}) => {
  const { guid, token, leftUser, pageType, currentFolderStatus } = props;
  const response = await CommonApi.get(
    `/handover/files/${guid}?check=true&leftUser=${leftUser}&pageType=${pageType}&currentFolderStatus=${currentFolderStatus}`,
    {
      headers: {
        'X-Shimo-Handover-Token': token,
        'Cache-Control': 'no-cache',
      },
    },
  );
  return response.data;
};

/**
 * 获取文件夹子文件
 * @param guid
 * @returns
 */
export const getChildrenFiles = async (props: { guid: string; leftUser: number; pageType: PageType }) => {
  const { guid, leftUser, pageType } = props;
  const response = await CommonApi.get(`/handover/files/${guid}?leftUser=${leftUser}&pageType=${pageType}`);
  return response.data;
};

/**
 * 生成文件快照
 */
export const createSnapshot = async (guids: string[], uid: number) => {
  const response = await CommonApi.post(
    `/handover/tokens`,
    {
      guids,
      left_user: uid,
      confirmed: false,
    },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
  return response.data;
};

/**
 * 获取历史交接文件
 */
export const getHistoryFiles = async (uid: number) => {
  const response = await CommonApi.get(`/handover/files/history/${uid}`);
  return response.data;
};

/**
 * 获取文件交接状态
 * @param token
 * @returns
 */
export const getFileTransferStatus = async (token: string, adminPage: boolean) => {
  const response = await CommonApi.get(`/handover/files/list?adminPage=${adminPage}`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  return response.data;
};

/**
 * 根据token获取被交接人的信息
 * @param token
 * @returns
 */
export const getTransfereeInfo = async (token: string) => {
  const response = await CommonApi.get(`/users/me`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  return response.data;
};

/**
 * 查询批量交接进度
 */
export const getBatchHandoverProgress = async (token: string) => {
  const response = await CommonApi.get(`/handover/files/progress`, {
    headers: {
      'X-Shimo-Handover-Token': token,
      'Cache-Control': 'no-cache',
    },
  });

  return response.data;
};

/**
 * 获取单个文件权限
 */
export const getFileManagement = async (token: string, guid: string) => {
  const response = await CommonApi.post(
    `/handover/files/${guid}/action/handover_admin`,
    {},
    {
      headers: {
        'X-Shimo-Handover-Token': token,
        'Content-Type': 'application/json',
      },
    },
  );

  return response.data;
};

/**
 * 获取多个文件权限
 */
export const getFilesManagement = async (token: string, guids: string[]) => {
  const response = await CommonApi.post(
    `/handover/files/action/handover_admin`,
    {
      guids,
    },
    {
      headers: {
        'X-Shimo-Handover-Token': token,
        'Content-Type': 'application/json',
      },
    },
  );

  return response.data;
};

/**
 * 结束交接
 */
export const deleteHandover = async (token: string) => {
  const response = await CommonApi.delete(`/handover/tokens/${token}`, {
    headers: {
      'X-Shimo-Handover-Token': token,
    },
  });

  return response.data;
};

/**
 * 预览摘要
 */
export const getPreviewSummary = async (token: string, guid: string) => {
  const response = await CommonApi.get(`/handover/files/${guid}/preview`, {
    headers: {
      'X-Shimo-Handover-Token': token,
    },
  });

  return response.data;
};
