import type { FileType } from '@/constants/fileList.config';

import { CommonApi } from './Request';

export type IParams = {
  status?: 'unread' | 'all';
  action?: 'peek';
  limit?: number;
};

interface User {
  id: number;
  name: string;
  avatar: string;
  email: string;
  team?: {
    id: number;
    name: string;
  };
}

export type RolePermission =
  | 'reader' // 只读
  | 'commentator' // 可评论
  | 'editor'; // 可编辑
export interface RoleApply {
  id: number;
  role: RolePermission;
  comment: string;
  status:
    | '' // 待处理, 前端显示'通过'按钮
    | 'FILE_NOT_FOUND' // 文件不存在
    | 'USER_NOT_ADMIN' // 用户不是管理员
    | 'NO_MODIFY_ROLE_PERMISSION' // 没有添加协作者的权限
    | 'ROLE_APPLY_IS_APPROVED' // 申请已经被通过
    | 'ROLE_APPLY_IS_APPROVED_BY_ANOTHER'; // 申请已经被其他人通过
}
export type OtherProps = {
  invitedRole?: 1 | 2 | 3 | 4;
  name?: string;
};
interface Notification {
  userId: number;
  msg: string;
  msgType: number;
  fileGuid?: string;
  fileName: string;
  invitedRole?: 1 | 2 | 3 | 4;
  user: User;
  roleApply?: RoleApply;
  file: {
    id: number;
    url: string;
    type?: FileType;
    isSpace: boolean;
  };
  createdAt: string;
  link: string;
}
export interface NotificationWrapper {
  id: string;
  isRead: boolean;
  createdAt: string;
  notification: Notification;
  roleApply: RoleApply;
  [x: string]: unknown;
}

/** 消息通知列表 */
export async function getNotifications(params: IParams) {
  return CommonApi.get<NotificationWrapper[]>('/notifications', { params });
}

/** 全部标记为已读 */
export function readAll() {
  return CommonApi.post('/notifications/action/read_all');
}

/** 单个标记为已读 */
export function readAlon(id: string) {
  return CommonApi.patch(`/notifications/${id}`, {
    status: 'read',
  });
}

/** 未查看消息计数（某个用户在某个时间点以后未查看的消息计数） */
export async function getUnpeekedCount() {
  return CommonApi.get('/notifications/unpeeked_count');
}
