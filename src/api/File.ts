import type { AxiosResponse } from 'axios';

import type { CloudImportExt } from '@/constants/fileTypes';
import { importExtToShimoTypeMap } from '@/constants/fileTypes';
import type { FileDetail, ImportFileApiResponse } from '@/types/api';

import type { SearchParmas, TemplateData } from './File.type';
import type { RolePermission } from './Message';
import { CommonApi } from './Request';

/**
 * 收藏
 * @param fileGuid - 文件 id
 */
export function star(fileGuid: string) {
  return CommonApi.put(`/files/${fileGuid}/star`);
}

/**
 * 取消收藏
 * @param fileGuid - 文件 id
 */
export function cancelStar(fileGuid: string) {
  return CommonApi.delete(`/files/${fileGuid}/star`);
}

/**
 * 查看文件详情
 * @param fileGuid - 文件 id
 */
export function fileDetail(fileGuid: string | undefined) {
  return CommonApi.get(`/files/${fileGuid}`);
}

/**
 * 重命名文件
 * @param fileGuid - 文件 id
 * @param name - 文件标题
 */
export function rename(fileGuid: string | undefined, name: string) {
  return CommonApi.patch(`/files/${fileGuid}`, { name });
}

interface FilesQuery {
  children?: boolean; // 查看子文件 桌面不可用
  excerpt?: boolean;
  fileType?: string; // shared 查询用到
  folder?: string; // 文件夹 guid findChildren 查询用到
  limit?: number; // 限制数量
  lastId?: string;
  lastTimestamp?: number; // 最后一个文件的时间戳
  tagId?: string; // 标签查询用到
  tagType?: string;
  page?: number; // 页码
  pageSize?: number; // 每页数量
  /** used: 最近文件 shared: 共享给我 null: 我的桌面  */
  type?: 'used' | 'shared' | 'created';
  orderBy?: 'updatedAt' | 'createdAt';
  /**  open:最近打开 edit:最近编辑 */
  lastAction?: 'open' | 'edit';
}

/**
 * 文件列表 query
 * @param query - 查询参数
 */
export function files(query: FilesQuery) {
  return CommonApi.get('/files', { params: query });
}

/**
 * 团队空间
 * @param query - 查询参数
 */
export function getSpaces(query: FilesQuery) {
  return CommonApi.get('/spaces', { params: query });
}
/**
 * 获取文档祖先
 * @param fileGuid - 文件 id
 */
export function getAncestors(fileGuid: string | undefined) {
  return CommonApi.get(`/files/${fileGuid}/ancestors`);
}

/**
 *  文件导航
 * @param fileGuid - 文件 id
 */
export function getNavigation(fileGuid: string | undefined) {
  return CommonApi.get(`/files/${fileGuid}/navigation`);
}

/**
 * 上传文件第一步
 * @param formData - 表单数据
 * @param headers - 请求头
 */
export function uploadPostPolicy(
  formData: FormData,
  headers: Record<string, string>,
  controller: AbortController | null,
) {
  return CommonApi.post(`/files/upload/postPolicy`, formData, { headers, signal: controller?.signal });
}

/**
 * 上传文件第二步
 * @param url - 上传地址
 * @param formData - 表单数据
 */
export function uploadMinio(
  url: string,
  formData: FormData,
  controller: AbortController | null,
  onProgress: (progress: number) => void,
) {
  return CommonApi.post(url, formData, {
    signal: controller?.signal,
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(percentCompleted);
    },
  });
}

/**
 * 上传文件第三步
 * @param data - 数据对象
 * @param data.bucket - 存储桶名称
 * @param data.fname - 文件名
 * @param data.size - 文件大小
 * @param data.key - 文件键值
 * @param data.token - 认证令牌
 */
export function uploadCallback(
  data: { bucket: string; fname: string; key: string; size: number; token: string },
  controller: AbortController | null,
) {
  return CommonApi.post(`/files/upload/postPolicy/callback/s3`, data, { signal: controller?.signal });
}

/**
 * 上传文件夹
 * @param data.name - 文件夹名称
 * @param data.folder - 上级文件夹ID
 * @param data.type - folder
 */
export function createFolder(data: { folder: string; name: string; type: string }, controller: AbortController | null) {
  return CommonApi.post(`/files`, data, { signal: controller?.signal });
}

export function uploadFolderFileList(data: { parentId: string; name: string }, controller: AbortController | null) {
  return CommonApi.post(`/files/upload`, data, { signal: controller?.signal });
}

/**
 * 获取空间内存
 */
export function getFileQuota() {
  return CommonApi.get(`/quota`);
}

/**
 * 用户打开文件动作
 * @param fileGuid - 文件 guid
 */
export function userAction(
  fileGuid: string,
  data?: {
    /** 是否预览 */
    isPreview?: 0 | 1;
    /** 是否打开 */
    trackOpen?: 0 | 1;
  },
) {
  return CommonApi.post(`/files/${fileGuid}/user_action`, data);
}

/**
 * 导出表格tale文件
 * @param fileGuid - 文件 guid
 */
export function exportTable(fileGuid: string) {
  return CommonApi.post(`/files/export/table/${fileGuid}`);
}

interface Exportdata {
  type: string; // 'pdf' | 'docx' | 'jpg' | 'md' | 'xlsx' | 'wps' | 'pptx' | 'jpeg' | 'xmind';
  width?: number; // 导出图片的宽度 Default: 884
}

/**
 * 导出文件
 * @param fileGuid - 文件 guid
 * @param data - 导出数据
 */
export function exportFile(fileGuid: string, data: Exportdata) {
  return CommonApi.post(`/files/export/${fileGuid}`, data);
}

/**
 * 获取导出进度
 * @param data - 导出数据
 */
export function exportProgress(data: { taskId: string }) {
  return CommonApi.post(`/files/export/progress`, data);
}

/**
 * 删除文件
 * @param fileGuid - 文件 guid
 */
export function deleteFile(fileGuid: string) {
  return CommonApi.delete(`/files/${fileGuid}`);
}

/** 获取回收站文件列表 */
export function getTrashList() {
  return CommonApi.get('/trashes');
}

/** 彻底删除回收站文件 */
export function deleteTrash(guid: string) {
  return CommonApi.delete(`/trashes/${guid}`);
}

/** 清空回收站文件列表 */
export function deleteTrashes() {
  return CommonApi.delete('/trashes');
}

/** 恢复回收站文件 */
export function recoveryFile(guid: string) {
  return CommonApi.patch(`/trashes/${guid}`);
}

/** 删除最近文件(清空此条记录) */
export function deleteRecentFile(guid?: string) {
  return CommonApi.delete(`/files/recents/${guid}`);
}

/** 批量删除文件 */
export function deleteBulkFile(data: { fileGuids: string[] }) {
  return CommonApi.post('/files/delete_batch', data);
}

/** 获取我的收藏列表 */
export function getStarredFileList(params?: { orderBy: 'updatedAt' | 'createdAt' }) {
  return CommonApi.get('/files/starred', { params });
}

/** 文档收藏 */
export function putStar(guid: string) {
  return CommonApi.put(`/files/${guid}/star`);
}
/**
 * 创建快捷方式
 * @param data - 数据对象
 * @param data.folder - 目标位置的guid或者桌面和团队空间
 * @param data.source_guid - 源文件的guid
 */
export function createdShortcut(data: { folder: string; sourceGuid: string }) {
  return CommonApi.post(`/files/shortcuts`, data);
}
export function getDownloadUrl(guid: string) {
  return new Promise<void>((resolve, reject) => {
    CommonApi.get(`/files/${guid}/download`)
      .then((res) => {
        if (res.status === 200) {
          const {
            request: { responseURL },
          } = res;
          resolve();
          window.open(responseURL);
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
}

/** 搜索文件 */
export function searchFile(data: SearchParmas) {
  return CommonApi.post('/search/files', data);
}

// 导入
export const importFile = (fileGuid: string, type: CloudImportExt): Promise<AxiosResponse<ImportFileApiResponse>> => {
  return CommonApi.post(`/files/import/${fileGuid}`, {
    type: importExtToShimoTypeMap[type],
  });
};

// 导入进度
export const importFileProgress = (taskId: string) => {
  return CommonApi.post('/files/import/progress', {
    taskId: taskId,
  });
};

// 查文件所在目录

export interface GetFileDirReturnType {
  list: FileDetail[];
  nextURL: Record<string, any>;
}

/**
 * 验证加密文件密码
 * @param password 输入的密码
 */
export function verifyFilePassword(fileGuid: string, password: string) {
  return CommonApi.post(`/files/${fileGuid}/password/verify`, { password });
}

export function getFileDir<T = GetFileDirReturnType>(fileParentGuid: string): Promise<AxiosResponse<T>> {
  return CommonApi.get(`/files`, {
    params: {
      folder: fileParentGuid,
    },
  });
}

interface DuplicateData {
  files: {
    guid: string; // 源文件 guid
    name: string; // 源文件名
  }[];
  folder: string; // 目标文件夹 guid
}

/**
 * 创建副本
 * @param data - 创建副本参数
 * @returns 创建副本的请求
 */
export function duplicate(data: DuplicateData) {
  return CommonApi.post(`/files/action/duplicate`, data);
}

// 批量创建副本
export function duplicateBatch(data: DuplicateData) {
  return CommonApi.post(`/files/batch_copy`, data);
}

interface MoveFileEntry {
  /** 源文件夹 guid，如果在文件详情里面，则不传，其他情况需要传 */
  from?: string;
  /** 目标文件夹 guid */
  to: string;
  /** 文件 guid */
  fileGuid: string;
}

interface MoveFileData {
  entries: MoveFileEntry[];
}

/**
 * 移动文件
 * @param data - 移动文件参数
 */
export function move(data: MoveFileData) {
  return CommonApi.post(`/files/move_batch`, data);
}

/**
 * 获取最近位置
 */
export function recentLocation() {
  return CommonApi.get(`/files/last_locations`);
}

interface ApplyAdminRoleData {
  /** 权限 */
  role: RolePermission;
  /** 备注 */
  comment: string;
  /** 文件 guid */
  fileGuid: string;
}

/**
 * 申请文件权限
 * @param data
 */
export function roleApply(data: ApplyAdminRoleData) {
  return CommonApi.post(`/role_applies`, data);
}

/**
 * 查询文件权限申请
 * @param params
 */
export function queryRoleApply(params: { fileGUID: string }) {
  return CommonApi.get(`/role_applies`, { params });
}

/**
 * 创建批量文件下载任务 http://drive.pages.shimo.run/api/v1/batch_downloads
 * @param data
 */
export function createPackageDownloadFile(data: { guids: string[] }, controller: AbortController | null) {
  return CommonApi.post(`/batch_downloads`, data, { signal: controller?.signal });
}

/**
 * 获取批量文件下载任务
 * @param url - 地址
 */
export function getPackageDownloadFile(url: string, controller: AbortController | null) {
  return CommonApi.get(url, {
    signal: controller?.signal,
  });
}

/**
 * 查询快速访问列表
 */
export function queryQuickAccess() {
  return CommonApi.get(`/desktop_shortcuts`);
}

/**
 * 添加文件到快速访问
 * @param fileGuid - 文件 guid
 */
export function addQuickAccess(fileGuid: string) {
  return CommonApi.post(`/files/${fileGuid}/desktop_shortcuts`);
}

/**
 * 从快速访问移除文件
 * @param fileGuid - 文件 guid
 */
export function removeQuickAccess(fileGuid: string) {
  return CommonApi.delete(`/files/${fileGuid}/desktop_shortcuts/verbose`);
}

/**
 * 修改快速方式排序
 * @param fileGuid - 文件 guid
 * @param data - 修改快速方式排序参数
 */
interface ModifyRankData {
  before?: string; // 待放置文件 guid, 如果排在最后，则传空对象 {}
}
export function modifyQuickAccessRank(fileGuid: string, data: ModifyRankData) {
  return CommonApi.patch(`/files/${fileGuid}/desktop_shortcuts/verbose`, data);
}

/** 保存模板 */
export function saveTemplates(data: TemplateData) {
  return CommonApi.post('/files/templates', data);
}

/** 订阅更新【查询】 */
export function getUpdateSubscription(guid: string) {
  return CommonApi.get(`/files/${guid}/update_subscription`);
}

/** 订阅更新【订阅】 */
export function updateSubscription(guid: string) {
  return CommonApi.put(`/files/${guid}/update_subscription`);
}

/** 订阅更新【取消】 */
export function deleteUpdateSubscription(guid: string) {
  return CommonApi.delete(`/files/${guid}/update_subscription`);
}

/**
 *
 * @param guids
 * @returns 获取列表选择总数
 */
export const filesNumUrl = '/batch_downloads/files_num';
