import { CommonApi } from './Request';

export interface LimitType {
  id: number;
  label: string;
  count: number;
}

export enum NotifierType {
  AdminAndCreator = 1,
  Creator = 2,
  Admin = 3,
}

export interface AlertInfoType {
  limit: LimitType[];
  notifier: NotifierType;
}

export interface LimitApiType {
  id: number;
  name: string;
  threshold: number;
}

const LIZARD_API = '';

export async function getAlertSwitchRequest() {
  const response = await CommonApi.get(`${LIZARD_API}/auditlog/alert/mode`);
  return response.data;
}

export async function updateAlertSwitchRequest(enabled: boolean) {
  const response = await CommonApi.patch(`${LIZARD_API}/auditlog/alert/mode`, { enabled });
  if (response.status !== 200 && response.status !== 204) {
    throw new Error(JSON.stringify(response.data));
  }
}

export async function getAlertLimitRequest() {
  const response = await CommonApi.get(`${LIZARD_API}/auditlog/alert/thresholds`);
  return response.data;
}

export async function getAlertNotifierRequest() {
  const response = await CommonApi.get(`${LIZARD_API}/auditlog/alert/notifiers`);
  return response.data;
}

export async function updateLimitRequest(params: Omit<LimitApiType, 'name'>[]) {
  const response = await CommonApi.patch(`${LIZARD_API}/auditlog/alert/thresholds`, params);
  if (response.status !== 200 && response.status !== 204) {
    return response.data;
  }
}
export async function updateNotifierRequest(params: number) {
  const response = await CommonApi.patch(`${LIZARD_API}/auditlog/alert/notifiers`, { option: params });
  if (response.status !== 200 && response.status !== 204) {
    return response.data;
  }
}
