import type { SettingConfigsApiType } from '../pages/pc/Enterprise/CollaborationWatermarkSettings/service/type';
import { CommonApi } from './Request';

export interface TimeZoneTypeResponse {
  locationI18n: string;
  location: string;
  offset: number;
  zone: string;
}

export interface TimeFormatTypeResponse {
  id: number;
  format: string;
}

export interface TimeSettingConfigsResponse {
  timezones: TimeZoneTypeResponse[];
  formats: TimeFormatTypeResponse[];
}

export interface TimeSettingConfigsApiResponse {
  data: TimeSettingConfigsResponse;
}

/**
 * 获取水印配置
 */
export const getWatermarkConfigsRequest = async (teamId: number) =>
  CommonApi.get<SettingConfigsApiType>(`/teams/${teamId}/watermark/settings`);

/**
 * 获取水印时间配置
 */
export const getWatermarkTimeConfigRequest = async () => CommonApi.get<TimeSettingConfigsResponse>('/time/settings');

/**
 * 更新水印配置
 */
export const updateWatermarkConfigsRequest = async (teamId: number, params: Partial<SettingConfigsApiType>) =>
  CommonApi.post(`/teams/${teamId}/watermark/settings`, params);

/**
 * 获取下载水印
 */
export const getDownloadWatermarkRequest = async (
  userId: number,
  guid: string,
  exportType: 'jpg' | 'pdf' | 'pure_pdf',
) => CommonApi.get(`/user/users/${userId}/downloadableWatermark`, { params: { exportType, guid } });
