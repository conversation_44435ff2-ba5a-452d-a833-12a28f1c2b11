import { CommonApi } from '@/api/Request';

import type { SyncThirdPartyResponse, TeamInfoResponse } from '../contexts/permissions/type';
import type { CurrentPermissions, UserFeatures } from './MemberPermissions.type';

// 后端暂无接口，直接返回true
export const getAlphaFeature = async () => {
  return true;
  // const response = await CommonApi.get(`${baseURL}/alpha_features/${key}`);
  // return (await response.data) as boolean;
};

export const getUserFeatures = async () => {
  return CommonApi.get<UserFeatures>(`/user/features`);
};

export const getCurrentPermissions = async () => {
  return CommonApi.get<CurrentPermissions>(`/users/me/permissions`);
};

// 后端已去掉该接口
export const getLicenseLimitType = async () => {
  const response = await CommonApi.get(`/user/kits/license/limitType`);
  if (response.status === 200) {
    return (await response.data) as {
      data: {
        limitType: 1 | 0;
      };
      domain: string;
      requestId: string;
    };
  } else {
    return undefined;
  }
};

export const isDingtalkOrWeWork: (id: number) => Promise<{
  isDingtalk: boolean;
  isSSOSAML: boolean;
  isWework: boolean;
}> = async (id: number) => {
  const response = await CommonApi.get(`/teams/${id}/is_dingtalk_or_wework`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

export const getTeamInfo: () => Promise<TeamInfoResponse> = async () => {
  const response = await CommonApi.get(`/teams/mine`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 获取第三方同步状态
 */
export const getStateSyncThirdParty: (id: number) => Promise<SyncThirdPartyResponse> = async (id: number) => {
  const response = await CommonApi.get(`/thirdparty/teams/${id}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};
