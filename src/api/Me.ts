import { fetchGet } from '@/utils/request';

import { baseURL } from '../contexts/activeRange/service/constant';
import { CommonApi } from './Request';

export async function getMe() {
  return CommonApi.get('/users/me');
}

export async function getAnonymousId(): Promise<number> {
  const response = await CommonApi.get<{ anonymousUser: number }>('/auth/anonymous_id');
  return response.data.anonymousUser;
}

export async function login(username: string, password: string) {
  return await CommonApi.postForm('/auth/password/login', {
    email: username,
    password,
  });
}
export async function loginByLdap(username: string, password: string) {
  return await CommonApi.post('/thirdparty/ldap/login', {
    username,
    password,
  });
}
/** 用户和企业容量 */
export function quota() {
  return CommonApi.get('/quota');
}

//UserCheckPoint 用户卡点 http://drive.pages.shimo.run/api/v1/users/checkpoint
export async function userCheckPoint() {
  const response = await CommonApi.get('/users/checkpoint');
  return response.data;
}

//根据用户id获取用户信息
export const getUserInfo = async (userId: number | string) => {
  const response = await CommonApi.get(`/users/${userId}/info`);
  return response.data;
};

export const getSdkUserInfo = async (userId: number | string) => {
  const response = await fetchGet({
    url: `${baseURL}/user/users/${userId}/info`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

export const logout = async () => {
  return await CommonApi.get('/auth/logout');
};
// 从旧项目迁移的 getMe 函数，使用统一的 CommonApi
export const getMeWithCache = async () => {
  const response = await CommonApi.get('/users/me', {
    headers: {
      'Cache-Control': 'no-cache',
    },
  });
  return response.data;
};
