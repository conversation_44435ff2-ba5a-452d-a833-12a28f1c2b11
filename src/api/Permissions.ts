import type { PermissionState, SyncThirdPartyResponse, TeamInfoResponse } from '../contexts/permissions/type';
import { CommonApi } from './Request';

/**
 * 获取用户权限信息
 */
export const getPermissions = async () => {
  const response = await CommonApi.get<PermissionState>('/user/permissions');
  return response.data;
};

/**
 * 获取团队信息
 */
export const getTeamInfo = async () => {
  const response = await CommonApi.get<TeamInfoResponse>('/user/team/info');
  return response.data;
};

/**
 * 获取第三方同步状态
 */
export const getSyncThirdPartyStatus = async () => {
  const response = await CommonApi.get<SyncThirdPartyResponse>('/user/sync/third-party');
  return response.data;
};
