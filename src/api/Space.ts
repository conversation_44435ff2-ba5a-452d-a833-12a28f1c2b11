import type { AnnouncementsResponse, TimelinesResponse } from '@/model/Space';

import { CommonApi } from './Request';

interface QueryParams {
  orderBy?: string;
}

interface PutAnnouncementsQuery {
  guid?: string;
  announcement?: string;
}

// 获取团队空间列表
export function getSpaceList(query: QueryParams) {
  return CommonApi.get('/spaces', { params: query });
}

//创建团队空间
export function addSpace(name: string) {
  return CommonApi.post('/spaces', { name });
}
//删除团队空间
export function deleteSpace(guid?: string) {
  return CommonApi.delete(`/spaces/${guid}`);
}

//团队空间名称的修改
export function editSpaceName(guid?: string, params?: { name: string }) {
  return CommonApi.patch(`/spaces/${guid}`, params);
}

//获取单个空间权限数据
export function getSpacePermission(guid?: string) {
  return CommonApi.get(`/files/${guid}`);
}

//置顶团队空间
export function setSpacePin(guid?: string) {
  return CommonApi.post(`/spaces/${guid}/action/pin`);
}

//取消置顶团队空间
export function setSpaceUnPin(guid?: string) {
  return CommonApi.post(`/spaces/${guid}/action/unpin`);
}

//获取置顶团队空间列表
export function getSpacePinList() {
  return CommonApi.get(`/pinned_spaces`);
}

//置顶团队空间拖拽排序
export function moveSpacePin(params?: { guid?: string; targetId?: string }) {
  return CommonApi.post(`/spaces/${params?.guid}/action/move`, { afterGuid: params?.targetId });
}

/** 团队空间公告显示 */
export async function getAnnouncements(guid?: string): Promise<AnnouncementsResponse[]> {
  const response = await CommonApi.get(`/spaces/${guid}/announcements`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}

/** 团队空间公告发布 */
export async function putAnnouncements({ guid, announcement }: PutAnnouncementsQuery): Promise<AnnouncementsResponse> {
  const response = await CommonApi.put(`/spaces/${guid}/announcements`, { announcement });
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}

/** 团队空间公告阅读 */
export function announcementsRead({ guid, announcementId }: { guid?: string; announcementId?: string }) {
  return CommonApi.get(`/spaces/${guid}/announcements/${announcementId}/action/read`);
}

/** 团队空间动态列表 */
export async function getTimelines(guid?: string): Promise<TimelinesResponse> {
  const response = await CommonApi.get(`/spaces/timelines?filterSpaceGUIDs=${guid}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}

/** 团队空间动态更多 */
export function timelinesMore({ id }: { id: number }) {
  return CommonApi.get(`/spaces/timelines/${id}`);
}
