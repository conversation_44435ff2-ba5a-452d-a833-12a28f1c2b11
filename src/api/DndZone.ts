import { CommonApi } from '@/api/Request';

import type { RulesResponse } from '../pages/pc/Enterprise/DndZoneManagement/service/response';
import type { Rule } from '../pages/pc/Enterprise/DndZoneManagement/service/type';

export async function getRulesRequest(): Promise<Rule[] | null> {
  const response = await CommonApi.get(`/org/bother_rules`);
  if (response.status === 200 || response.status === 204) {
    const { rules } = response.data as RulesResponse;
    return rules;
  } else {
    return Promise.reject(await response.data);
  }
}

export async function updateRuleRequest(rule: Rule): Promise<void> {
  const response = await CommonApi.put(`/org/bother_rules/${rule.id}`, rule);
  if (response.status === 200 || response.status === 204) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}

export async function createRuleRequest(rule: Partial<Pick<Rule, 'id'>> & Omit<Rule, 'id'>): Promise<void> {
  const response = await CommonApi.post(`/org/bother_rules`, rule);
  if (response.status === 200 || response.status === 204) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}

export async function deleteRuleRequest(id: number): Promise<void> {
  const response = await CommonApi.delete(`/org/bother_rules/${id}`);
  if (response.status === 200 || response.status === 204) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
}
