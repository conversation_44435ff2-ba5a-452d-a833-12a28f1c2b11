import type { TemplateDataItem } from '@/model/Template';

import { CommonApi } from './Request';

interface TemplateTypeParams {
  type: number | undefined;
  page: number;
  pageSize: number;
}
export async function loadTemplateTypeData(params: TemplateTypeParams) {
  return await CommonApi.get('/publicTemplates', { params });
}

export async function createTemplate(data: TemplateDataItem) {
  return await CommonApi.post('/publicTemplates', data);
}

export async function updateTemplate(data: TemplateDataItem) {
  return await CommonApi.patch(`/publicTemplates/${data.id}`, data);
}

export async function deleteTemplate(id: string | number) {
  return await CommonApi.delete(`/publicTemplates/${id}`);
}

export async function createByTemplate(data: { originGuid: string; type: string }) {
  return await CommonApi.get(`files/create/${data.type}`, { params: { templateGuid: data.originGuid } });
}

export async function updateByTemplate(data: Partial<TemplateDataItem>) {
  return await CommonApi.patch(`/templates/${data.guid}`, data);
}

export async function deleteByTemplate(data: Partial<TemplateDataItem>) {
  return await CommonApi.delete(`/templates/${data.guid}`);
}
