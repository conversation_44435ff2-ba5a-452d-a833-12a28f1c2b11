import type { CollaborationData, InviteData, InviteInfo } from '@/components/Collaboration/types';

import type {
  CollaboratorsData,
  CollaboratorsProps,
  DepartmentsData,
  DepartmentUsersInfo,
  InvitePatchData,
  RecentContactData,
  SearchResponse,
} from './Collaboration.type';
import { CommonApi } from './Request';

// 获取分享协作 空间 文件夹 及文件详情
export function getCollaborationDetail(guid: string) {
  return CommonApi.get<CollaborationData>(`/files/${guid}`);
}

// 获取协作者列表（协作者和管理者 所以人员）
export function getCollaborationList(guid: string, query: CollaboratorsProps) {
  return CommonApi.get<CollaboratorsData>(`/files/${guid}/collaborators`, { params: query });
}
interface updateItem {
  role: string;
  needNotice?: boolean;
  userId?: number;
}
//修改协作者的权限---添加协作者权限
export function updateCollaboration(guid: string, id: number, data: updateItem) {
  return CommonApi.patch(`/files/${guid}/collaborators/${id}`, data);
}
//修改协作者的权限---添加协作者权限
export function addCollaboration(guid: string, data: updateItem) {
  return CommonApi.post(`/files/${guid}/collaborators/`, data);
}

//修改协作者中部门权限 或者添加
export function updateCollaborationDepartment(guid: string, id: number, data: updateItem) {
  return CommonApi.patch(`/files/${guid}/dep_collaborators/${id}`, data);
}

//移除协作者人员 权限
export function deleteCollaboration(guid: string, id: number) {
  return CommonApi.delete(`/files/${guid}/collaborators/${id}`);
}
//移除协作者中 部门权限
export function deleteCollaborationDepartment(guid: string, id: number) {
  return CommonApi.delete(`/files/${guid}/dep_collaborators/${id}`);
}

//修改上级目录协作者权限
export function updateParentCollaboration(guid: string, data: { parentRole: string }) {
  return CommonApi.patch(`/files/${guid}`, data);
}

//关闭或者开启 分享（api/v1/files/{fileGuid}/share）
export function updateShareStatus(guid: string, data: { shareMode: string }) {
  return CommonApi.patch(`/files/${guid}/share`, data);
}

//分享密码 的关闭开（api/v1/files/{fileGuid}/share）
export function SharePasswordStatus(guid: string, data: { passwordProtected: boolean; reset: boolean }) {
  return CommonApi.patch(`/files/${guid}/password`, data);
}

//修改文件过期时间
export function updateExpireTime(guid: string, data: { shareModeExpireDuration: number }) {
  return CommonApi.patch(`/files/${guid}/expire`, data);
}

//查询最近联系人
export function getRecentContact(keyword?: string) {
  return CommonApi.post<RecentContactData>(`/search/recent_contacts`, { keyword });
}

//获取组织架构
export function getOrgDepartment(id: number) {
  return CommonApi.get<DepartmentsData>(`/org/departments/${id}/shadow_subtree`);
}

//获取组织架构成员列表
export function getOrgDepartmentUser(id: number, query: { page: number; perPage: number }) {
  return CommonApi.get<DepartmentUsersInfo>(`/org/departments/${id}/users`, { params: query });
}

//搜索人员
interface SearchQueryParams {
  limit: number;
  keyword?: string;
  filter: {
    user: {
      includeRecentContact: boolean;
      includeTeamMember: boolean;
    };
    department: {
      id?: number;
    };
    group: {
      id?: number;
    };
  };
  fetchFileRoleByFileGuid?: string;
}
export function getSearchUser(query: SearchQueryParams) {
  return CommonApi.post<SearchResponse>(`/search`, query);
}

//设置为管理者
export function setAdmin(guid: string, id: number, data: { needNotice?: boolean }) {
  return CommonApi.put(`/files/${guid}/admins/${id}`, data);
}

//删除管理者
export function deleteAdmin(guid: string, id: number) {
  return CommonApi.delete(`/files/${guid}/admins/${id}`);
}

//设置部门为管理者
export function setDepAdmin(guid: string, uid: number, data: { needNotice?: boolean }) {
  return CommonApi.put(`/files/${guid}/dep_admins/${uid}`, data);
}

//删除部门管理者
export function deleteDepAdmin(guid: string, id: number) {
  return CommonApi.delete(`/files/${guid}/dep_admins/${id}`);
}

type GetAdminListResponseData = {
  role_type: number;
  user: { teamName: string };
  department: { name: string; id: number };
  group: { [x: string]: unknown };
  team: { [x: string]: unknown };
}[];
export const getAdminList = (fileGuid: string) =>
  CommonApi.get<GetAdminListResponseData>(`/role_applies/admins`, {
    params: {
      fileGUID: fileGuid,
    },
  });

export const getInviteCode = async (guid: string) => {
  return CommonApi.get<InviteInfo | null>(`/files/${guid}/invite_code`);
};

export const deleteInviteCode = async (guid: string) => {
  return CommonApi.delete(`/files/${guid}/invite_code`);
};

export const createInviteCode = async (guid: string, data: InvitePatchData) => {
  return CommonApi.post(`/files/${guid}/invite_code`, data);
};

export const updateInviteCode = async (guid: string, data: InvitePatchData) => {
  return CommonApi.patch(`/files/${guid}/invite_code`, data);
};

export const getInviteInfoByCode = async (code: string) => {
  return CommonApi.get<InviteData>(`/files/invite_code/${code}`);
};

export const comfirmInvite = async (code: string) => {
  return CommonApi.post(`/files/invite_code/collaborator`, { code });
};
