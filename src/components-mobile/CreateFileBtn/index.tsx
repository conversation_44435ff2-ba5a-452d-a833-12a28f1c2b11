import { Button, Popup } from 'antd-mobile';
import { useMemo, useState } from 'react';

import addFileBtn from '@/assets/images/svg/addFileBtn.svg';
import addFileBtn_disabled from '@/assets/images/svg/addFileBtn-disabled.svg';
import type { FileItem } from '@/constants/fileList.config';
import { FILE_LIST_H5 } from '@/constants/fileList.config';
import { fm } from '@/modules/Locale';
import { useUserCheckPoint } from '@/service/Me';
import { useUploadStore } from '@/store/Upload';

import FileMenuList from './components/FileMenuList';
import { UploadIcon } from './components/UploadIcon';
import { useDisabledCreateFileBtn } from './hooks/useDisabledCreateFileBtn';
import { useShowCreateFileBtn } from './hooks/useShowCreateFileBtn';
import styles from './index.less';

export default () => {
  const shouldShow = useShowCreateFileBtn();
  const isDisabled = useDisabledCreateFileBtn();

  const i18n_cancel = fm('SiderMenu.cancel');

  const { isShowUploadBoard } = useUploadStore((state) => state);
  const { isNoFeaturePoint } = useUserCheckPoint();

  const [visible, setVisible] = useState(false);

  const fileMenuList = useMemo(() => {
    return FILE_LIST_H5.map((item: FileItem) => {
      item.hidden = item.supportType && isNoFeaturePoint(item.supportType || '');
      return item;
    });
  }, [isNoFeaturePoint]);

  if (!shouldShow) return null;

  return (
    <div className={styles.container}>
      <img
        className={styles.fixedButton}
        src={isDisabled ? addFileBtn_disabled : addFileBtn}
        onClick={() => {
          if (!isDisabled) {
            setVisible(true);
          }
        }}
      />

      <Popup
        className={styles.popup}
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <FileMenuList config={fileMenuList} setVisible={setVisible} />
        <Button block className={styles.cancelBtn} onClick={() => setVisible(false)}>
          {i18n_cancel}
        </Button>
      </Popup>
      {isShowUploadBoard && <UploadIcon />}
    </div>
  );
};
