@radius-lg: 8px;

.fixedButton {
  @size: 48px;

  width: @size;
  height: @size;
  border-radius: 50%;
  box-shadow: 0 4px 16px 0 var(--theme-box-shadow-color-2);
}

.popup {
  :global {
    .adm-popup-body {
      border-radius: @radius-lg;
      background-color: var(--theme-basic-color-bg-default);
    }
  }
}

.cancelBtn {
  border: none;
  border-top: 6px solid var(--theme-layout-color-bg-editor);
  box-sizing: content-box;
  background-color: var(--theme-basic-color-bg-default);
}

.container {
  position: fixed;
  z-index: 999;
  bottom: 74px;
  right: 20px;
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  justify-content: center;
}

.uploadButton {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: var(--theme-layout-color-bg-white);
  box-shadow: 0 4px 16px 0 var(--theme-basic-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  position: relative;
}

.uploadCount {
  color: var(--theme-text-color-white);
  background-color: var(--theme-button-color-primary);
  border-radius: 50%;
  width: 12px;
  height: 12px;
  position: absolute;
  font-size: 8px;
  right: 9px;
  top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
