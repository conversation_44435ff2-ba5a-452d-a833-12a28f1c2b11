import type { InputProps } from 'antd-mobile';
import { Form, Input, Modal } from 'antd-mobile';
import classNames from 'classnames';
import { useState } from 'react';
import { createRoot } from 'react-dom/client';

import { fm2 } from '@/modules/Locale';
import { illegalCharRegex, sanitizeInput } from '@/utils-mobile/validate';

import styles from './index.less';

const i18n_cancel = fm2('SiderMenu.cancel');
const i18n_confirm = fm2('SiderMenu.confirm');
const i18n_inputName = fm2('SiderMenu.inputName');
const i18n_pleaseInputFilename = fm2('SiderMenu.pleaseInputFilename');
const i18n_validateIllegalTips = fm2('SiderMenu.validateIllegalTips');

export interface CommonModal {
  open?: boolean;
  title?: string;
  requiredMessage?: string;
  placeholder?: string;
  type?: 'input' | 'default';
  initialValues?: { filename?: string }; //初始值
  onClose?: () => void;
  onConfirm?: (filename: string) => void;
  renderFooter?: () => React.ReactNode;
  renderConfirmContent?: () => React.ReactNode;
  renderContent?: () => React.ReactNode;
  showCancelBtn?: boolean;
  inputProps?: InputProps;
}

const CreateFileModal = ({
  open,
  title,
  type = 'default',
  initialValues,
  onClose = () => {},
  onConfirm = () => {},
  requiredMessage = '',
  placeholder = '',
  inputProps = undefined,
  renderFooter,
  renderConfirmContent,
  renderContent,
  showCancelBtn = true,
}: CommonModal) => {
  const [filename, setFilename] = useState('');
  const [visible, setVisible] = useState(open);
  const [form] = Form.useForm();
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);

  const handleClose = () => {
    setVisible(false);
    onClose();
  };

  const handleConfirm = () => {
    if (!title) return;
    onConfirm(filename.trim() || title);
    setFilename('');
    setVisible(false);
  };

  const modalContent = () => {
    switch (type) {
      case 'input':
        return (
          <div>
            <div className={styles.contentLayout}>
              <div className={styles.modalTitle}>{title}</div>
              <Form
                className={styles.form}
                form={form}
                initialValues={initialValues}
                onFinish={({ filename }) => {
                  onConfirm((filename as string).trim());
                  setVisible(false);
                }}
                onValuesChange={() => {
                  setTimeout(() => {
                    const errors = form.getFieldsError();
                    const hasErrors = errors.some((item) => item.errors.length > 0);
                    const val = form.getFieldValue('filename');
                    setIsSubmitDisabled(hasErrors || !val?.trim());
                  });
                }}
              >
                <Form.Item
                  name="filename"
                  rules={[
                    { required: true, message: requiredMessage || i18n_pleaseInputFilename },
                    {
                      validator: (_, value) => {
                        const cleaned = sanitizeInput(value || '');
                        if (illegalCharRegex.test(cleaned)) {
                          return Promise.reject(new Error(`${i18n_validateIllegalTips}: .\\/:*?"<>|`));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    clearable
                    className={styles.input}
                    placeholder={placeholder || i18n_inputName}
                    onChange={(val) => {
                      form.setFieldValue('filename', sanitizeInput(val));
                    }}
                    {...inputProps}
                  />
                </Form.Item>
              </Form>
            </div>

            <div className={styles.footerBtnLayout}>
              {renderFooter ? (
                renderFooter()
              ) : (
                <>
                  <div className={classNames(styles.btn)} onClick={() => handleClose()}>
                    {i18n_cancel}
                  </div>
                  <div className={styles.divider} />
                  <div
                    className={classNames(styles.btn, styles.btnConfirm, {
                      [styles.disabled]: isSubmitDisabled,
                    })}
                    onClick={() => form.submit()}
                  >
                    {renderConfirmContent ? renderConfirmContent() : i18n_confirm}
                  </div>
                </>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div>
            <div className={styles.contentLayout}>
              <div className={styles.modalTitle}>{title}</div>
              {renderContent?.()}
            </div>

            <div className={styles.footerBtnLayout}>
              {renderFooter ? (
                renderFooter()
              ) : (
                <>
                  {showCancelBtn && (
                    <>
                      <div className={classNames(styles.btn)} onClick={() => handleClose()}>
                        {i18n_cancel}
                      </div>
                      <div className={styles.divider} />
                    </>
                  )}

                  <div className={classNames(styles.btn, styles.btnConfirm)} onClick={() => handleConfirm()}>
                    {renderConfirmContent ? renderConfirmContent() : i18n_confirm}
                  </div>
                </>
              )}
            </div>
          </div>
        );
    }
  };

  return (
    <Modal
      closeOnMaskClick
      className={styles.commonModal}
      content={modalContent()}
      visible={visible}
      onClose={handleClose}
    />
  );
};

CreateFileModal.show = (props: CommonModal) => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);

  const destroy = () => {
    root.unmount();
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  root.render(
    <CreateFileModal
      {...props}
      open={true}
      onClose={() => {
        props.onClose?.();
        destroy();
      }}
      onConfirm={(filename) => {
        props.onConfirm?.(filename);
        destroy();
      }}
    />,
  );
};

export default CreateFileModal;
