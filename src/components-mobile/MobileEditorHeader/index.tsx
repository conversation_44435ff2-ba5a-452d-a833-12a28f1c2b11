import { UserOutlined } from '@ant-design/icons';
import { Avatar, message } from 'antd';
import { Toast } from 'antd-mobile';
import { useContext, useEffect } from 'react';
import { useParams } from 'umi';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import { ReactComponent as HeaderBack20Icon } from '@/assets/images/svg/headerBack20.svg';
import { ReactComponent as HeaderMore20Icon } from '@/assets/images/svg/headerMore20.svg';
import { ReactComponent as HeaderShare20Icon } from '@/assets/images/svg/headerShare20.svg';
import { CollaborationShareMobile } from '@/components-mobile/Collaboration/CollaborationShareMobile';
import { FileSettingContext } from '@/contexts/FileSetting';
import { useCollaborators } from '@/hooks/useCollaborators';
import { useDisclosure } from '@/hooks/useDisclosure';
import type { FileDataModel } from '@/model/FileList';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import type { FileDetail } from '@/types/api';
import { getOpenFilePermissionTip } from '@/utils/file';

import style from './index.less';
interface Props {
  data?: FileDetail;
}
export const MobileEditorHeader = ({ data }: Props) => {
  const { updateActionProp, mainDivRef, setOpenPopup } = useContext(FileSettingContext);
  const { collaborators } = useCollaborators();
  const { guid } = useParams<{ guid: string }>();
  const userMe = useMeStore((state) => state.me);
  // 只有当 userMe.id 存在时才加入数组
  const effectiveUserMe = userMe?.id ? userMe : null;
  const combinedCollaborators = effectiveUserMe ? [effectiveUserMe, ...collaborators] : collaborators;
  const { isOpen, open, close, toggle } = useDisclosure(false);
  const routeListback = ['/desktop', '/recent', '/favorites', '/created', '/share', '/space'];
  const enterUrl = document.referrer;
  const url = enterUrl ? new URL(document.referrer).pathname : '';
  const goback = () => {
    if (routeListback.includes(url)) {
      window.location.href = url;
    } else {
      window.history.back();
    }
  };
  const shareModal = (_e: React.MouseEvent) => {
    _e.stopPropagation();
    if (data?.guid) {
      open();
    }
  };

  const moreList = (_e: React.MouseEvent) => {
    _e.stopPropagation();
    setOpenPopup(true);
    updateActionProp(data as unknown as FileDataModel, 'editor');
  };

  useEffect(() => {
    to(fileApi.userAction(guid || '', { trackOpen: 1 }));
  }, [guid]);

  useEffect(() => {
    const permissionsAndReasons = data?.permissionsAndReasons;
    if (permissionsAndReasons) {
      const tipMessage = getOpenFilePermissionTip(permissionsAndReasons);
      setTimeout(() => {
        message.warning(tipMessage);
      }, 1000);
    }
    const canEdit = data?.permissionsAndReasons?.canEdit?.value;
    const unableType = ['presentation', 'mindmap']; //幻灯片和思维导图不支持移动端修改
    if (canEdit && unableType.includes(data?.type)) {
      setTimeout(() => {
        Toast.show({ content: fm2('Editor.onlyReadAndComment'), duration: 3000 });
      }, 2000);
    }
  }, [data]);
  return (
    <div ref={mainDivRef}>
      <div className={style.moblieEditerHeader}>
        <div className={style.return} onClick={goback}>
          <HeaderBack20Icon />
        </div>
        <div className={style.ravatarBox}>
          {combinedCollaborators.slice(0, 4).map((item) => (
            <div key={item.id} className={style.ravatarItem}>
              <Avatar icon={<UserOutlined />} size={24} src={item?.avatar || undefined} />
            </div>
          ))}
          {combinedCollaborators.length > 4 && (
            <div className={style.ravatarItem}>
              <span className={style.ravatarMore}> +{combinedCollaborators.length - 4}</span>
            </div>
          )}
        </div>
        <div className={style.rightBox}>
          <span className={style.rightIcon} onClick={(e) => shareModal(e)}>
            <HeaderShare20Icon />
          </span>
          <span className={style.rightIcon} onClick={(e) => moreList(e)}>
            <HeaderMore20Icon />
          </span>
        </div>
      </div>
      <CollaborationShareMobile close={close} guid={data?.guid || ''} toggle={toggle} visible={isOpen} />
    </div>
  );
};
