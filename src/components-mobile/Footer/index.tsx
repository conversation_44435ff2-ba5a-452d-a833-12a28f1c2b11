import { isNil } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { history, Link, useLocation, useParams } from 'umi';

import { getSpaces } from '@/api/File';
import { getSpacePinList } from '@/api/Space';
import backDesktopSvg from '@/assets/mobile/footerTab/backDesktop.svg';
import backFileSvg from '@/assets/mobile/footerTab/backFile.svg';
import backSpaceSvg from '@/assets/mobile/footerTab/backSpace.svg';
import { ReactComponent as FileSvg } from '@/assets/mobile/footerTab/file.svg';
import { ReactComponent as HomeSvg } from '@/assets/mobile/footerTab/home.svg';
import { ReactComponent as NoticeSvg } from '@/assets/mobile/footerTab/message.svg';
import { ReactComponent as MySvg } from '@/assets/mobile/footerTab/my.svg';
import { ReactComponent as TabDeskSvg } from '@/assets/mobile/footerTab/tab-desk.svg';
import { ReactComponent as TabSpace } from '@/assets/mobile/footerTab/tab-space.svg';
import type { PopItemProps, TabModel } from '@/model/File';
import { RootType, TabTypeEnum } from '@/model/File';
import type { SpaceEntity } from '@/model/FileList';
import { fm } from '@/modules/Locale';
import { useFileTabStore } from '@/store/FileTab';

import { withFileListHOC } from '../FileList';
import { PopupItem } from '../FileList/List/PopupItem';
import itemStyle from '../FileList/List/PopupItem/index.less';
import { FilePopup } from '../FileList/Popup';
import style from './index.less';
const rowHeight = 45;
export const Footer = () => {
  const { guid } = useParams();
  const { setActiveTab, activeTab, navigators } = useFileTabStore((state) => state);
  const [popActiveItem, setPopActiveItem] = useState('/desktop');
  const [visible, setVisible] = useState(false);
  const location = useLocation();
  const cancel = fm('Common.cancel');
  const returnToFile = fm('Tab.returnToFile');
  const myDesktop = fm('BackToPopover.myDesktop');
  const siderMenuSpaceText = fm('SiderMenu.siderMenuSpaceText');
  const home = fm('Tab.home');
  const file = fm('Tab.file');
  const notice = fm('Tab.notice');
  const my = fm('Tab.my');

  const text = {
    cancel,
    returnToFile,
    myDesktop,
    siderMenuSpaceText,
    home,
    file,
    notice,
    my,
  };
  const [popupList, setPopupList] = useState<TabModel[]>([
    {
      img: backFileSvg,
      title: text.returnToFile,
      path: '/folder',
      type: TabTypeEnum.DESKTOP,
      width: 18,
    },
    {
      img: backDesktopSvg,
      title: text.myDesktop,
      path: '/desktop',
      type: TabTypeEnum.SPACE,
      width: 24,
    },
  ]);
  const otherTabs = useMemo<Record<TabTypeEnum, TabModel>>(
    () => ({
      [TabTypeEnum.DESKTOP]: {
        img: TabDeskSvg,
        title: text.myDesktop,
        path: '/desktop',
        type: TabTypeEnum.DESKTOP,
      },
      [TabTypeEnum.SPACE]: {
        img: TabSpace,
        title: text.siderMenuSpaceText,
        path: '/space',
        type: TabTypeEnum.SPACE,
      },
    }),
    [text.myDesktop, text.siderMenuSpaceText],
  );

  const resetTabList = [
    {
      img: HomeSvg,
      title: text.home,
      path: '/',
    },
    {
      img: FileSvg,
      title: text.file,
      path: '/desktop',
    },
    {
      img: NoticeSvg,
      title: text.notice,
      path: '/notification',
    },
    {
      img: MySvg,
      title: text.my,
      path: '/my',
    },
  ];
  const [tabs, setTabs] = useState<TabModel[]>(resetTabList);
  const changeTabs = useCallback(
    (currentTab: TabModel) => {
      setTabs((tabs) => {
        const arr = [...tabs];
        arr.splice(1, 1, currentTab);
        setActiveTab(currentTab.path);
        return arr;
      });
    },
    [setActiveTab],
  );
  const changeActiveTab = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>, tab: TabModel) => {
      if (!isNil(tab.type)) {
        e.preventDefault();
        setVisible(true);
      } else {
        setActiveTab(tab.path);
      }
    },
    [setActiveTab],
  );
  const EnhancedFileList = withFileListHOC<PopItemProps>((props) => <PopupItem {...props} selectVal={popActiveItem} />);
  const getAllSpacesList = useCallback((): Promise<SpaceEntity[]> => {
    return Promise.all([getSpaces({}), getSpacePinList()])
      .then((res) => {
        const spaceList = res[0]?.data?.spaces || [];
        const spaceTopList = res[1]?.data?.spaces || [];
        return [...spaceTopList, ...spaceList];
      })
      .catch(() => {
        return [];
      });
  }, []);

  const getNavigationList = useCallback(async () => {
    const currentTab =
      navigators.rootType === RootType.Space ? otherTabs[TabTypeEnum.SPACE] : otherTabs[TabTypeEnum.DESKTOP];
    const list = await getAllSpacesList();
    setPopupList((arr) => {
      const tabList = [...arr].splice(0, 2);
      return [
        ...tabList,
        ...list.map((item) => ({
          img: backSpaceSvg,
          title: item.name,
          path: `/folder/${item.guid}`,
        })),
      ];
    });
    if (navigators.ancestors.length) {
      setPopActiveItem(`${popupList[currentTab?.type as number].path}/${guid || ''}`);
    }
    changeTabs(currentTab);
  }, [guid, changeTabs, getAllSpacesList, otherTabs, navigators, setPopActiveItem]);

  useEffect(() => {
    if (guid && navigators.ancestors.length > 0) {
      getNavigationList();
    }
  }, [guid, getNavigationList, location.pathname, navigators]);

  useEffect(() => {
    if (!guid) {
      setTabs(resetTabList);
      setPopupList((arr) => {
        const tabList = [...arr].splice(0, 2);
        return tabList;
      });
      setPopActiveItem(popupList[1].path);
    }
  }, [guid]);

  useEffect(() => {
    if (['/recent', '/favorites', '/created', '/share'].includes(location.pathname)) {
      setActiveTab('/');
    } else if (['/space', '/desktop'].includes(location.pathname)) {
      setActiveTab('/desktop');
    } else if (['/', '/notification', '/my'].includes(location.pathname)) {
      setActiveTab(location.pathname);
    }
  }, [location.pathname, setActiveTab]);

  const renderItemTab = useCallback(() => {
    return tabs.map((item) => {
      return (
        <Link
          key={item.title}
          className={`${style.tab} ${activeTab === item.path ? style.active : null}`}
          to={item.path}
          onClick={(e) => changeActiveTab(e, item)}
        >
          <item.img className={`${style.tabIcon} ${activeTab === item.path ? style.activeTabIcon : null}`} />
          {item.title}
        </Link>
      );
    });
  }, [activeTab, changeActiveTab, tabs]);

  const height = useMemo(() => {
    const height = popupList.length * rowHeight;
    return height < 260 ? height : 260;
  }, [popupList.length]);

  const popClickItem = useCallback(
    (data: TabModel) => {
      if ('type' in data) {
        setActiveTab('/desktop');
        if (data.type) {
          history.push(data.path);
        } else {
          history.back();
        }
      } else {
        changeTabs(otherTabs[TabTypeEnum.SPACE]);
        setPopActiveItem(data.path);
        history.push(data.path);
      }
      setVisible(false);
    },
    [changeTabs, otherTabs, setActiveTab],
  );
  const goVisible = useCallback((bol: boolean) => {
    setVisible(bol);
  }, []);
  return (
    <div className={style.footer}>
      {renderItemTab()}
      <FilePopup setVisible={setVisible} visible={visible}>
        <div className={style.popupList} style={{ height: `${height}px` }}>
          <EnhancedFileList
            clickItem={popClickItem}
            isBottomPadding={false}
            list={popupList}
            rowHeight={rowHeight}
            selectVal={popActiveItem}
          />
        </div>
        <div className={`${itemStyle.popupItem} ${itemStyle.top}`}>
          <div className={itemStyle.popupItemCancel} onClick={() => goVisible(false)}>
            {text.cancel}
          </div>
        </div>
      </FilePopup>
    </div>
  );
};
