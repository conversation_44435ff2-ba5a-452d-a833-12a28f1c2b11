import { Ellipsis } from 'antd-mobile';
import { memo, useMemo } from 'react';

import spaceSvg from '@/assets/mobile/file/svg/space.svg';
import backDesktopSvg from '@/assets/mobile/footerTab/backDesktop.svg';
import { FileTypeImage } from '@/components-mobile/FileTypedimage';
import type { FileItemProps } from '@/model/FileList';

import style from '../Item/item.less';
export const ItemFolder = memo((prop: FileItemProps) => {
  const { data } = prop;
  const imgPros = useMemo(() => {
    if (data.guid === 'Desktop') {
      return {
        src: backDesktopSvg,
      };
    } else if (data.isSpaceFile) {
      return {
        src: spaceSvg,
      };
    } else {
      return {
        type: data.subType || data.type,
      };
    }
  }, [data.guid, data.subType, data.type, data.isSpaceFile]);
  return (
    <div className={style.item}>
      <FileTypeImage fit="cover" height={42} {...imgPros} width={42} />
      <div className={style.content}>
        <div className={style.contentLeft}>
          <div className={style.contentLeftName}>
            <Ellipsis content={data.name} direction="end" />
          </div>
        </div>
      </div>
    </div>
  );
});
