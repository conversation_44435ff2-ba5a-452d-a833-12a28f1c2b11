import { Ellipsis } from 'antd-mobile';
import { memo, useContext } from 'react';

import collectionSvg from '@/assets/mobile/file/svg/collection.svg';
import { FileTypeImage } from '@/components-mobile/FileTypedimage';
import { FileSettingContext } from '@/contexts/FileSetting';
import { useFileItem } from '@/hooks/useFile';
import type { FileItemProps } from '@/model/FileList';

import style from './item.less';

export const FileListItem = memo((prop: FileItemProps) => {
  const { updateActionProp, setOpenPopup } = useContext(FileSettingContext);

  const { data, isHideCollection, selectedType } = prop;
  const { timerText } = useFileItem(data, selectedType);

  const actionMore = (e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenPopup(true);
    updateActionProp(data, selectedType);
  };

  return (
    <div className={style.item}>
      <FileTypeImage fit="cover" height={42} type={data.isSpace ? 'space' : data.subType || data.type} width={42} />
      <div className={style.content}>
        <div className={style.contentLeft}>
          <div className={style.contentLeftName}>
            <Ellipsis content={data.name} direction="end" />
            {!isHideCollection && data.starred && <FileTypeImage fit="contain" src={collectionSvg} width={20} />}
          </div>
          <div className={style.contentLeftUpdatedAt}>
            <Ellipsis content={timerText} direction="end" />
          </div>
        </div>
        <div className={style.contentRight} onClick={(e) => actionMore(e)} />
      </div>
    </div>
  );
});
