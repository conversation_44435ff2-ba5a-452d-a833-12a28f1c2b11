.item {
  display: flex;
  width: calc(100% - 8px);
  height: 104px;
  padding: 10px 0 10px 8px;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  border-radius: 5px;
  border: 1px solid var(--theme-basic-color-lighter);
  content-visibility: auto;

  /* 一级投影 */
  box-shadow: 0 2px 4px 0 var(--theme-input-color-active-shadow);
}

.itemTop {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemTopLeft {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--theme-text-color-secondary);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.name {
  width: 100%;
  text-align: left;
  color: var(--theme-basic-color-primary);
  font-size: 14px;
  margin-top: 3px;
  padding-right: 6px;
}

.timeText {
  width: 100%;
  color: var(--theme-text-color-secondary);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 15px;
}

.itemTopRight {
  display: flex;
  align-items: center;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.noGuid {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  align-self: center;
  color: var(--theme-text-color-disabled);
}
