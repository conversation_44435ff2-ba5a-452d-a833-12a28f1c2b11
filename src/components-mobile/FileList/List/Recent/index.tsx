import { memo, useCallback, useMemo } from 'react';

import { useStickyHeaders } from '@/hooks/useStickyHeader';
import type { FileDataModel, FileItemProps, fileSortModel } from '@/model/FileList';
import { ModeTypeEnum } from '@/model/FileList';

import { FileSort } from '../../SortHeader';
import { FileCardItem } from '../Card';
import { FileListItem } from '../Item/item';
import style from './index.less';

type Props = {
  filesByDateObj: Record<string, FileDataModel[]>;
  fileSortProp: fileSortModel;
  clickItem: (file: FileDataModel) => void;
};

const FileItem = memo(
  ({
    file,
    ItemComponent,
    onClick,
  }: {
    file: FileDataModel;
    ItemComponent: React.ComponentType<FileItemProps>;
    onClick: () => void;
  }) => (
    <div className={style.fileItem} onClick={onClick}>
      <ItemComponent isFullScreenWidth isHideCollection data={file} />
    </div>
  ),
);

const DateGroup = memo(
  ({
    date,
    files,
    fileSortProp,
    itemComponent,
    clickItem,
  }: {
    date: string;
    files: FileDataModel[];
    fileSortProp: fileSortModel;
    itemComponent: Record<string, React.ComponentType<FileItemProps>>;
    clickItem: (file: FileDataModel) => void;
  }) => {
    const ItemComponent = itemComponent[fileSortProp.sortData.layout!];

    return (
      <div className={style.listItem}>
        <FileSort {...fileSortProp} isHideLayout>
          <span className={style.date}>{date}</span>
        </FileSort>
        <div className={`${fileSortProp.sortData.layout === ModeTypeEnum.card ? style.files : null}`}>
          {files.map((file) => (
            <FileItem key={file.guid} file={file} ItemComponent={ItemComponent} onClick={() => clickItem(file)} />
          ))}
        </div>
      </div>
    );
  },
);

export const RecentList = memo(({ filesByDateObj, fileSortProp, clickItem }: Props) => {
  const { containerRef, isSticky, stickyHeader } = useStickyHeaders({
    headerSelector: '.headerSelector',
    parentSelector: '.parentSelector',
    offset: 40,
  });

  // 缓存组件映射
  const itemComponent = useMemo(
    () => ({
      [ModeTypeEnum.list]: FileListItem,
      [ModeTypeEnum.card]: FileCardItem,
    }),
    [],
  );

  // 缓存日期列表
  const dateKeys = useMemo(
    () => Object.keys(filesByDateObj).filter((date) => filesByDateObj[date].length > 0),
    [filesByDateObj],
  );

  const renderItem = useCallback(() => {
    return dateKeys.map((date) => (
      <DateGroup
        key={date}
        clickItem={clickItem}
        date={date}
        files={filesByDateObj[date]}
        fileSortProp={fileSortProp}
        itemComponent={itemComponent}
      />
    ));
  }, [dateKeys, filesByDateObj, fileSortProp, itemComponent, clickItem]);

  const renderStickyHeader = useCallback(() => {
    if (!isSticky) return null;

    return (
      <div className={style.fixed}>
        <FileSort {...fileSortProp}>
          <span className={style.date}>{stickyHeader}</span>
        </FileSort>
      </div>
    );
  }, [isSticky, stickyHeader, fileSortProp]);

  return (
    <div ref={containerRef} className={`${style.recentList} parentSelector`}>
      {renderStickyHeader()}
      {renderItem()}
    </div>
  );
});
