.recentList {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-bottom: 120px;
}

.listItem {
  width: 100%;
}

.fileItem {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fixed {
  width: 100%;
  position: fixed;
  padding: 0 15px 0 0;
  height: auth;
  box-sizing: border-box;
  top: 40px;
  z-index: 9;
}

.files {
  max-width: 100%;
  display: grid;
  grid-gap: 16px;
  padding: 0 8px;
  grid-template-columns: repeat(auto-fill, minmax(135px, 1fr));
  grid-auto-rows: max-content;
}

.date {
  color: var(--theme-basic-color-black);
  font-size: 13px;
  height: 36px;
  line-height: 36px;
  padding-left: 8px;
}
