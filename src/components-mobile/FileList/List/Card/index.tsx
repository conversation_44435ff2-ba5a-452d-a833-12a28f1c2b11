import { Ellipsis } from 'antd-mobile';
import { memo, useContext } from 'react';

import collectionSvg from '@/assets/mobile/file/svg/collection.svg';
import MoreSvg from '@/assets/mobile/file/svg/more.svg';
import { FileTypeImage } from '@/components-mobile/FileTypedimage';
import { FileSettingContext } from '@/contexts/FileSetting';
import { useFileItem } from '@/hooks/useFile';
import type { FileItemProps } from '@/model/FileList';

import style from './index.less';
export const FileCardItem = memo((prop: FileItemProps) => {
  const { data, isHideCollection, selectedType } = prop;
  const { timerText } = useFileItem(data, selectedType);
  const { updateActionProp, setOpenPopup } = useContext(FileSettingContext);
  const actionMore = (e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenPopup(true);
    updateActionProp(data, selectedType);
  };
  return (
    <div className={style.item} style={{ width: prop.isFullScreenWidth ? '100%' : 'calc(100% - 16px)' }}>
      <div className={style.itemTop}>
        <div className={style.itemTopLeft}>
          <Ellipsis content={timerText} direction="end" />
          {!isHideCollection && data.starred && <FileTypeImage fit="contain" src={collectionSvg} width={20} />}
        </div>
        <div className={style.itemTopRight} onClick={(e) => actionMore(e)}>
          <FileTypeImage fit="contain" src={MoreSvg} style={{ marginRight: '-9px' }} width={20} />
        </div>
      </div>
      <div className={style.name}>
        <Ellipsis content={data.name} direction="end" />
      </div>
      <div className={style.img}>
        <FileTypeImage fit="contain" height={84} type={data.isSpace ? 'space' : data.subType || data.type} width={84} />
      </div>
    </div>
  );
});
