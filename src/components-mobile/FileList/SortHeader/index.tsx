import { Button } from 'antd-mobile';
import { memo, useCallback, useState } from 'react';

import { ReactComponent as ArrowDown } from '@/assets/mobile/file/svg/arrow-down.svg';
import CardSvg from '@/assets/mobile/file/svg/card.svg';
import ListSvg from '@/assets/mobile/file/svg/list.svg';
import type { SortModel } from '@/model/FileList';
import { fm } from '@/modules/Locale';

import { FilePopup } from '../Popup';
import { Sort } from '../Sort';
import style from './index.less';
type Props = {
  sortData: Partial<SortModel>;
  changeMode: () => void;
  upDateSortData?: (data: Partial<SortModel>) => void;
  children?: React.ReactNode;
  isHideLayout?: boolean;
};
export const FileSort = memo(({ isHideLayout, children, sortData, changeMode, upDateSortData, ...reset }: Props) => {
  const [visible, setVisible] = useState(false);
  const openSortDialog = useCallback(() => {
    setVisible(true);
  }, []);
  const changeSortData = useCallback(
    (data: Partial<SortModel>) => {
      upDateSortData?.(data);
      setVisible(false);
    },
    [upDateSortData],
  );
  const sortDataName = sortData.key ? fm(sortData.key) : '';
  return (
    <div className={`${style.header} headerSelector`} style={{ padding: children ? '0' : '0 5px 0 16px' }}>
      {children ? (
        children
      ) : (
        <Button className={style.headerText} fill="none" size="mini" onClick={openSortDialog}>
          {sortDataName}
          <ArrowDown style={{ marginLeft: '5px' }} />
        </Button>
      )}
      {!isHideLayout && (
        <div
          className={style.modeType}
          style={{
            background: `url(${sortData.layout ? CardSvg : ListSvg}) center center / contain`,
          }}
          onClick={changeMode}
        />
      )}

      <FilePopup setVisible={setVisible} visible={visible}>
        <Sort changeSortData={changeSortData} setVisible={setVisible} sortData={sortData} {...reset} />
      </FilePopup>
    </div>
  );
});
