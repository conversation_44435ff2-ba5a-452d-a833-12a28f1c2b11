import { NavBar, Space } from 'antd-mobile';
import { type ReactNode, useContext } from 'react';

import FindSvg from '@/assets/mobile/file/svg/find.svg';
import HeaderMoreSvg from '@/assets/mobile/file/svg/header-more.svg';
import LeftSvg from '@/assets/mobile/file/svg/left.svg';
import { FileSettingContext } from '@/contexts/FileSetting';
import type { FileDataModel } from '@/model/FileList';

import { FileTypeImage } from '../../FileTypedimage';
import { useSearch } from './hooks/useSearch';
import style from './index.less';

type Props = {
  defaultRight?: ReactNode;
  back?: () => void;
  data?: Partial<FileDataModel> | null;
  guid?: string;
  children?: ReactNode;
};

export const FileHeader = ({ defaultRight, back, data, guid, children }: Props) => {
  const { handleSearch } = useSearch();
  const { updateActionProp, setOpenPopup } = useContext(FileSettingContext);
  const actionMore = (event: React.MouseEvent<HTMLImageElement, Event>) => {
    event.stopPropagation();
    setOpenPopup(true);
    updateActionProp(data as FileDataModel);
  };
  const right = (
    <div style={{ fontSize: 24 }}>
      <Space className={style.spaceGap}>
        <FileTypeImage fit="contain" src={FindSvg} width={21} onClick={() => handleSearch()} />
        {guid && <FileTypeImage fit="contain" src={HeaderMoreSvg} width={24} onClick={actionMore} />}
      </Space>
    </div>
  );
  return (
    <div className={style.header}>
      {guid ? (
        <NavBar
          backIcon={<FileTypeImage fit="contain" src={LeftSvg} width={24} />}
          right={defaultRight ?? right}
          onBack={back}
        >
          {data?.name}
        </NavBar>
      ) : (
        <NavBar back={children} backIcon={false} right={defaultRight ?? right} />
      )}
    </div>
  );
};
