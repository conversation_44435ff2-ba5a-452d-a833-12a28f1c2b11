import { Radio } from 'antd-mobile';
import classNames from 'classnames';

import type { PermissionType } from '@/components/Collaboration/components';
import { OptionPermission, OptionsLinkExpiry } from '@/components/Collaboration/components';
import { fm } from '@/modules/Locale';

import { ShareContentDefault, ShareContentMedium } from './ShareAction';
import styles from './ShareAction.less';
import { CustomSwitch, SmallRadio } from './ShareControllActions';

export function ShareByLinkSharingSwitch({
  checked,
  onChange,
}: {
  checked: boolean;
  onChange: (open: boolean) => void;
}) {
  const i18n = {
    linkShareEnabled: fm('ShareCollaborationMobile.linkShareEnabled'),
    linkShareEnabledDescription: fm('ShareCollaborationMobile.linkShareEnabledDescription'),
    inviteByLink: fm('ShareCollaborationMobile.inviteByLink'),
    inviteByLinkDesc: fm('ShareCollaborationMobile.inviteByLinkDesc'),
  };

  function handleClick(value: boolean) {
    onChange(value);
  }

  return (
    <div className={styles.shareAction}>
      {checked ? (
        <ShareContentMedium title={i18n.linkShareEnabled}>{i18n.linkShareEnabledDescription}</ShareContentMedium>
      ) : (
        <ShareContentDefault title={i18n.inviteByLink}>{i18n.inviteByLinkDesc}</ShareContentDefault>
      )}
      <CustomSwitch checked={checked} onChange={handleClick} />
    </div>
  );
}

export function ShareByLinkExpirySettings({
  expiresAt,
  handleChange,
}: {
  expiresAt: number;
  handleChange: (value: number) => void;
}) {
  const linkExpiryPeriod = fm('ShareCollaborationMobile.linkExpiryPeriod');

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title={linkExpiryPeriod} />
      </div>
      <div className={styles.linkExpirySettingsExtra}>
        <Radio.Group value={expiresAt} onChange={(e) => handleChange(e as number)}>
          {OptionsLinkExpiry().map((item) => (
            <SmallRadio key={item.value} label={item.label} value={item.value} />
          ))}
        </Radio.Group>
      </div>
    </div>
  );
}

export function LinkPermissionSettings({
  role,
  handleChange,
}: {
  role: PermissionType;
  handleChange: (value: PermissionType) => void;
}) {
  const linkPermission = fm('ShareCollaborationMobile.linkPermission');

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title={linkPermission} />
      </div>
      <div className={styles.linkExpirySettingsExtra}>
        <Radio.Group value={role} onChange={(e) => handleChange(e as PermissionType)}>
          {OptionPermission().map((item) => (
            <SmallRadio key={item.value} label={item.label} value={item.value} />
          ))}
        </Radio.Group>
      </div>
    </div>
  );
}
