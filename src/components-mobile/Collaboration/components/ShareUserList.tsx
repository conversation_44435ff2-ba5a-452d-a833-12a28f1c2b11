import { Avatar } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationList } from '@/api/Collaboration';
import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { ReactComponent as Organization } from '@/assets/mobile/file/svg/organizations.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm, fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { AddCollaborator } from './AddCollaborator';
import { CollaboratorManagement } from './CollaboratorManagement';
import { CollaboratorManagementPopup } from './CollaboratorManagementPopup';
import { ParentDirectoryCollaboratorList } from './ParentDirectoryCollaboratorList';
import styles from './ShareUserList.less';

function AvatarList({ avatars }: { avatars: CollaboratorsData['roles'] }) {
  return (
    <div className={styles['shareUserList-avatars']}>
      {avatars.slice(0, 5).map((item) => {
        if (item.departmentId) {
          return <Organization key={item.id} height={24} width={24} />;
        }
        return <Avatar key={item.id} src={item.avatar} />;
      })}
    </div>
  );
}

export const getPopInfo = () =>
  ({
    index: {
      title: fm2('ShareCollaboration.collaborator'),
      type: 'index',
    },
    parent: {
      title: fm2('ShareCollaborationMobile.parentDirectoryCollaborator'),
      type: 'parent',
    },
    add: {
      title: fm2('ShareCollaboration.collaborator'),
      type: 'add',
    },
  }) as const;

export type CloseInfo = {
  type: keyof ReturnType<typeof getPopInfo>;
  extra?: string;
};

export function ShareUserList({
  actionRef,
  toggleParentVisible,
}: {
  toggleParentVisible: () => void;
  actionRef: React.RefObject<HTMLDivElement>;
}) {
  const i18n = {
    noRoles: fm('ShareCollaboration.noRoles'),
    addRoles: fm('ShareCollaboration.addRoles'),
  };

  const { collaborators: data, setCollaborators: setData, collaborationData } = useCollaborationStore((state) => state);

  const { isOpen, open, close } = useDisclosure(false);
  const [popInfo, setPopInfo] = useState<ReturnType<typeof getPopInfo>[keyof ReturnType<typeof getPopInfo>]>(
    getPopInfo().index,
  );
  const [parentId, setParentId] = useState<string>();

  function handleClose({ type, extra }: CloseInfo) {
    setPopInfo(getPopInfo()[type]);
    if (extra) {
      setParentId(extra);
    }
  }

  function handlePopClose() {
    // 当前关闭的是index，直接关闭整个popup,否则打开对应的popup
    if (popInfo.type === getPopInfo().index.type) {
      close();
      toggleParentVisible();
    } else {
      setPopInfo(getPopInfo().index);
    }
  }

  useEffect(() => {
    if (collaborationData?.guid && popInfo.type === getPopInfo().index.type) {
      getCollaborationList(collaborationData.guid, { includeInherited: false, includeAdmin: true }).then((res) => {
        setData(res.data);
      });
    }
  }, [collaborationData?.guid, popInfo.type, setData]);

  const hasCollaborators = useMemo(() => data?.roles && data.roles.length > 0, [data]);

  function handleOpen() {
    toggleParentVisible();
    open();
  }

  return (
    <>
      <div className={styles.shareUserList} onClick={handleOpen}>
        <div className={styles['shareUserList-content']}>
          {hasCollaborators ? (
            <AvatarList avatars={data!.roles} />
          ) : (
            <div className={styles['shareUserList-empty']}>
              <NoDataIcon className={styles['shareUserList-empty-icon']} />
              {i18n.noRoles}
            </div>
          )}
        </div>
        <div className={styles['shareUserList-action']}>
          {i18n.addRoles}
          <ArrowRight />
        </div>
      </div>
      <CollaboratorManagementPopup actionRef={actionRef} isOpen={isOpen} title={popInfo.title} onClose={handlePopClose}>
        {popInfo.type === getPopInfo().index.type && <CollaboratorManagement data={data} showOtherPop={handleClose} />}
        {popInfo.type === getPopInfo().parent.type && <ParentDirectoryCollaboratorList guid={parentId!} />}
        {popInfo.type === getPopInfo().add.type && <AddCollaborator />}
      </CollaboratorManagementPopup>
    </>
  );
}
