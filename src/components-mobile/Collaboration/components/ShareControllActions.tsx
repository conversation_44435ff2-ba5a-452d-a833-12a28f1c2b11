import { message } from 'antd';
import type { SwitchProps } from 'antd-mobile';
import { Radio, Switch } from 'antd-mobile';
import type { RadioValue } from 'antd-mobile/es/components/radio';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as RadioIcon } from '@/assets/images/svg/Radio.svg';
import { ReactComponent as RadioSelected } from '@/assets/images/svg/RadioSelected.svg';
import { ReactComponent as Refresh } from '@/assets/images/svg/refresh-no-color.svg';
import { OptionsDays } from '@/components/Collaboration/components';
import { CloseList, OpenList } from '@/components/Collaboration/components/SelectOptions';
import { updateShareExpire, updateSharePasswordAndGet } from '@/components/Collaboration/utils';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { ShareContentDefault, ShareContentMedium } from './ShareAction';
import styles from './ShareAction.less';
import { ShareSettingsActionSheet } from './ShareSettingsActionSheet';

export function CustomSwitch(props: SwitchProps) {
  return (
    <Switch
      {...props}
      style={{
        '--checked-color': 'var(--theme-link-button-color)',
        '--height': '24px',
        '--width': '42px',
      }}
    />
  );
}

export function LinkSharingSwitch({ checked, onChange }: { checked: boolean; onChange: (open: boolean) => void }) {
  const i18n = {
    notSupportShare: fm('ShareCollaboration.notSupportShare'),
    linkShareEnabled: fm('ShareCollaborationMobile.linkShareEnabled'),
    linkShareDisabled: fm('ShareCollaborationMobile.linkShareDisabled'),
    linkShareDisabledDescription: fm('ShareCollaborationMobile.linkShareDisabledDescription'),
    noPermissionBySettings: fm('ShareCollaboration.noPermissionText'),
  };
  const { share } = useCollaborationStore((state) => state);
  const disabled = share?.shareDisabled;
  const isSpecicalType = share?.isSpecicalType;

  function handleClick(value: boolean) {
    if (isSpecicalType) {
      message.error({ content: i18n.notSupportShare });
      return;
    }
    if (disabled) {
      message.error({ content: i18n.noPermissionBySettings });
      return;
    }
    onChange(value);
  }

  return (
    <div className={styles.shareAction}>
      {checked ? (
        <ShareContentMedium title={i18n.linkShareEnabled} />
      ) : (
        <ShareContentDefault title={i18n.linkShareDisabled}>{i18n.linkShareDisabledDescription}</ShareContentDefault>
      )}
      <CustomSwitch checked={checked} className={disabled ? styles.shareActionDisable : ''} onChange={handleClick} />
    </div>
  );
}

export function ShareSettings({ toggleParentVisible }: { toggleParentVisible: () => void }) {
  const { isOpen, open, close } = useDisclosure(false);
  const { share, collaborationData } = useCollaborationStore((state) => state);
  const disabled = share?.shareDisabled;

  const { highlightText, text } = useMemo(() => {
    const list = share?.passwordStatus ? OpenList : CloseList;

    return list().find((item) => item.value === collaborationData?.shareMode) || list()[0];
  }, [collaborationData?.shareMode, share?.passwordStatus]);

  function handleClick() {
    if (disabled) {
      return;
    }
    open();
    toggleParentVisible();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        <ShareContentMedium title={text()}>{highlightText}</ShareContentMedium>
        <ArrowRight className={disabled ? styles.shareActionDisable : ''} />
      </div>
      <ShareSettingsActionSheet visible={isOpen} onClose={handleClose} />
    </>
  );
}

export function SmallRadio({ value, label }: { value: RadioValue; label: string }) {
  return (
    <Radio
      className={styles.smallRadio}
      icon={(checked) => (checked ? <RadioSelected /> : <RadioIcon />)}
      style={{
        '--icon-size': '12px',
        '--font-size': '12px',
        '--gap': '9px',
      }}
      value={value}
    >
      {label}
    </Radio>
  );
}

export function LinkExpirySettings() {
  const { share, guid, collaborationData } = useCollaborationStore((state) => state);

  const [checked, setChecked] = useState(share?.shareTimeStatus);
  const [value, setValue] = useState(30);

  const i18n = {
    linkExpiration: fm('ShareCollaboration.linkExpiration'),
    expirationClose: fm('ShareCollaboration.expirationClose'),
  };

  function handleSwitchChange(checked: boolean) {
    updateShareExpire(guid, checked ? 30 : 0);
    setChecked(checked);
  }

  function handleChange(value: RadioValue) {
    updateShareExpire(guid, value as number);
    setValue(value as number);
  }

  useEffect(() => {
    setChecked(share?.shareTimeStatus);
    const shareModeExpireDuration = collaborationData?.shareModeExpireDuration;
    const expireDays = shareModeExpireDuration ? Math.floor(shareModeExpireDuration / 86400) : undefined;

    setValue(expireDays || 30);
  }, [share?.shareTimeStatus, collaborationData?.shareModeExpireDuration]);

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title={i18n.linkExpiration}>{checked ? '' : i18n.expirationClose}</ShareContentMedium>
        <CustomSwitch checked={checked} disabled={share?.shareDisabled} onChange={handleSwitchChange} />
      </div>
      {checked && (
        <div className={styles.linkExpirySettingsExtra}>
          <Radio.Group disabled={share?.shareDisabled} value={value} onChange={handleChange}>
            {OptionsDays().map((item) => (
              <SmallRadio key={item.value} label={item.label} value={item.value} />
            ))}
          </Radio.Group>
        </div>
      )}
    </div>
  );
}

export function LinkPasswordSettings() {
  const { share, guid, setShare } = useCollaborationStore((state) => state);

  const i18n = {
    linkPassword: fm('ShareCollaboration.linkPassword'),
    password: fm('ShareCollaborationMobile.password'),
    changePassword: fm('ShareCollaboration.changePassword'),
  };

  async function handleChangePassword() {
    if (!share?.shareDisabled) {
      const newPassword = await updateSharePasswordAndGet(guid, true, true);
      if (newPassword !== null) {
        setShare({
          ...share,
          password: newPassword,
          passwordStatus: true,
        });
      }
    }
  }

  async function handleSwitchChange(checked: boolean) {
    if (!share?.shareDisabled) {
      const newPassword = await updateSharePasswordAndGet(guid, checked, false);
      setShare({
        ...share,
        password: newPassword || '',
        passwordStatus: checked,
      });
    }
  }

  return (
    <div className={styles.linkExpirySettings}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title={i18n.linkPassword} />
        <CustomSwitch checked={share?.passwordStatus} disabled={share?.shareDisabled} onChange={handleSwitchChange} />
      </div>
      {share?.passwordStatus && (
        <div className={styles.linkExpirySettingsExtra}>
          <div className={styles.linkPassword}>
            {i18n.password}: {share?.password}
          </div>
          <div className={styles.changePassword} onClick={handleChangePassword}>
            <Refresh className={styles.refreshIcon} />
            {i18n.changePassword}
          </div>
        </div>
      )}
    </div>
  );
}
