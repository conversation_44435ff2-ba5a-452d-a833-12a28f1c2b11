import { ActionSheet } from 'antd-mobile';
import { useCallback, useMemo } from 'react';

import { fm } from '@/modules/Locale';

import styles from '../CollaborationShareMobile.less';
import { useInviteCode } from '../hooks/useInviteCode';
import { useToast } from '../hooks/useToast';
import { handleCopyLinkShare } from '../utils/mobile';
import { LinkPermissionSettings, ShareByLinkExpirySettings, ShareByLinkSharingSwitch } from './ShareByLinkActions';
import { CopyInviteLink } from './SimpleButton';

interface IProps {
  visible: boolean;
  onClose: () => void;
  guid: string;
}

export function ShareByLink({ visible, guid, onClose }: IProps) {
  const closeText = fm('ShareCollaborationMobile.close');

  const { showError, showLoading } = useToast();
  const { checked, handleCopyLink, toggleShare, expiresAt, role } = useInviteCode(guid, handleCopyLinkShare);

  const handleChange = useCallback(
    (checked: boolean) => {
      const close = showLoading();

      toggleShare(checked).catch((err) => {
        if (err) {
          showError(err);
        } else {
          close();
        }
      });
    },
    [showError, showLoading, toggleShare],
  );

  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: <ShareByLinkSharingSwitch checked={checked} onChange={handleChange} />,
      },
      ...(checked
        ? [
            {
              key: 'linkExpirySettings',
              text: <ShareByLinkExpirySettings {...expiresAt} />,
            },
            {
              key: 'linkPermissionSettings',
              text: <LinkPermissionSettings {...role} />,
            },
          ]
        : []),
      {
        key: 'copyLink',
        text: <CopyInviteLink disabled={!checked} onClick={handleCopyLink} />,
      },
    ];
  }, [checked, handleChange, expiresAt, role, handleCopyLink]);

  return (
    <ActionSheet
      actions={actions}
      cancelText={closeText}
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
