import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import type { ShareMode } from '@/components/Collaboration/components/SelectOptions';
import { CloseList, OpenList } from '@/components/Collaboration/components/SelectOptions';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import styles from './ShareAction.less';
import { ShareControll } from './ShareControll';

type ShareContentProps = {
  title: string;
  children?: React.ReactNode;
  className?: string;
};

export function ShareContentDefault({ title, children, className }: ShareContentProps) {
  return (
    <div className={classNames(styles.shareActionContent, className)}>
      <div className={styles.shareActionContentTitleDefault}>{title}</div>
      <div className={styles.shareActionContentText}>{children}</div>
    </div>
  );
}

export function ShareContentMedium({ title, children, className }: ShareContentProps) {
  return (
    <div className={classNames(styles.shareActionContent, className)}>
      <div className={styles.shareActionTitle}>{title}</div>
      {children && <div className={styles.shareActionContentText}>{children}</div>}
    </div>
  );
}
export function ShareSettings({
  title,
  children,
  highlightText,
  disable,
  onClick,
}: {
  title: string;
  highlightText: string;
  children?: React.ReactNode;
  disable?: boolean;
  onClick?: () => void;
}) {
  return (
    <div
      className={classNames(styles.shareSettings, {
        [styles.shareSettingsDisable]: disable,
      })}
      onClick={onClick}
    >
      <div className={styles.shareSettingsContent}>
        <div className={styles.shareSettingsTitle}>{title}</div>
        <span className={styles.shareSettingsTextSpan}>{highlightText}</span>
      </div>
      {children}
    </div>
  );
}

function ShareOpenContent({ passwordStatus, shareMode }: { passwordStatus?: boolean; shareMode?: ShareMode }) {
  const linkShareEnabled = fm('ShareCollaborationMobile.linkShareEnabled');

  const { highlightText, text } = useMemo(() => {
    const list = passwordStatus ? OpenList : CloseList;

    return list().find((item) => item.value === shareMode) || list()[0];
  }, [passwordStatus, shareMode]);

  return (
    <ShareContentMedium title={linkShareEnabled}>
      <span>
        {text()}
        <span className={styles.shareActionTextSpan}>{highlightText}</span>
      </span>
    </ShareContentMedium>
  );
}

type ShareActionProps = {
  toggleParentVisible: () => void;
};

export function ShareAction({ toggleParentVisible }: ShareActionProps) {
  const { guid, share, collaborationData } = useCollaborationStore((state) => state);

  const { isOpen, open, close, toggle } = useDisclosure(false);
  const [checked, setChecked] = useState(share?.shareStatus || false);
  const i18n = {
    linkShareDisabled: fm('ShareCollaborationMobile.linkShareDisabled'),
    linkShareDisabledDescription: fm('ShareCollaborationMobile.linkShareDisabledDescription'),
  };

  function handleClick() {
    toggleParentVisible();
    open();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  useEffect(() => {
    if (share?.shareStatus !== undefined) {
      setChecked(share.shareStatus);
    }
  }, [share?.shareStatus]);

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        {checked ? (
          <ShareOpenContent passwordStatus={share?.passwordStatus} shareMode={collaborationData?.shareMode} />
        ) : (
          <ShareContentMedium title={i18n.linkShareDisabled}>{i18n.linkShareDisabledDescription}</ShareContentMedium>
        )}
        <ArrowRight />
      </div>
      <ShareControll
        guid={guid}
        open={checked}
        setOpen={setChecked}
        toggle={toggle}
        visible={isOpen}
        onClose={handleClose}
      />
    </>
  );
}
