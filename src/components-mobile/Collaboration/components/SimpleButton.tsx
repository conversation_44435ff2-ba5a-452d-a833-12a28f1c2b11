import { Button } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { fm } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { copyLink, handleCopyLinkShare } from '../utils/mobile';
import styles from './ShareAction.less';

export function SimpleButton({
  onClick,
  children,
  disabled,
  ...rest
}: { onClick?: () => void; children: React.ReactNode; disabled?: boolean } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={styles.simple} {...rest}>
      <Button className={styles.simpleButton} disabled={disabled} fill="none" onClick={onClick}>
        {children}
      </Button>
    </div>
  );
}

export function CopyInviteLink({ onClick, disabled }: { onClick?: () => void; disabled?: boolean }) {
  const copyText = fm('ShareCollaborationMobile.copyLink');

  function handleClick() {
    onClick?.();
  }

  return (
    <SimpleButton disabled={disabled} onClick={handleClick}>
      <div className={styles.copyLink}>{copyText}</div>
    </SimpleButton>
  );
}

export function CopyLink({ onClick, disabled }: { onClick?: () => void; disabled?: boolean }) {
  const copyText = fm('ShareCollaborationMobile.copyLink');
  const accessPassword = fm('ShareCollaboration.accessPassword');
  const copyLinkAndPassword = fm('ShareCollaborationMobile.copyLinkAndPassword');

  const { collaborationData: data, share } = useCollaborationStore((state) => state);
  const [text, setText] = useState(copyText);

  function handleClick() {
    if (data?.url && data?.name) {
      const url = copyLink({ url: data.url, name: data.name });
      const copyUrl = share?.passwordStatus ? `${url} ${accessPassword} ${share?.password}` : url;
      handleCopyLinkShare(copyUrl);
      onClick?.();
    }
  }

  useEffect(() => {
    if (share?.passwordStatus) {
      setText(copyLinkAndPassword);
    } else {
      setText(copyText);
    }
  }, [copyLinkAndPassword, copyText, share?.passwordStatus]);

  return (
    <SimpleButton disabled={disabled} onClick={handleClick}>
      {text}
    </SimpleButton>
  );
}

export function CloseAction({
  onClick,
  title,
  ...rest
}: {
  onClick: () => void;
  title?: string;
} & React.HTMLAttributes<HTMLDivElement>) {
  const defaultTitle = fm('ShareCollaborationMobile.close');

  return (
    <SimpleButton onClick={onClick} {...rest}>
      {title || defaultTitle}
    </SimpleButton>
  );
}
