import { ActionSheet } from 'antd-mobile';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getLocale } from 'umi';

import { getPermissions, usePermissions } from '@/components/Collaboration/hooks/usePermission';
import type { CollaborationData } from '@/components/Collaboration/types';
import { getCollaborationDataInfo } from '@/components/Collaboration/utils';
import { fm } from '@/modules/Locale';

import styles from './CollaborationShareMobile.less';
import { ShareAction } from './components/ShareAction';
import { ShareUserList } from './components/ShareUserList';
import { CopyLink } from './components/SimpleButton';
import { useCollaborationStore } from './store/CollaboratorManagement';

interface CollaborationShareMobileProps {
  guid: string;
  visible: boolean;
  close: () => void;
  toggle: () => void;
}

export function CollaborationShareMobile({ guid, visible, toggle, close }: CollaborationShareMobileProps) {
  const closeText = fm('ShareCollaborationMobile.close');
  const storedLocale = getLocale();

  const [id, setId] = useState<string | null>(guid);
  const [data, setData] = useState<CollaborationData>();
  const actionRef = useRef<HTMLDivElement>(null);
  const { setCollaborationData, setGuid, setShare, collaborationId } = useCollaborationStore((state) => state);
  const { roleCheckable } = usePermissions(data);

  const handleClose = useCallback(() => {
    close();
    setId(null);
  }, [close]);

  const actions = useMemo(() => {
    const premissionAction = [
      {
        key: 'userList',
        text: <ShareUserList actionRef={actionRef} toggleParentVisible={toggle} />,
      },
      {
        key: 'shareAction',
        text: <ShareAction toggleParentVisible={toggle} />,
      },
    ];

    const premission = roleCheckable ? premissionAction : [];

    return [
      ...premission,
      {
        key: 'copyLink',
        text: <CopyLink onClick={handleClose} />,
      },
    ];
  }, [toggle, roleCheckable, handleClose]);

  useEffect(() => {
    if (id) {
      getCollaborationDataInfo(id).then((res) => {
        if (res) {
          setGuid(res.data.guid!);

          const info = getPermissions(res.data);
          setData(res.data);
          setCollaborationData(res.data);
          setShare({
            ...info,
            copyUrl: res.copyUrl,
          });
        }
      });
    }
  }, [id, setCollaborationData, setGuid, setShare, storedLocale]);

  useEffect(() => {
    if (visible) {
      setId(guid);
    }
  }, [guid, visible]);

  return (
    <div key={collaborationId} ref={actionRef}>
      <ActionSheet
        actions={actions}
        cancelText={closeText}
        className={styles.collaborationShareMobile}
        visible={visible}
        onClose={handleClose}
      />
    </div>
  );
}
