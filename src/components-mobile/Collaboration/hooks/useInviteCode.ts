import { useCallback, useEffect, useState } from 'react';

import { createInviteCode, deleteInviteCode, getInviteCode } from '@/api/Collaboration';
import type { ResponseError } from '@/types/api';

import { useLinkExpiry } from './useLinkExpiry';
import { useLoading } from './useLoading';
import { useRole } from './useRole';

const getUrl = (code: string) => {
  return `${new URL('file-invite', window.location.origin).href}/${code}`;
};

export function useInviteCode(guid: string, copyLink: (url: string) => void) {
  const { setError, isLoading, setIsLoading } = useLoading();
  const { setExpiresAt, ...expiresAt } = useLinkExpiry(guid, setError);
  const { setRole, ...role } = useRole(guid, setError);

  const [checked, setChecked] = useState(false);
  const [url, setUrl] = useState(getUrl('invite_code'));

  const getInviteCodeInfo = useCallback(async () => {
    const res = await getInviteCode(guid);
    if (res.data && Object.keys(res.data).length) {
      setExpiresAt(res.data.validDuration);
      setChecked(!!res.data.code);
      setRole(res.data.role);
      const url = getUrl(res.data.code);
      setUrl(url);
    } else {
      setChecked(false);
      setUrl(getUrl('invite_code'));
    }
  }, [guid, setExpiresAt, setRole]);

  const handleCopyLink = useCallback(() => {
    copyLink(url);
  }, [copyLink, url]);

  const toggleShare = useCallback(
    async (flag: boolean) => {
      setIsLoading(true);
      try {
        if (flag) {
          const res = await createInviteCode(guid, {
            expiresIn: expiresAt.expiresAt,
            role: role.role,
          });
          setExpiresAt(res.data.validDuration);
          setRole(res.data.role);
          setUrl(getUrl(res.data.code));
        } else {
          await deleteInviteCode(guid);
          setUrl(getUrl('invite_code'));
          setExpiresAt(7 * 86400);
          setRole('editor');
        }
        setIsLoading(false);
        setChecked(flag);
      } catch (error) {
        setError((error as ResponseError).data.msg);
        setIsLoading(false);
        throw (error as ResponseError).data.msg;
      }
    },
    [expiresAt.expiresAt, guid, role.role, setError, setExpiresAt, setIsLoading, setRole],
  );

  useEffect(() => {
    if (guid) {
      getInviteCodeInfo();
    }
  }, [getInviteCodeInfo, guid]);

  return {
    checked,
    handleCopyLink,
    toggleShare,
    expiresAt,
    role,
    url,
    isLoading,
  };
}
