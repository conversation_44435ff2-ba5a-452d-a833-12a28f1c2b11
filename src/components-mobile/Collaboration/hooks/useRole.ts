import { useCallback, useState } from 'react';

import { updateInviteCode } from '@/api/Collaboration';
import { catchApiResult } from '@/api/Request';
import { OptionPermission } from '@/components/Collaboration/components';

export function useRole(guid: string, setError: (error: string) => void) {
  const [role, setRole] = useState(OptionPermission()[2].value);

  const handleChange = useCallback(
    async (value: string) => {
      const [err] = await catchApiResult(updateInviteCode(guid, { role: value }));
      if (err) {
        setError(err.data.msg);
        return;
      }
      setRole(value);
    },
    [guid, setError],
  );

  return {
    role,
    setRole,
    handleChange,
  };
}
