import { Toast } from 'antd-mobile';
import { useCallback } from 'react';

import { useFormatMessage } from '@/modules/Locale';

export function useToast() {
  const setAdminSuccessText = useFormatMessage('ShareCollaborationMobile.setAdminSuccess');

  const showLoading = useCallback(() => {
    const { close } = Toast.show({ icon: 'loading', content: 'loading' });

    return close;
  }, []);

  const showSuccess = useCallback(() => {
    Toast.show({ icon: 'success', content: setAdminSuccessText });
  }, [setAdminSuccessText]);

  const showError = useCallback((msg: string) => {
    Toast.show({ icon: 'fail', content: msg });
  }, []);

  return {
    showLoading,
    showSuccess,
    showError,
  };
}
