import { useCallback, useState } from 'react';

import { updateInviteCode } from '@/api/Collaboration';
import { catchApiResult } from '@/api/Request';

export function useLinkExpiry(guid: string, setError: (error: string) => void) {
  const [expiresAt, setExpiresAt] = useState(7 * 86400);

  const handleChange = useCallback(
    async (value: number) => {
      const [err] = await catchApiResult(updateInviteCode(guid, { expiresIn: value }));
      if (err) {
        setError(err.data.msg);
        return;
      }
      setExpiresAt(value);
    },
    [guid, setError],
  );

  return {
    expiresAt,
    setExpiresAt,
    handleChange,
  };
}
