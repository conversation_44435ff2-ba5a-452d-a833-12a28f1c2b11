import { CloseOutlined } from '@ant-design/icons';
import { ActionSheet, Dialog, Toast } from 'antd-mobile';
import type { ToastHandler } from 'antd-mobile/es/components/toast';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { ReactComponent as Check<PERSON>ircleIcon } from '@/assets/images/svg/check-circle.svg';
import { ReactComponent as UploadErrorIcon } from '@/assets/images/upload/mobile/upload-error.svg';
import { ReactComponent as UploadProgressIcon } from '@/assets/images/upload/mobile/upload-progress.svg';
import { FileSettingContext } from '@/contexts/FileSetting';
import type { FileUploadResult } from '@/hooks/useFileUpload';
import useFileUpload from '@/hooks/useFileUpload';
import { fm, fm2 } from '@/modules/Locale';
import type { StatusType, UploadType } from '@/store/Upload';
import { uploadStore, useUploadStore } from '@/store/Upload';
import { openFile } from '@/utils/file';

import styles from './index.less';

interface ToastContentProps {
  close: () => void;
  status: StatusType;
  fileName: string;
  type: UploadType;
  errorMsg?: string;
  jump: () => void;
}

function ToastContent({ errorMsg, type, status, close, fileName, jump }: ToastContentProps) {
  const { setIsShowUploadList } = useUploadStore((state) => state);

  const content = (() => {
    if (errorMsg) {
      return `${fm2('UseFileUploadMobile.importFailed')}，${errorMsg}`;
    }
    switch (status) {
      case 'fail':
        return type === 'all'
          ? fm2('UseFileUploadMobile.uploadFailedClickView', { fileName })
          : fm2('UseFileUploadMobile.importFailedClickView', { fileName });
      case 'uploading':
      case 'progress':
      case 'waiting':
        return type === 'all' ? fm2('UseFileUploadMobile.uploading') : fm2('UseFileUploadMobile.importing');
      default:
        return type === 'all'
          ? fm2('UseFileUploadMobile.uploadSuccessClickViewFile', { fileName })
          : fm2('UseFileUploadMobile.importSuccessClickView', { fileName });
    }
  })();

  function handleJump() {
    close();
    if (status === 'success') {
      jump();
    } else {
      setIsShowUploadList(true);
    }
  }

  function handleClose(e: React.MouseEvent<HTMLDivElement>) {
    e.stopPropagation();
    close();
  }

  return (
    <div className={styles.toastContent} onClick={handleJump}>
      <div className={styles.checkIcon}>
        {status === 'fail' ? (
          <UploadErrorIcon />
        ) : status === 'uploading' ? (
          <UploadProgressIcon />
        ) : (
          <CheckCircleIcon />
        )}
      </div>
      <div className={styles.toastText}>{content}</div>
      <div className={styles.closeIcon} onClick={handleClose}>
        <CloseOutlined height={12} width={12} />
      </div>
    </div>
  );
}

export type ToastInfo = {
  name: string;
  status: StatusType;
  fileName?: string;
  errorMsg?: string;
};

// Toast管理类
class ToastManager {
  static show(
    isShowUploadList: boolean,
    toastClose: React.MutableRefObject<ToastHandler | undefined>,
    info: ToastInfo,
    type: UploadType,
    actionsRef: React.MutableRefObject<HTMLDivElement | null>,
    jump: () => void,
  ) {
    // 在文件上传面板，不提示
    if (isShowUploadList) {
      return;
    }

    toastClose.current = Toast.show({
      content: (
        <ToastContent
          close={() => toastClose.current?.close()}
          errorMsg={info.errorMsg}
          fileName={info.fileName ?? info.name}
          jump={jump}
          status={info.status}
          type={type}
        />
      ),
      duration: 0,
      position: 'top',
      maskClassName: styles.toastMask,
      getContainer: actionsRef.current,
    });
  }
}

export function FileUpload({ parentGuid }: { parentGuid: string }) {
  const i18nText = {
    noSpaceTitle: fm('UseFileUpload.noSpaceTitle'),
    noSpaceContent: fm('UseFileUpload.noSpaceContent'),
    actionTitle: fm('UseFileUploadMobile.actionTitle'),
    selectFromManager: fm('UseFileUploadMobile.selectFromManager'),
    importFile: fm('UseFileUploadMobile.importFile'),
    cancel: fm('UseFileUploadMobile.cancel'),
  };
  const {
    currentUploadList,
    uploadList,
    h5ActionShow: visible,
    setH5ActionShow: setVisible,
  } = useUploadStore((state) => state);
  const toastClose = useRef<ToastHandler>();
  const { goRefreshFileList, mainDivRef } = useContext(FileSettingContext);

  const [type, setType] = useState<UploadType>('all');

  // 更新文件状态
  const updateFileStatus = (generateTempId: string | number, status: StatusType) => {
    const { uploadList, setUploadList } = uploadStore.getState();
    const updatedList = uploadList.map((item) => {
      if (item.generateTempId === generateTempId) {
        return { ...item, status };
      }
      return item;
    });
    setUploadList(updatedList);
  };

  const handleJump = (fileResult: FileUploadResult) => {
    openFile({
      url: fileResult.fileDetail?.url || '',
      type: fileResult.fileDetail?.type || '',
      guid: fileResult.fileDetail?.guid || '',
    });
  };

  // 处理文件导入
  const handleFileImport = async (fileResult: FileUploadResult) => {
    const { isShowUploadList } = uploadStore.getState();

    if (fileResult.status === 'fail') {
      // 出错时设置为失败状态
      updateFileStatus(fileResult.generateTempId, 'fail');
      ToastManager.show(
        isShowUploadList,
        toastClose,
        {
          name: fileResult.file?.name || '',
          status: fileResult.status,
          fileName: fileResult.fileDetail?.name,
          errorMsg: fileResult.message,
        },
        type,
        mainDivRef!,
        () => {
          handleJump(fileResult);
        },
      );
    } else {
      // 根据结果更新状态
      updateFileStatus(fileResult.generateTempId, fileResult.status);
      ToastManager.show(
        isShowUploadList,
        toastClose,
        {
          name: fileResult.file?.name || '',
          status: fileResult.status,
          fileName: fileResult.fileDetail?.name,
          errorMsg: fileResult.message,
        },
        type,
        mainDivRef!,
        () => {
          handleJump(fileResult);
        },
      );
    }
  };

  // 处理文件上传
  const handleFileUpload = (fileResult: FileUploadResult) => {
    const { isShowUploadList } = uploadStore.getState();

    ToastManager.show(
      isShowUploadList,
      toastClose,
      {
        name: fileResult.file?.name || '',
        status: fileResult.status,
        fileName: fileResult.file?.name,
      },
      type,
      mainDivRef!,
      () => {
        handleJump(fileResult);
      },
    );
  };

  const handleRefresh = useCallback(() => {
    // 在本次上传的文件中找到当前文件的状态， 上传成功，刷新文件列表.
    const successList = currentUploadList.map((item) =>
      uploadList.find((it) => it.generateTempId === item.generateTempId),
    );
    const item = successList.find((item) => item?.status === 'success');
    if (item) {
      goRefreshFileList();
    }
  }, [currentUploadList, uploadList, goRefreshFileList]);

  const { importFile } = useFileUpload({
    parentGuid,
    onProgress: (fileResult) => {
      if (type === 'import') {
        handleFileImport(fileResult);
      } else {
        handleFileUpload(fileResult);
      }
    },
  });

  const handleClick = useCallback(
    (t: UploadType) => {
      setVisible(false);
      setType(t);
      importFile(t, () => {
        Dialog.alert({
          title: i18nText.noSpaceTitle,
          content: i18nText.noSpaceContent,
          bodyClassName: styles.dialogBody,
        });
      });
    },
    [i18nText.noSpaceContent, i18nText.noSpaceTitle, importFile, setVisible],
  );

  const actions = useMemo(
    () => [
      {
        key: 'title',
        text: <span className={styles.actionTitle}>{i18nText.actionTitle}</span>,
      },
      {
        key: 'upload',
        text: (
          <span className={styles.actionBtn} onClick={() => handleClick('all')}>
            {i18nText.selectFromManager}
          </span>
        ),
      },
      {
        key: 'import',
        text: (
          <span className={styles.actionBtn} onClick={() => handleClick('import')}>
            {i18nText.importFile}
          </span>
        ),
      },
      {
        key: 'close',
        text: (
          <span className={styles.actionBtn} onClick={() => setVisible(false)}>
            {i18nText.cancel}
          </span>
        ),
      },
    ],
    [handleClick, i18nText.actionTitle, i18nText.cancel, i18nText.importFile, i18nText.selectFromManager, setVisible],
  );

  useEffect(() => {
    if (currentUploadList.length) {
      handleRefresh();
    }
  }, [handleRefresh, currentUploadList]);

  return (
    <div>
      <ActionSheet
        actions={actions}
        className={styles.actionSheet}
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </div>
  );
}
